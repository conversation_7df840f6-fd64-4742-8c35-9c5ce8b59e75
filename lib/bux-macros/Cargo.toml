[package]
name = "bux-macros"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
proc-macro = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
bux-compiler = { path = "../bux-compiler" }

proc-macro2 = { workspace = true }
proc-macro2-diagnostics = { workspace = true }
quote = { workspace = true }
syn = { workspace = true, features = ["full"] }
