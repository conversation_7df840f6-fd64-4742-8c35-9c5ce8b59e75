[package]
name = "approck-ace"
version = "0.1.0"
edition = "2021"

[package.metadata.acp]
module = {}

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
serde = { workspace = true, features = ["derive"] }
reqwest = { workspace = true, features = ["json"] }
serde_json = { workspace = true }
tokio = { workspace = true, features = ["full"] }
rustls = { workspace = true }
rustls-pki-types = { workspace = true }