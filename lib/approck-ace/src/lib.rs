// -------------------------------------------------------------------------------------------------
// App and Identity Traits

pub trait App: approck::App {
    fn ace_system(&self) -> &ModuleStruct;
}

pub trait Identity: approck::Identity {}


// -------------------------------------------------------------------------------------------------
// Module Definition

#[derive(serde::Deserialize, Debug)]
pub struct ModuleConfig {
    pub server: Option<String>,
    pub app: Option<String>,
    pub auth: Option<ModuleConfigAuth>,
}

#[derive(serde::Deserialize, Debug)]
pub enum ModuleConfigAuth {
    #[serde(rename = "app-production")]
    AppProduction {
        secret: String,
    },
    #[serde(rename = "app-preview")]
    AppPreview {
        secret: String,
    },
    #[serde(rename = "developer")]
    Developer {
        name: String,
        secret: String,
    },
}


#[allow(dead_code)]
pub struct ModuleStruct {
    ace_server: Option<String>,
    app_name: Option<String>,
    auth: Option<ModuleStructAuth>,
    client: reqwest::Client,
}

pub enum ModuleStructAuth {
    AppProduction {
        secret: String,
    },
    AppPreview {
        secret: String,
    },
    Developer {
        name: String,
        secret: String,
    },
}


// -------------------------------------------------------------------------------------------------
// Module Implementation

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;

    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            ace_server: config.server,
            app_name: config.app,
            auth: match config.auth {
                Some(ModuleConfigAuth::AppProduction { secret }) => {
                    Some(ModuleStructAuth::AppProduction { secret })
                }
                Some(ModuleConfigAuth::AppPreview { secret }) => {
                    Some(ModuleStructAuth::AppPreview { secret })
                }
                Some(ModuleConfigAuth::Developer { name, secret }) => {
                    Some(ModuleStructAuth::Developer { name, secret })
                }
                None => None,
            },
            client: reqwest::Client::new(),
        })
    }

    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

impl ModuleStruct {
    pub async fn get_certificate(
        &self,
        _app_name: &str,
        _hostname: &str,
    ) -> granite::Result<approck::server::CertificateRequestState> {
        // TODO: Implement the logic to get the certificate from the ace server
        unimplemented!()        
    }

    pub async fn get_config_json(
        &self,
        _app_name: &str,
    ) -> granite::Result<serde_json::Value> {
        // TODO: Implement the logic to get the config from the ace server
        unimplemented!()
    }
}
