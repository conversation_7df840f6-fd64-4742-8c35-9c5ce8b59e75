pub mod derive_gadt;
pub mod gen_rs;
pub mod gen_ts;
pub mod macros;
pub mod parser;

use granite_core::Decimal;
use proc_macro2::TokenStream;
use std::collections::HashSet;
use syn::{Ident, Visibility};

#[derive(Debug, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct TSImport {
    pub is_type: bool,
    pub ident: String,
    pub from_path: String,
}

#[derive(Clone, Debug, PartialEq)]
pub struct MacroOption {
    pub rs_type: bool,
    pub rs_type_validate: bool,
    pub rs_type_encode: bool,
    pub rs_type_decode: bool,
    pub rs_partial: bool,
    pub rs_partial_validate: bool,
    pub rs_partial_encode: bool,
    pub rs_partial_decode: bool,
    pub rs_error: bool,
    pub rs_error_encode: bool,
    pub rs_error_decode: bool,
    pub rs_debug: bool,
    pub rs_clone: bool,
    pub rs_partial_eq: bool,

    pub ts_type: bool,
    pub ts_type_validate: bool,
    pub ts_type_encode: bool,
    pub ts_type_decode: bool,
    pub ts_partial: bool,
    pub ts_partial_validate: bool,
    pub ts_partial_encode: bool,
    pub ts_partial_decode: bool,
    pub ts_error: bool,
    pub ts_error_encode: bool,
    pub ts_error_decode: bool,
    pub ts_from: Option<String>,
}

#[derive(Debug, Clone, PartialEq)]
pub struct CodeGenOption {
    pub rs_type: bool,
    pub rs_validate: bool,
    pub rs_encode: bool,
    pub rs_decode: bool,
    pub rs_debug: bool,
    pub rs_clone: bool,
    pub rs_partial_eq: bool,

    pub ts_type: bool,
    pub ts_validate: bool,
    pub ts_encode: bool,
    pub ts_decode: bool,
}

pub struct GMacroInputBuilder {
    pub macro_option: MacroOption,
    pub vis: Visibility,
    pub ident: Ident,
    pub attrs: Vec<syn::Attribute>,
    pub g_inner: GInner,
    pub mod_ident: Option<Ident>,
}

pub struct GMacroInput {
    pub macro_option: MacroOption,
    pub mod_ident: Option<Ident>,
    pub gadt_type: GADT,
    pub gadt_partial: GADT,
    pub gadt_error: GADT,
}

/// Represents a single gtype algebraic data type, which can be a `struct`, `enum` or `type`.
/// Common attributes are stored in this struct, along with a `ginner` field which contains the actual type information.
pub struct GADT {
    pub code_gen: CodeGenOption,
    pub vis: syn::Visibility,
    pub attrs: Vec<syn::Attribute>,
    pub g_inner: GInner,
    pub g_style: GStyle,
    pub mod_ident: Option<syn::Ident>,
}

impl GADT {
    pub fn ident(&self) -> &Ident {
        match &self.g_style {
            GStyle::Type { type_ident, .. } => type_ident,
            GStyle::Partial { partial_ident, .. } => partial_ident,
            GStyle::Error { error_ident } => error_ident,
        }
    }
}

pub enum GStyle {
    Type {
        type_ident: Ident,
        error_ident: Ident,
    },
    Partial {
        type_ident: Ident,
        partial_ident: Ident,
        error_ident: Ident,
    },
    Error {
        error_ident: Ident,
    },
}

#[derive(Clone)]
pub enum GInner {
    Struct(GInnerStruct),
    Enum(GInnerEnum),
    Type(GInnerType),
}

#[derive(Clone)]
pub enum GInnerStruct {
    Unit,
    Tuple(Vec<(Visibility, GType)>),
    Named(Vec<(Visibility, Ident, GType)>),
}

#[derive(Clone)]
pub struct GInnerEnum(pub Vec<(Ident, GVariant)>);

#[derive(Clone)]
pub struct GInnerType {
    /// the type itself
    pub type_path: syn::TypePath,
    pub import_from: Option<String>,
}

#[derive(Clone)]
pub enum GVariant {
    Unit,
    Tuple(Vec<(GType,)>),
    Struct(Vec<(Ident, GType)>),
}

#[derive(Debug, Clone, PartialEq)]
pub enum GType {
    // builtin for compiler support
    Undefinable(Box<GType>),
    NestedError(Box<GType>),
    Undefined,

    // user definable
    GType(syn::Ident),
    Scalar(GScalar),
    Option {
        zero_to_none: bool,
        some_type: Box<GType>,
    },
    Result(Box<GType>, Box<GType>),
    Vec(Box<GType>),
    HashSet(GScalar),
    HashMap {
        key_type: GScalar,
        value_type: Box<GType>,
        max_items: Option<usize>,
    },
    BTreeMap(GScalar, Box<GType>),
    IndexMap(GScalar, Box<GType>),
    Range(GScalar),
    JsonValue,
    JsonObject,
    JsonArray,
}

#[derive(Debug, PartialEq, Clone)]
#[allow(non_camel_case_types)]
pub enum GScalar {
    char,
    bool,
    i8 {
        default: Option<i8>,
        min: Option<i8>,
        max: Option<i8>,
    },
    u8 {
        default: Option<u8>,
        min: Option<u8>,
        max: Option<u8>,
    },
    i16 {
        default: Option<i16>,
        min: Option<i16>,
        max: Option<i16>,
    },
    u16 {
        default: Option<u16>,
        min: Option<u16>,
        max: Option<u16>,
    },
    i32 {
        default: Option<i32>,
        min: Option<i32>,
        max: Option<i32>,
    },
    u32 {
        default: Option<u32>,
        min: Option<u32>,
        max: Option<u32>,
    },
    i64 {
        default: Option<i64>,
        min: Option<i64>,
        max: Option<i64>,
    },
    u64 {
        default: Option<u64>,
        min: Option<u64>,
        max: Option<u64>,
    },
    f32 {
        default: Option<f32>,
        min: Option<f32>,
        max: Option<f32>,
    },
    f64 {
        default: Option<f64>,
        min: Option<f64>,
        max: Option<f64>,
    },
    String {
        default: Option<String>,
        max: Option<usize>,
        min: Option<usize>,
        empty_to_none: Option<bool>,
        no_empty: Option<bool>,
        trim: Option<Trim>,
    },
    Uuid {
        no_empty: Option<bool>,
        version: Option<usize>,
    },
    Decimal {
        default: Option<Decimal>,
        min: Option<Decimal>,
        max: Option<Decimal>,
    },
    Integer,
    BigInt,
    DateUtc,
    DateTz,
    DateTimeUtc,
    DateTimeTz,
    Time,
    Duration,
    IpAddr,
}

#[derive(Clone, Debug, PartialEq)]
pub enum Trim {
    Start,
    End,
    Both,
}

#[derive(Debug)]
pub enum GeneratedFile {
    TS(GeneratedFileTS),
}

#[derive(Debug)]
pub struct GeneratedFileTS {
    pub rel_path: String,
    pub imports: HashSet<TSImport>,
    pub chunks: Vec<GeneratedChunk>,
}

#[derive(Debug)]
pub struct GeneratedChunk {
    pub mod_ident: Option<syn::Ident>,
    pub imports: HashSet<TSImport>,
    pub items: Vec<ChunkItem>,
}

#[derive(Debug)]
pub enum ChunkItem {
    Code(TokenStream),
    Note(String),
}

impl GInnerType {
    pub fn ident(&self) -> &Ident {
        &self.type_path.path.segments.last().unwrap().ident
    }

    pub fn path(&self) -> syn::TypePath {
        self.type_path.clone()
    }
}

impl TSImport {
    pub fn granite_type(ident: &str) -> TSImport {
        TSImport {
            is_type: true,
            ident: ident.to_string(),
            from_path: "@granite/lib.mts".to_string(),
        }
    }

    pub fn granite_item(ident: &str) -> TSImport {
        TSImport {
            is_type: false,
            ident: ident.to_string(),
            from_path: "@granite/lib.mts".to_string(),
        }
    }

    pub fn other_type(ident: &str, from: &str) -> TSImport {
        TSImport {
            is_type: true,
            ident: ident.to_string(),
            from_path: from.to_string(),
        }
    }

    pub fn other_item(ident: &str, from: &str) -> TSImport {
        TSImport {
            is_type: false,
            ident: ident.to_string(),
            from_path: from.to_string(),
        }
    }
}

impl Default for GeneratedChunk {
    fn default() -> Self {
        Self::new(None)
    }
}

impl GeneratedChunk {
    pub fn new(mod_ident: Option<syn::Ident>) -> Self {
        Self {
            mod_ident,
            imports: HashSet::new(),
            items: Vec::new(),
        }
    }

    pub fn import(&mut self, import: TSImport) {
        self.imports.insert(import);
    }

    pub fn code(&mut self, code: TokenStream) {
        self.items.push(ChunkItem::Code(code));
    }

    pub fn note<S: Into<String>>(&mut self, comment: S) {
        self.items.push(ChunkItem::Note(comment.into()));
    }

    pub fn extend(&mut self, other: GeneratedChunk) {
        self.imports.extend(other.imports);
        self.items.extend(other.items);
    }
}

#[allow(dead_code)]
trait IdentExt {
    fn to_validate(&self) -> syn::Ident;
    fn to_encode(&self) -> syn::Ident;
    fn to_decode(&self) -> syn::Ident;
    fn to_map_key(&self) -> String;
    fn to_key_map_ident(&self) -> syn::Ident;
}

impl IdentExt for syn::Ident {
    fn to_validate(&self) -> syn::Ident {
        quote::format_ident!("{}_validate", self)
    }
    fn to_encode(&self) -> syn::Ident {
        quote::format_ident!("{}_encode", self)
    }
    fn to_decode(&self) -> syn::Ident {
        quote::format_ident!("{}_decode", self)
    }
    /// Rust idents are converted to_string() with `r#` prefix in some cases, which needs removed
    fn to_map_key(&self) -> String {
        let s = self.to_string();
        if let Some(s) = s.strip_prefix("r#") {
            s.to_string()
        } else {
            s
        }
    }
    fn to_key_map_ident(&self) -> syn::Ident {
        quote::format_ident!("{}_map", self)
    }
}
