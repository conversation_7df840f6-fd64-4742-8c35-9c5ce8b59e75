use crate::ident_str;

use super::{
    GADT, GInner, GInnerEnum, GInnerStruct, GInnerType, GMacroInput, GScalar, GStyle, GType,
    GVariant, GeneratedChunk, IdentExt, TSImport,
};
use std::collections::{HashMap, HashSet};

use proc_macro2::TokenStream;
use quote::{ToTokens, format_ident, quote};

impl GeneratedChunk {
    fn import_type(&mut self, get_imports: &impl Ext) {
        get_imports.import_type(&mut self.imports);
    }
    fn import_validate(&mut self, get_imports: &impl Ext) {
        get_imports.import_validate(&mut self.imports);
    }
    fn import_encode(&mut self, get_imports: &impl Ext) {
        get_imports.import_encode(&mut self.imports);
    }
    fn import_decode(&mut self, get_imports: &impl Ext) {
        get_imports.import_decode(&mut self.imports);
    }
}

pub fn ts_import_set_to_token_stream(imports: &HashSet<TSImport>) -> TokenStream {
    // map the imports into a hashmap of files to import vecs
    let mut file_to_import_map: HashMap<&String, Vec<&TSImport>> = HashMap::new();
    for import in imports {
        file_to_import_map
            .entry(&import.from_path)
            .or_default()
            .push(import);
    }

    let mut file_and_import_vec = file_to_import_map.into_iter().collect::<Vec<_>>();
    file_and_import_vec.sort();
    for (_from_path, imports) in file_and_import_vec.iter_mut() {
        imports.sort();
    }

    let import_lines = file_and_import_vec.into_iter().map(|(from_path, imports)| {
        let items = imports.into_iter().map(|import| {
            let ident = ident_str!(import.ident);
            if import.is_type {
                quote! {
                    type #ident
                }
            } else {
                quote! {
                    #ident
                }
            }
        });

        quote! {
            import { #(#items),* } from #from_path;
        }
    });

    quote! {
        #(#import_lines)*
    }
}

/// Returns the generated typescript code for the given GADT struct or enum along with requested TSI and TSO code
pub fn g_macro_input_to_generated_chunk(
    _source: Option<String>,
    g_macro_input: GMacroInput,
) -> GeneratedChunk {
    let mut generated_chunk = GeneratedChunk::new(g_macro_input.mod_ident.clone());

    for gadt in [
        &g_macro_input.gadt_type,
        &g_macro_input.gadt_partial,
        &g_macro_input.gadt_error,
    ] {
        if gadt.code_gen.ts_type {
            derive_type(gadt, &mut generated_chunk);
        }
        if gadt.code_gen.ts_validate {
            derive_validate(gadt, &mut generated_chunk);
        }
        if gadt.code_gen.ts_encode {
            dervive_encode(gadt, &mut generated_chunk);
        }
        if gadt.code_gen.ts_decode {
            derive_decode(gadt, &mut generated_chunk);
        }
    }

    generated_chunk
}

fn derive_type(gadt: &GADT, output: &mut GeneratedChunk) {
    let type_exp = gadt.ident();

    match &gadt.g_inner {
        // UNIT STRUCT
        GInner::Struct(GInnerStruct::Unit) => {
            output.note(format!("derive_type for struct (unit): {type_exp}"));
            output.code(quote! {
                export type #type_exp = true;
            });
        }
        // TUPLE STRUCT
        GInner::Struct(GInnerStruct::Tuple(singles)) => {
            let mut field_tokens = Vec::with_capacity(singles.len());

            for (_vis, gtype) in singles {
                let optional_token = gtype.optional_token();
                let type_exp = gtype.type_exp();
                output.import_type(gtype);
                field_tokens.push(quote! {
                    #type_exp #optional_token,
                });
            }

            output.note(format!("derive_type for struct (tuple): {type_exp}"));
            output.code(quote! {
                export type #type_exp = [#(#field_tokens)*];
            });
        }
        // NAMED STRUCT
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut field_tokens = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, ident, gtype) in vis_ident_gtype_list {
                let optional_token = gtype.optional_token();
                let type_exp = gtype.type_exp();
                output.import_type(gtype);
                field_tokens.push(quote! {
                    #ident #optional_token : #type_exp;
                });
            }

            output.note(format!("derive_type for struct (named): {type_exp}"));

            // Handle empty structs with Record<string, never> to satisfy TypeScript linter
            if field_tokens.is_empty() {
                output.code(quote! {
                    export type #type_exp = Record<string, never>;
                });
            } else {
                output.code(quote! {
                    export type #type_exp = {
                        #(#field_tokens)*
                    };
                });
            }
        }
        // ENUM
        GInner::Enum(GInnerEnum(ident_gvariant_list)) => {
            let mut variant_tokens = Vec::with_capacity(ident_gvariant_list.len());
            let mut map_tokens = Vec::with_capacity(ident_gvariant_list.len());

            for (variant_ident, gvariant) in ident_gvariant_list {
                let variant_map_key = variant_ident.to_map_key();
                match gvariant {
                    GVariant::Unit => {
                        variant_tokens.push(quote! {
                            {#variant_map_key: true}
                        });
                        map_tokens.push(quote! {
                            #variant_map_key: {#variant_map_key: true},
                        });
                    }
                    GVariant::Tuple(singles) => {
                        let mut field_tokens = Vec::with_capacity(singles.len());
                        for (field_gtype,) in singles {
                            let optional_token = field_gtype.optional_token();
                            let type_exp = field_gtype.type_exp();
                            output.import_type(field_gtype);
                            field_tokens.push(quote! {
                                #type_exp #optional_token,
                            });
                        }
                        variant_tokens.push(quote! {
                            { #variant_ident: [#(#field_tokens)*]}
                        });
                    }
                    GVariant::Struct(doubles) => {
                        let mut field_tokens = Vec::with_capacity(doubles.len());
                        for (field_ident, field_gtype) in doubles {
                            let optional_token = field_gtype.optional_token();
                            let type_exp = field_gtype.type_exp();
                            output.import_type(field_gtype);
                            field_tokens.push(quote! {
                                #field_ident #optional_token : #type_exp ,
                            });
                        }
                        variant_tokens.push(quote! {
                            { #variant_ident: {#(#field_tokens)*} }
                        });
                    }
                }
            }

            output.note(format!("derive_type for enum: {type_exp}"));
            output.code(quote! {
                export type #type_exp = #(#variant_tokens) | *;
            });

            if matches!(gadt.g_style, GStyle::Type { .. } | GStyle::Partial { .. }) {
                let type_exp_map = gadt.ident().to_key_map_ident();
                output.note(format!("derive_type for enum map: {type_exp}"));
                output.code(quote! {
                    export const #type_exp_map: Record<string, #type_exp> = {
                        #(#map_tokens)*
                    };
                });
            }
        }
        GInner::Type(GInnerType { import_from, .. }) => match import_from {
            Some(import_from) => {
                output.import(TSImport::other_type(&gadt.ident().to_string(), import_from));
            }
            None => {
                // no need to do anything
            }
        },
    };
}

/// The purpose of a validate function is to upgrade a parital type to a full type or produce an error type
/// This can be invoked either on a partial type or a full type.
/// In either case, we need to know the ident of the full type.
fn derive_validate(gadt: &GADT, output: &mut GeneratedChunk) {
    let function_ident = gadt.ident().to_validate();

    let (src_type_exp, dst_type_exp, err_type_exp) = match &gadt.g_style {
        GStyle::Type {
            type_ident,
            error_ident,
        } => (type_ident, type_ident, error_ident),
        GStyle::Partial {
            type_ident,
            partial_ident,
            error_ident,
        } => (partial_ident, type_ident, error_ident),
        GStyle::Error { .. } => panic!("derive_validate for error type is not supported"),
    };

    match &gadt.g_inner {
        // UNIT STRUCT
        GInner::Struct(GInnerStruct::Unit) => {
            output.note(format!(
                "derive_validate for struct (unit): {}",
                gadt.ident()
            ));
            output.import(TSImport::granite_type("NestedError"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(value: #src_type_exp | undefined): Result<#dst_type_exp, NestedError<#err_type_exp>> {
                    if (value === undefined) {
                        return { Err: { Outer: "value is missing" } };
                    }

                    if (value !== true) {
                        return { Err: { Outer: "TypeError: expected true but got " + typeof value } };
                    }

                    return { Ok: true };
                };
            });
        }

        // TUPLE STRUCT (EMPTY)
        GInner::Struct(GInnerStruct::Tuple(singles)) if singles.is_empty() => {
            output.note(format!(
                "derive_validate for struct (tuple, empty): {}",
                gadt.ident()
            ));
            output.import(TSImport::granite_type("NestedError"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(value: #src_type_exp | undefined): Result<#dst_type_exp, NestedError<#err_type_exp>> {
                    if (value === undefined) {
                        return { Err: { Outer: "value is missing" } };
                    }

                    if (typeof value !== "object" || value === null || !Array.isArray(value)) {
                        return { Err: { Outer: "TypeError: expected array but got " + typeof value } };
                    }

                    return { Ok: [] };
                };
            });
        }

        // TUPLE STRUCT
        GInner::Struct(GInnerStruct::Tuple(singles)) => {
            // Handle the empty tuple struct case.
            if singles.is_empty() {
                output.code(quote! {
                    export function #function_ident(value: #src_type_exp | undefined): Result<#dst_type_exp, NestedError<#err_type_exp>> {
                        return { Ok: [] };
                    };
                });
                return;
            }

            let mut vec1 = Vec::with_capacity(singles.len());
            let mut vec2 = Vec::with_capacity(singles.len());
            let mut vec3 = Vec::with_capacity(singles.len());
            let mut vec4 = Vec::with_capacity(singles.len());

            for (index, (_vis, gtype)) in singles.iter().enumerate() {
                output.import_validate(gtype);
                let index_key = proc_macro2::Literal::usize_unsuffixed(index);
                let callable = gtype.callable_validate();
                let result_name = format_ident!("r_{}", index);

                vec1.push(quote! {
                    const #result_name = #callable(value[#index_key] ?? undefined);
                });
                vec2.push(quote! {
                    ("Ok" in #result_name)
                });
                vec3.push(quote! {
                    #result_name.Ok,
                });
                vec4.push(quote! {
                    ("Err" in #result_name ? #result_name.Err : undefined),
                });
            }

            output.note(format!(
                "derive_validate for struct (tuple): {}",
                gadt.ident()
            ));
            output.import(TSImport::granite_type("NestedError"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(value: #src_type_exp | undefined): Result<#dst_type_exp, NestedError<#err_type_exp>> {
                    if (value === undefined) {
                        return { Err: { Outer: "value is missing" } };
                    }

                    if (typeof value !== "object" || value === null || !Array.isArray(value)) {
                        return { Err: { Outer: "TypeError: expected array but got " + typeof value } };
                    }

                    #(#vec1)*
                    if (#(#vec2)&&*) {
                        return { Ok: [#(#vec3)*] };
                    }
                    else {
                        return { Err: { Outer: "validation error", Inner: [#(#vec4)*] } };
                    }
                };
            });
        }

        // NAMED STRUCT (EMPTY)
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list))
            if vis_ident_gtype_list.is_empty() =>
        {
            output.note(format!(
                "derive_validate for struct (named, empty): {}",
                gadt.ident()
            ));
            output.import(TSImport::granite_type("NestedError"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(value: #src_type_exp | undefined): Result<#dst_type_exp, NestedError<#err_type_exp>> {
                    if (value === undefined) {
                        return { Err: { Outer: "value is missing" } };
                    }

                    if (typeof value !== "object" || value === null || Array.isArray(value)) {
                        return { Err: { Outer: "TypeError: expected object but got " + typeof value } };
                    }

                    return { Ok: {} };
                };
            });
        }

        // NAMED STRUCT
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut vec1 = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut vec2 = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut vec3 = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut vec4 = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, ident, gtype) in vis_ident_gtype_list {
                output.import_validate(gtype);
                let map_key = ident.to_map_key();
                let callable = gtype.callable_validate();
                let result_name = format_ident!("r_{}", ident);

                vec1.push(quote! {
                    const #result_name = #callable(value[#map_key] ?? undefined);
                });
                vec2.push(quote! {
                    ("Ok" in #result_name)
                });
                vec3.push(quote! {
                    #map_key: #result_name.Ok,
                });
                vec4.push(quote! {
                    #map_key: ("Err" in #result_name ? #result_name.Err : undefined),
                });
            }

            output.note(format!(
                "derive_validate for struct (named): {}",
                gadt.ident()
            ));
            output.import(TSImport::granite_type("NestedError"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(value: #src_type_exp | undefined): Result<#dst_type_exp, NestedError<#err_type_exp>> {
                    if (value === undefined) {
                        return { Err: { Outer: "value is missing" } };
                    }

                    if (typeof value !== "object" || value === null || Array.isArray(value)) {
                        return { Err: { Outer: "TypeError: expected object but got " + typeof value } };
                    }

                    #(#vec1)*
                    if (#(#vec2)&&*) {
                        return { Ok: { #(#vec3)* } };
                    }
                    else {
                        return { Err: { Outer: "validation error", Inner: { #(#vec4)* } } };
                    }
                };
            });
        }

        // ENUM
        GInner::Enum(GInnerEnum(ident_gvariant_list)) => {
            let mut variant_tokens = Vec::with_capacity(ident_gvariant_list.len());
            for (variant_ident, gvariant) in ident_gvariant_list {
                let variant_map_key = variant_ident.to_map_key();
                match gvariant {
                    GVariant::Unit => {
                        variant_tokens.push(quote! {
                            if (#variant_map_key in value) {
                                return { Ok: {#variant_map_key: true} };
                            }
                        });
                    }
                    GVariant::Tuple(singles) if singles.is_empty() => {
                        variant_tokens.push(quote! {
                            if (#variant_map_key in value && Array.isArray(value[#variant_map_key])) {
                                return { Ok: { #variant_map_key: [] } };
                            }
                        });
                    }
                    GVariant::Tuple(singles) => {
                        let mut vec1 = Vec::with_capacity(singles.len());
                        let mut vec2 = Vec::with_capacity(singles.len());
                        let mut vec3 = Vec::with_capacity(singles.len());
                        let mut vec4 = Vec::with_capacity(singles.len());

                        for (index, (field_gtype,)) in singles.iter().enumerate() {
                            output.import_validate(field_gtype);
                            let index_key = proc_macro2::Literal::usize_unsuffixed(index);
                            let callable = field_gtype.callable_validate();
                            let result_name = format_ident!("r_{}", index);

                            vec1.push(quote! {
                                const #result_name = #callable(arr[#index_key] ?? undefined);
                            });
                            vec2.push(quote! {
                                ("Ok" in #result_name)
                            });
                            vec3.push(quote! {
                                #result_name.Ok,
                            });
                            vec4.push(quote! {
                                ("Err" in #result_name ? #result_name.Err : undefined),
                            });
                        }

                        variant_tokens.push(quote! {
                            if (#variant_map_key in value && Array.isArray(value[#variant_map_key])) {
                                const arr = value[#variant_map_key];
                                #(#vec1)*
                                if (#(#vec2)&&*) {
                                    return { Ok: { #variant_map_key: [#(#vec3)*] } };
                                }
                                else {
                                    return { Err: { Outer: "validation error", Inner: { #variant_map_key: [#(#vec4)*] } } };
                                }
                            }
                        });
                    }
                    GVariant::Struct(doubles) if doubles.is_empty() => {
                        variant_tokens.push(quote! {
                            if (#variant_map_key in value && typeof value[#variant_map_key] === "object" && value[#variant_map_key] !== null && !Array.isArray(value[#variant_map_key])) {
                                return { Ok: { #variant_map_key: {} } };
                            }
                        });
                    }
                    GVariant::Struct(doubles) => {
                        let mut vec1 = Vec::with_capacity(doubles.len());
                        let mut vec2 = Vec::with_capacity(doubles.len());
                        let mut vec3 = Vec::with_capacity(doubles.len());
                        let mut vec4 = Vec::with_capacity(doubles.len());

                        for (field_ident, field_gtype) in doubles {
                            output.import_validate(field_gtype);
                            let map_key = field_ident.to_map_key();
                            let callable = field_gtype.callable_validate();
                            let result_name = format_ident!("r_{}", field_ident);
                            vec1.push(quote! {
                                const #result_name = #callable(obj[#map_key] ?? undefined);
                            });
                            vec2.push(quote! {
                                ("Ok" in #result_name)
                            });
                            vec3.push(quote! {
                                #map_key: #result_name.Ok,
                            });
                            vec4.push(quote! {
                                #map_key: ("Err" in #result_name ? #result_name.Err : undefined),
                            });
                        }

                        variant_tokens.push(quote! {
                            if (#variant_map_key in value && typeof value[#variant_map_key] === "object" && value[#variant_map_key] !== null && !Array.isArray(value[#variant_map_key])) {
                                const obj = value[#variant_map_key];
                                #(#vec1)*
                                if (#(#vec2)&&*) {
                                    return { Ok: { #variant_map_key: { #(#vec3)* } } };
                                }
                                else {
                                    return { Err: { Outer: "validation error", Inner: { #variant_map_key: { #(#vec4)* } } } };
                                }
                            }
                        });
                    }
                }
            }

            output.note(format!("derive_validate for enum: {}", gadt.ident()));
            output.import(TSImport::granite_type("NestedError"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(value: #src_type_exp | undefined): Result<#dst_type_exp, NestedError<#err_type_exp>> {
                    if (value === undefined) {
                        return { Err: { Outer: "value is missing" } };
                    }

                    if (typeof value === "object" && value !== null && !Array.isArray(value)) {
                        #(#variant_tokens)*
                    }

                    return { Err: { Outer: "invalid variant" } };
                };
            });
        }
        GInner::Type(GInnerType { import_from, .. }) => {
            match import_from {
                Some(import_from) => {
                    output.import(TSImport::other_item(
                        &gadt.ident().to_validate().to_string(),
                        import_from,
                    ));
                }
                None => {
                    // no need to do anything
                }
            }
        }
    }
}

fn dervive_encode(gadt: &GADT, output: &mut GeneratedChunk) {
    let function_ident = gadt.ident().to_encode();
    let type_exp = gadt.ident().clone();

    match &gadt.g_inner {
        // UNIT STRUCT
        GInner::Struct(GInnerStruct::Unit) => {
            output.import(TSImport::granite_type("JsonValue"));
            output.note(format!("derive_encode for struct (unit): {type_exp}"));
            output.code(quote! {
                export function #function_ident(_value: #type_exp): JsonValue {
                    return true;
                };
            });
        }
        // TUPLE STRUCT
        GInner::Struct(GInnerStruct::Tuple(singles)) => {
            output.import(TSImport::granite_type("JsonValue"));
            let mut vec3 = Vec::with_capacity(singles.len());

            for (index, (_vis, gtype)) in singles.iter().enumerate() {
                output.import_encode(gtype);
                let index_key = proc_macro2::Literal::usize_unsuffixed(index);
                let callable = gtype.callable_encode();
                vec3.push(quote! {
                    #callable(_value[#index_key]),
                });
            }

            output.note(format!("derive_encode for struct (tuple): {type_exp}"));
            output.code(quote! {
                export function #function_ident(_value: #type_exp): JsonValue {
                    return [#(#vec3)*];
                };
            });
        }

        // NAMED STRUCT
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            output.import(TSImport::granite_type("JsonValue"));
            let mut vec3 = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, ident, gtype) in vis_ident_gtype_list {
                output.import_encode(gtype);
                let map_key = ident.to_map_key();
                let callable = gtype.callable_encode();
                vec3.push(quote! {
                    #ident: #callable(_value[#map_key]),
                });
            }

            output.note(format!("derive_encode for struct (named): {type_exp}"));
            output.code(quote! {
                export function #function_ident(_value: #type_exp): JsonValue {
                    return {
                        #(#vec3)*
                    };
                };
            });
        }
        // ENUM
        GInner::Enum(GInnerEnum(ident_gvariant_list)) => {
            output.import(TSImport::granite_type("JsonValue"));
            let mut variant_tokens = Vec::with_capacity(ident_gvariant_list.len());
            for (variant_ident, gvariant) in ident_gvariant_list {
                let variant_map_key = variant_ident.to_map_key();
                match gvariant {
                    // UNIT VARIANT
                    GVariant::Unit => {
                        variant_tokens.push(quote! {
                            if (#variant_map_key in data) {
                                return {#variant_map_key: true};
                            }
                        });
                    }
                    // TUPLE VARIANT
                    GVariant::Tuple(singles) => {
                        let mut vec3 = Vec::with_capacity(singles.len());
                        for (index, (field_gtype,)) in singles.iter().enumerate() {
                            output.import_encode(field_gtype);

                            let index_key = proc_macro2::Literal::usize_unsuffixed(index);
                            let callable = field_gtype.callable_encode();
                            vec3.push(quote! {
                                #callable(data.#variant_ident[#index_key]),
                            });
                        }
                        variant_tokens.push(quote! {
                            if (#variant_map_key in data) {
                                return  { #variant_map_key: [#(#vec3)*]};
                            }
                        });
                    }
                    // STRUCT VARIANT
                    GVariant::Struct(doubles) => {
                        let mut vec3 = Vec::with_capacity(doubles.len());
                        for (field_ident, field_gtype) in doubles {
                            output.import_encode(field_gtype);
                            let map_key = field_ident.to_map_key();
                            let callable = field_gtype.callable_encode();
                            vec3.push(quote! {
                                #field_ident: #callable(data.#variant_ident[#map_key]),
                            });
                        }
                        variant_tokens.push(quote! {
                            if (#variant_map_key in data) {
                                return { #variant_map_key: {#(#vec3)*} };
                            }
                        });
                    }
                }
            }

            output.note(format!("derive_encode for enum: {type_exp}"));
            output.code(quote! {
                export function #function_ident(data: #type_exp): JsonValue {
                    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
                        #(#variant_tokens)*
                    }

                    return null;
                };
            });
        }
        GInner::Type(GInnerType { import_from, .. }) => match import_from {
            Some(import_from) => {
                output.import(TSImport::other_item(
                    &gadt.ident().to_encode().to_string(),
                    import_from,
                ));
            }
            None => {
                // no need to do anything
            }
        },
    };
}

fn derive_decode(gadt: &GADT, output: &mut GeneratedChunk) {
    let function_ident = gadt.ident().to_decode();
    let type_exp = &gadt.ident();

    match &gadt.g_inner {
        // UNIT STRUCT
        GInner::Struct(GInnerStruct::Unit) => {
            output.note(format!("derive_decode for struct (unit): {type_exp}"));
            output.import(TSImport::granite_type("JsonValue"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(data: JsonValue): Result<#type_exp, string> {
                    if (data === true) {
                        return { Ok: data };
                    } else {
                        return { Err: "Unit: wrong type: " + typeof data };
                    }
                };
            });
        }
        // TUPLE STRUCT
        GInner::Struct(GInnerStruct::Tuple(singles)) => {
            let mut vec1 = Vec::with_capacity(singles.len());
            let mut vec3 = Vec::with_capacity(singles.len());

            for (index, (_vis, gtype)) in singles.iter().enumerate() {
                output.import_decode(gtype);
                let index_key = proc_macro2::Literal::usize_unsuffixed(index);
                let callable = gtype.callable_decode();
                let result_name = format_ident!("r_{}", index);

                // special handling of undefinable types
                if gtype.is_undefinable() {
                    vec1.push(quote! {
                        const #result_name = #callable(data[#index_key] ?? null);
                    });
                    vec3.push(quote! {
                        ("Ok" in #result_name ? #result_name.Ok : undefined),
                    });
                }
                // regular types should fail fast on decoding errors
                else {
                    vec1.push(quote! {
                        const #result_name = #callable(data[#index_key] ?? null);
                        if ("Err" in #result_name) {
                            return #result_name;
                        }
                    });
                    vec3.push(quote! {
                        #result_name.Ok,
                    });
                }
            }

            output.note(format!("derive_decode for struct (tuple): {type_exp}"));
            output.import(TSImport::granite_type("JsonValue"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(data: JsonValue): Result<#type_exp, string> {
                    if (Array.isArray(data)) {
                        #(#vec1)*
                        return { Ok: [#(#vec3)*] };
                    } else {
                        return { Err: "Tuple: wrong type: " + typeof data };
                    }
                };
            });
        }

        // NAMED STRUCT
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut vec1 = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut vec3 = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, ident, gtype) in vis_ident_gtype_list {
                output.import_decode(gtype);
                let map_key = ident.to_map_key();
                let callable = gtype.callable_decode();
                let result_name = format_ident!("r_{}", ident);

                // special handling of undefinable types
                if gtype.is_undefinable() {
                    vec1.push(quote! {
                        const #result_name = #callable(data[#map_key] ?? null);
                    });
                    vec3.push(quote! {
                        #map_key: ("Ok" in #result_name ? #result_name.Ok : undefined),
                    });
                }
                // regular types should fail fast on decoding errors
                else {
                    vec1.push(quote! {
                        const #result_name = #callable(data[#map_key] ?? null);
                        if ("Err" in #result_name) {
                            return #result_name;
                        }
                    });
                    vec3.push(quote! {
                        #map_key: #result_name.Ok,
                    });
                }
            }

            output.note(format!("derive_decode for struct (named): {type_exp}"));
            output.import(TSImport::granite_type("JsonValue"));
            output.import(TSImport::granite_type("Result"));

            // Handle empty structs explicitly
            if vec1.is_empty() {
                output.code(quote! {
                    export function #function_ident(data: JsonValue): Result<#type_exp, string> {
                        if (typeof data === "object" && data !== null && !Array.isArray(data)) {
                            return { Ok: {} };
                        }
                        else {
                            return { Err: "Named: wrong type: " + typeof data };
                        }
                    }
                });
            } else {
                output.code(quote! {
                    export function #function_ident(data: JsonValue): Result<#type_exp, string> {
                        if (typeof data === "object" && data !== null && !Array.isArray(data)) {
                            #(#vec1)*
                            return { Ok: { #(#vec3)* } };
                        } else {
                            return { Err: "Named: wrong type: " + typeof data };
                        }
                    };
                });
            }
        }

        // ENUM
        GInner::Enum(GInnerEnum(ident_gvariant_list)) => {
            let mut variant_tokens = Vec::with_capacity(ident_gvariant_list.len());
            for (variant_ident, gvariant) in ident_gvariant_list {
                let variant_map_key = variant_ident.to_map_key();
                match gvariant {
                    GVariant::Unit => {
                        variant_tokens.push(quote! {
                            if (#variant_map_key in data) {
                                return { Ok: {#variant_map_key: true} };
                            }
                        });
                    }
                    GVariant::Tuple(singles) => {
                        let mut vec1 = Vec::with_capacity(singles.len());
                        let mut vec3 = Vec::with_capacity(singles.len());

                        for (index, (field_gtype,)) in singles.iter().enumerate() {
                            output.import_decode(field_gtype);
                            let index_key = proc_macro2::Literal::usize_unsuffixed(index);
                            let callable = field_gtype.callable_decode();
                            let result_name = format_ident!("r_{}", index);

                            // special handling of undefinable types
                            if field_gtype.is_undefinable() {
                                vec1.push(quote! {
                                    const #result_name = #callable(arr[#index_key] ?? null);
                                });
                                vec3.push(quote! {
                                    ("Ok" in #result_name ? #result_name.Ok : undefined),
                                });
                            }
                            // regular types should fail fast on decoding errors
                            else {
                                vec1.push(quote! {
                                    const #result_name = #callable(arr[#index_key] ?? null);
                                    if ("Err" in #result_name) {
                                        return #result_name;
                                    }
                                });
                                vec3.push(quote! {
                                    #result_name.Ok,
                                });
                            }
                        }

                        variant_tokens.push(quote! {
                            if (#variant_map_key in data && Array.isArray(data[#variant_map_key])) {
                                const arr = data[#variant_map_key];
                                #(#vec1)*
                                return { Ok: { #variant_map_key: [#(#vec3)*] } };
                            }
                        });
                    }
                    GVariant::Struct(doubles) => {
                        let mut vec1 = Vec::with_capacity(doubles.len());
                        let mut vec3 = Vec::with_capacity(doubles.len());

                        for (field_ident, field_gtype) in doubles {
                            output.import_decode(field_gtype);
                            let map_key = field_ident.to_map_key();
                            let callable = field_gtype.callable_decode();
                            let result_name = format_ident!("r_{}", field_ident);

                            // special handling of undefinable types
                            if field_gtype.is_undefinable() {
                                vec1.push(quote! {
                                    const #result_name = #callable(obj[#map_key] ?? null);
                                });
                                vec3.push(quote! {
                                    #map_key: ("Ok" in #result_name ? #result_name.Ok : undefined),
                                });
                            }
                            // regular types should fail fast on decoding errors
                            else {
                                vec1.push(quote! {
                                    const #result_name = #callable(obj[#map_key] ?? null);
                                    if ("Err" in #result_name) {
                                        return #result_name;
                                    }
                                });
                                vec3.push(quote! {
                                    #map_key: #result_name.Ok,
                                });
                            }
                        }

                        variant_tokens.push(quote! {
                            if (#variant_map_key in data && typeof data[#variant_map_key] === "object" && data[#variant_map_key] !== null && !Array.isArray(data[#variant_map_key])) {
                                const obj = data[#variant_map_key];
                                #(#vec1)*
                                return { Ok: { #variant_map_key: { #(#vec3)* } } };
                            }
                        });
                    }
                }
            }

            output.note(format!("derive_decode for enum: {type_exp}"));
            output.import(TSImport::granite_type("JsonValue"));
            output.import(TSImport::granite_type("Result"));
            output.code(quote! {
                export function #function_ident(data: JsonValue): Result<#type_exp, string> {
                    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
                        #(#variant_tokens)*
                    }

                    return { Err: "invalid variant" };
                };
            });
        }
        GInner::Type(GInnerType { import_from, .. }) => {
            match import_from {
                Some(import_from) => {
                    output.import(TSImport::other_item(
                        &gadt.ident().to_decode().to_string(),
                        import_from,
                    ));
                }
                None => {
                    // no need to do anything
                }
            }
        }
    }
}

trait GTypeExt {
    fn is_undefinable(&self) -> bool;
    fn optional_token(&self) -> TokenStream;
}

trait Ext {
    fn type_exp(&self) -> TokenStream;
    fn import_type(&self, imports: &mut HashSet<TSImport>);
    fn import_validate(&self, imports: &mut HashSet<TSImport>);
    fn import_encode(&self, imports: &mut HashSet<TSImport>);
    fn import_decode(&self, imports: &mut HashSet<TSImport>);
    fn callable_validate(&self) -> TokenStream;

    fn callable_encode(&self) -> TokenStream;
    fn callable_decode(&self) -> TokenStream;
}

impl GTypeExt for GType {
    fn is_undefinable(&self) -> bool {
        matches!(self, GType::Undefinable(_))
    }

    fn optional_token(&self) -> TokenStream {
        match self {
            GType::Undefinable(_) => quote! {?},
            _ => quote! {},
        }
    }
}

impl Ext for GType {
    fn type_exp(&self) -> TokenStream {
        match self {
            GType::Undefinable(gtype) => {
                let inner_type = gtype.type_exp();
                quote! {
                    (#inner_type | undefined)
                }
            }
            GType::NestedError(gtype) => {
                let inner_token = gtype.type_exp();
                quote! {
                    NestedError<#inner_token>
                }
            }
            GType::Undefined => {
                quote! {
                    undefined
                }
            }
            GType::GType(ident) => ident.to_token_stream(),
            GType::Scalar(gscalar) => gscalar.type_exp(),
            GType::Option {
                zero_to_none: _,
                some_type: vtype,
            } => {
                let inner_token = vtype.type_exp();
                quote! {
                    Option<#inner_token>
                }
            }
            GType::Result(ok, err) => {
                let ok_token = ok.type_exp();
                let err_token = err.type_exp();
                quote! {
                    Result<#ok_token, #err_token>
                }
            }
            GType::Vec(gtype) => {
                let inner_token = gtype.type_exp();
                quote! {
                    Vec<#inner_token>
                }
            }
            GType::HashSet(gscalar) => {
                let scalar_token = gscalar.type_exp();
                quote! {
                    HashSet<#scalar_token>
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                max_items: _,
            } => {
                let key_token = ktype.type_exp();
                let value_token = vtype.type_exp();
                quote! {
                    HashMap<#key_token, #value_token>
                }
            }
            GType::BTreeMap(key, value) => {
                let key_token = key.type_exp();
                let value_token = value.type_exp();
                quote! {
                    BTreeMap<#key_token, #value_token>
                }
            }
            GType::IndexMap(key, value) => {
                let key_token = key.type_exp();
                let value_token = value.type_exp();
                quote! {
                    IndexMap<#key_token, #value_token>
                }
            }
            GType::Range(scalar) => {
                let scalar_token = scalar.type_exp();
                quote! {
                    Range<#scalar_token>
                }
            }
            GType::JsonValue => {
                quote! {
                    JsonValue
                }
            }
            GType::JsonObject => {
                quote! {
                    JsonObject
                }
            }
            GType::JsonArray => {
                quote! {
                    JsonArray
                }
            }
        }
    }

    fn import_validate(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::Undefinable(gtype) => {
                gtype.import_validate(imports);
            }
            GType::NestedError(_) => {
                unreachable!(
                    "ErrorOuterInner can only be used with GStyle::Error therefore cannot be used with Validate"
                );
            }
            GType::Undefined => {
                unreachable!(
                    "Undefined can only be used with GStyle::Error therefore cannot be used with Validate"
                );
            }
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_validate(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_validate"));
                vtype.import_validate(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_validate"));
                ok.import_validate(imports);
                err.import_validate(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_validate"));
                item.import_validate(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_validate"));
                item.import_validate(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_validate"));
                ktype.import_validate(imports);
                vtype.import_validate(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_validate"));
                key.import_validate(imports);
                value.import_validate(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_validate"));
                key.import_validate(imports);
                value.import_validate(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_validate"));
                item.import_validate(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_validate"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_validate"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_validate"));
            }
        }
    }

    fn import_type(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::Undefinable(gtype) => {
                gtype.import_type(imports);
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_type("NestedError"));
                gtype.import_type(imports);
            }
            GType::Undefined => {}
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_type(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_type("Option"));
                vtype.import_type(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_type("Result"));
                ok.import_type(imports);
                err.import_type(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_type("Vec"));
                item.import_type(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_type("HashSet"));
                item.import_type(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_type("HashMap"));
                ktype.import_type(imports);
                vtype.import_type(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_type("BTreeMap"));
                key.import_type(imports);
                value.import_type(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_type("IndexMap"));
                key.import_type(imports);
                value.import_type(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_type("Range"));
                item.import_type(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_type("JsonValue"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_type("JsonObject"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_type("JsonArray"));
            }
        }
    }

    fn import_encode(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::Undefinable(gtype) => {
                gtype.import_encode(imports);
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_encode"));
                gtype.import_encode(imports);
            }
            GType::Undefined => {}
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_encode(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_encode"));
                vtype.import_encode(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_encode"));
                ok.import_encode(imports);
                err.import_encode(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_encode"));
                item.import_encode(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_encode"));
                item.import_encode(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_encode"));
                ktype.import_encode(imports);
                vtype.import_encode(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_encode"));
                key.import_encode(imports);
                value.import_encode(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_encode"));
                key.import_encode(imports);
                value.import_encode(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_encode"));
                item.import_encode(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_encode"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_encode"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_encode"));
            }
        }
    }

    fn import_decode(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::Undefinable(gtype) => {
                gtype.import_decode(imports);
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_decode"));
                gtype.import_decode(imports);
            }
            GType::Undefined => {}
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_decode(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_decode"));
                vtype.import_decode(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_decode"));
                ok.import_decode(imports);
                err.import_decode(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_decode"));
                item.import_decode(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_decode"));
                item.import_decode(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_decode"));
                ktype.import_decode(imports);
                vtype.import_decode(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_decode"));
                key.import_decode(imports);
                value.import_decode(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_decode"));
                key.import_decode(imports);
                value.import_decode(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_decode"));
                item.import_decode(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_decode"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_decode"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_decode"));
            }
        }
    }

    fn callable_validate(&self) -> TokenStream {
        match &self {
            GType::Undefinable(inner_gtype) => inner_gtype.callable_validate(),
            GType::NestedError(_) => {
                unreachable!(
                    "ErrorOuterInner can only be used with GStyle::Error therefore cannot be used with Validate"
                );
            }
            GType::Undefined => {
                unreachable!(
                    "Undefined can only be used with GStyle::Error therefore cannot be used with Validate"
                );
            }
            GType::GType(ident) => {
                let callable = ident.to_validate();
                quote! {
                    #callable
                }
            }
            GType::Scalar(scalar) => {
                let callable = scalar.callable_validate();
                quote! {
                    #callable
                }
            }
            GType::Option { some_type, .. } => {
                let some_callable = some_type.callable_validate();
                quote! {
                    ((v) => {
                        return Option_validate(v, #some_callable)
                    })
                }
            }
            GType::Result(_ok, _err) => {
                unreachable!(
                    "Result can only be used with GStyle::Error therefore cannot be used with Validate"
                );
            }
            GType::Vec(value_gtype) => {
                let value_callable = value_gtype.callable_validate();
                quote! {
                    ((v) => {
                        return Vec_validate(v, #value_callable)
                    })
                }
            }
            GType::HashSet(inner) => {
                let value_callable = inner.callable_validate();
                quote! {
                    ((v) => {
                        return HashSet_validate(v, #value_callable)
                    })
                }
            }
            GType::HashMap {
                key_type,
                value_type,
                ..
            } => {
                let key_callable = key_type.callable_validate();
                let value_callable = value_type.callable_validate();
                quote! {
                    ((v) => {
                        return HashMap_validate(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_callable = key.callable_validate();
                let value_callable = value.callable_validate();
                quote! {
                    ((v) => {
                        return BTreeMap_validate(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_callable = key.callable_validate();
                let value_callable = value.callable_validate();
                quote! {
                    ((v) => {
                        return IndexMap_validate(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_callable = scalar.callable_validate();
                quote! {
                    ((v) => {
                        return Range_validate(v, #inner_callable)
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    JsonValue_validate
                }
            }
            GType::JsonObject => {
                quote! {
                    JsonObject_validate
                }
            }
            GType::JsonArray => {
                quote! {
                    JsonArray_validate
                }
            }
        }
    }

    fn callable_encode(&self) -> TokenStream {
        match &self {
            GType::Undefinable(inner_gtype) => {
                let inner_callable = inner_gtype.callable_encode();
                quote! {
                    ((v) => {
                        if (v === undefined) {
                            return null;
                        }
                        else {
                            return #inner_callable(v)
                        }
                    })
                }
            }
            GType::NestedError(inner_gtype) => {
                let inner_callable = inner_gtype.callable_encode();
                quote! {
                    ((v) => {
                        return NestedError_encode(v, #inner_callable)
                    })
                }
            }
            GType::Undefined => {
                quote! {
                    (_v) => { return null; }
                }
            }

            GType::GType(ident) => {
                let callable = ident.to_encode();
                quote! {
                    #callable
                }
            }
            GType::Scalar(scalar) => {
                let callable = scalar.callable_encode();
                quote! {
                    #callable
                }
            }
            GType::Option { some_type, .. } => {
                let some_callable = some_type.callable_encode();
                quote! {
                    ((v) => {
                        return Option_encode(v, #some_callable)
                    })
                }
            }
            GType::Result(ok, err) => {
                let ok_callable = ok.callable_encode();
                let err_callable = err.callable_encode();
                quote! {
                    ((v) => {
                        return Result_encode(v, #ok_callable, #err_callable)
                    })
                }
            }
            GType::Vec(value_gtype) => {
                let value_callable = value_gtype.callable_encode();
                quote! {
                    ((v) => {
                        return Vec_encode(v, #value_callable)
                    })
                }
            }
            GType::HashSet(inner) => {
                let value_callable = inner.callable_encode();
                quote! {
                    ((v) => {
                        return HashSet_encode(v, #value_callable)
                    })
                }
            }
            GType::HashMap {
                key_type,
                value_type,
                ..
            } => {
                let key_callable = key_type.callable_encode();
                let value_callable = value_type.callable_encode();
                quote! {
                    ((v) => {
                        return HashMap_encode(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_callable = key.callable_encode();
                let value_callable = value.callable_encode();
                quote! {
                    ((v) => {
                        return BTreeMap_encode(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_callable = key.callable_encode();
                let value_callable = value.callable_encode();
                quote! {
                    ((v) => {
                        return IndexMap_encode(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_callable = scalar.callable_encode();
                quote! {
                    ((v) => {
                        return Range_encode(v, #inner_callable)
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    JsonValue_encode
                }
            }
            GType::JsonObject => {
                quote! {
                    JsonObject_encode
                }
            }
            GType::JsonArray => {
                quote! {
                    JsonArray_encode
                }
            }
        }
    }

    fn callable_decode(&self) -> TokenStream {
        match &self {
            GType::Undefinable(inner_gtype) => {
                let inner_callable = inner_gtype.callable_decode();
                quote! {
                    ((v) => {
                        if (typeof v === "object" && v !== null && !Array.isArray(v) && "Some" in v) {
                            return #inner_callable(v["Some"]);
                        }
                        return { Err: "NONE" };
                    })
                }
            }
            GType::NestedError(inner_gtype) => {
                let inner_callable = inner_gtype.callable_decode();
                quote! {
                    ((v) => {
                        return NestedError_decode(v, #inner_callable)
                    })
                }
            }
            GType::Undefined => {
                quote! {
                    (_v) => { return {Ok: undefined} }
                }
            }
            GType::GType(ident) => {
                let callable = ident.to_decode();
                quote! {
                    #callable
                }
            }
            GType::Scalar(scalar) => {
                let callable = scalar.callable_decode();
                quote! {
                    #callable
                }
            }
            GType::Option { some_type, .. } => {
                let some_callable = some_type.callable_decode();
                quote! {
                    ((v) => {
                        return Option_decode(v, #some_callable)
                    })
                }
            }
            GType::Result(ok, err) => {
                let ok_callable = ok.callable_decode();
                let err_callable = err.callable_decode();
                quote! {
                    ((v) => {
                        return Result_decode(v, #ok_callable, #err_callable)
                    })
                }
            }
            GType::Vec(value_gtype) => {
                let value_callable = value_gtype.callable_decode();
                quote! {
                    ((v) => {
                        return Vec_decode(v, #value_callable)
                    })
                }
            }
            GType::HashSet(inner) => {
                let value_callable = inner.callable_decode();
                quote! {
                    ((v) => {
                        return HashSet_decode(v, #value_callable)
                    })
                }
            }
            GType::HashMap {
                key_type,
                value_type,
                ..
            } => {
                let key_callable = key_type.callable_decode();
                let value_callable = value_type.callable_decode();
                quote! {
                    ((v) => {
                        return HashMap_decode(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_callable = key.callable_decode();
                let value_callable = value.callable_decode();
                quote! {
                    ((v) => {
                        return BTreeMap_decode(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_callable = key.callable_decode();
                let value_callable = value.callable_decode();
                quote! {
                    ((v) => {
                        return IndexMap_decode(v, #key_callable, #value_callable)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_callable = scalar.callable_decode();
                quote! {
                    ((v) => {
                        return Range_decode(v, #inner_callable)
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    JsonValue_decode
                }
            }
            GType::JsonObject => {
                quote! {
                    JsonObject_decode
                }
            }
            GType::JsonArray => {
                quote! {
                    JsonArray_decode
                }
            }
        }
    }
}

impl Ext for GScalar {
    fn type_exp(&self) -> TokenStream {
        match self {
            GScalar::char => quote! { char },
            GScalar::bool => quote! { boolean }, // boolean is the name in ts
            GScalar::i8 { .. } => quote! { i8 },
            GScalar::u8 { .. } => quote! { u8 },
            GScalar::i16 { .. } => quote! { i16 },
            GScalar::u16 { .. } => quote! { u16 },
            GScalar::i32 { .. } => quote! { i32 },
            GScalar::u32 { .. } => quote! { u32 },
            GScalar::i64 { .. } => quote! { i64 },
            GScalar::u64 { .. } => quote! { u64 },
            GScalar::f32 { .. } => quote! { f32 },
            GScalar::f64 { .. } => quote! { f64 },
            GScalar::String { .. } => quote! { string }, // this is the only exception because `String` means something in TS
            GScalar::Uuid { .. } => quote! { Uuid },
            GScalar::Decimal { .. } => quote! { Decimal },
            GScalar::Integer => quote! { Integer },
            GScalar::BigInt => quote! { BigInt },
            GScalar::DateUtc => quote! { DateUtc },
            GScalar::DateTz => quote! { DateTz },
            GScalar::DateTimeUtc => quote! { DateTimeUtc },
            GScalar::DateTimeTz => quote! { DateTimeTz },
            GScalar::Duration => quote! { Duration },
            GScalar::Time => quote! { Time },
            GScalar::IpAddr => quote! { IpAddr },
        }
    }

    fn import_type(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_type("char"));
            }
            GScalar::bool => {
                // boolean is builtin
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_type("i8"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_type("u8"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_type("i16"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_type("u16"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_type("i32"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_type("u32"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_type("i64"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_type("u64"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_type("f32"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_type("f64"));
            }
            GScalar::String { .. } => {
                // string is a ts builtin type
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_type("Uuid"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_type("Decimal"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_type("Integer"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_type("BigInt"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_type("DateUtc"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_type("DateTz"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_type("DateTimeUtc"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_type("DateTimeTz"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_type("Duration"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_type("Time"));
            }
            GScalar::IpAddr => {
                imports.insert(TSImport::granite_type("IpAddr"));
            }
        }
    }

    fn import_validate(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_validate"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_validate"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_validate"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_validate"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_validate"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_validate"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_validate"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_validate"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_validate"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_validate"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_validate"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_validate"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_validate"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_validate"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_validate"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_validate"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_validate"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_validate"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_validate"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_validate"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_validate"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_validate"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_validate"));
            }
            GScalar::IpAddr => {
                imports.insert(TSImport::granite_item("IpAddr_validate"));
            }
        }
    }

    fn import_encode(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_encode"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_encode"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_encode"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_encode"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_encode"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_encode"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_encode"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_encode"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_encode"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_encode"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_encode"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_encode"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_encode"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_encode"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_encode"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_encode"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_encode"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_encode"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_encode"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_encode"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_encode"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_encode"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_encode"));
            }
            GScalar::IpAddr => {
                imports.insert(TSImport::granite_item("IpAddr_encode"));
            }
        }
    }

    fn import_decode(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_decode"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_decode"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_decode"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_decode"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_decode"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_decode"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_decode"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_decode"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_decode"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_decode"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_decode"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_decode"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_decode"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_decode"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_decode"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_decode"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_decode"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_decode"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_decode"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_decode"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_decode"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_decode"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_decode"));
            }
            GScalar::IpAddr => {
                imports.insert(TSImport::granite_item("IpAddr_decode"));
            }
        }
    }

    fn callable_validate(&self) -> TokenStream {
        match self {
            GScalar::char => quote! { char_validate },
            GScalar::bool => quote! { boolean_validate },
            GScalar::i8 { .. } => quote! { i8_validate },
            GScalar::u8 { .. } => quote! { u8_validate },
            GScalar::i16 { .. } => quote! { i16_validate },
            GScalar::u16 { .. } => quote! { u16_validate },
            GScalar::i32 { .. } => quote! { i32_validate },
            GScalar::u32 { .. } => quote! { u32_validate },
            GScalar::i64 { .. } => quote! { i64_validate },
            GScalar::u64 { .. } => quote! { u64_validate },
            GScalar::f32 { .. } => quote! { f32_validate },
            GScalar::f64 { .. } => quote! { f64_validate },
            GScalar::String { .. } => quote! { string_validate },
            GScalar::Uuid { .. } => quote! { Uuid_validate },
            GScalar::Decimal { .. } => quote! { Decimal_validate },
            GScalar::Integer => quote! { Integer_validate },
            GScalar::BigInt => quote! { BigInt_validate },
            GScalar::DateUtc => quote! { DateUtc_validate },
            GScalar::DateTz => quote! { DateTz_validate },
            GScalar::DateTimeUtc => quote! { DateTimeUtc_validate },
            GScalar::DateTimeTz => quote! { DateTimeTz_validate },
            GScalar::Duration => quote! { Duration_validate },
            GScalar::Time => quote! { Time_validate },
            GScalar::IpAddr => quote! { IpAddr_validate },
        }
    }

    fn callable_encode(&self) -> TokenStream {
        match self {
            GScalar::char => quote! { char_encode },
            GScalar::bool => quote! { boolean_encode },
            GScalar::i8 { .. } => quote! { i8_encode },
            GScalar::u8 { .. } => quote! { u8_encode },
            GScalar::i16 { .. } => quote! { i16_encode },
            GScalar::u16 { .. } => quote! { u16_encode },
            GScalar::i32 { .. } => quote! { i32_encode },
            GScalar::u32 { .. } => quote! { u32_encode },
            GScalar::i64 { .. } => quote! { i64_encode },
            GScalar::u64 { .. } => quote! { u64_encode },
            GScalar::f32 { .. } => quote! { f32_encode },
            GScalar::f64 { .. } => quote! { f64_encode },
            GScalar::String { .. } => quote! { string_encode },
            GScalar::Uuid { .. } => quote! { Uuid_encode },
            GScalar::Decimal { .. } => quote! { Decimal_encode },
            GScalar::Integer => quote! { Integer_encode },
            GScalar::BigInt => quote! { BigInt_encode },
            GScalar::DateUtc => quote! { DateUtc_encode },
            GScalar::DateTz => quote! { DateTz_encode },
            GScalar::DateTimeUtc => quote! { DateTimeUtc_encode },
            GScalar::DateTimeTz => quote! { DateTimeTz_encode },
            GScalar::Duration => quote! { Duration_encode },
            GScalar::Time => quote! { Time_encode },
            GScalar::IpAddr => quote! { IpAddr_encode },
        }
    }

    fn callable_decode(&self) -> TokenStream {
        match self {
            GScalar::char => quote! { char_decode },
            GScalar::bool => quote! { boolean_decode },
            GScalar::i8 { .. } => quote! { i8_decode },
            GScalar::u8 { .. } => quote! { u8_decode },
            GScalar::i16 { .. } => quote! { i16_decode },
            GScalar::u16 { .. } => quote! { u16_decode },
            GScalar::i32 { .. } => quote! { i32_decode },
            GScalar::u32 { .. } => quote! { u32_decode },
            GScalar::i64 { .. } => quote! { i64_decode },
            GScalar::u64 { .. } => quote! { u64_decode },
            GScalar::f32 { .. } => quote! { f32_decode },
            GScalar::f64 { .. } => quote! { f64_decode },
            GScalar::String { .. } => quote! { string_decode },
            GScalar::Uuid { .. } => quote! { Uuid_decode },
            GScalar::Decimal { .. } => quote! { Decimal_decode },
            GScalar::Integer => quote! { Integer_decode },
            GScalar::BigInt => quote! { BigInt_decode },
            GScalar::DateUtc => quote! { DateUtc_decode },
            GScalar::DateTz => quote! { DateTz_decode },
            GScalar::DateTimeUtc => quote! { DateTimeUtc_decode },
            GScalar::DateTimeTz => quote! { DateTimeTz_decode },
            GScalar::Duration => quote! { Duration_decode },
            GScalar::Time => quote! { Time_decode },
            GScalar::IpAddr => quote! { IpAddr_decode },
        }
    }
}
