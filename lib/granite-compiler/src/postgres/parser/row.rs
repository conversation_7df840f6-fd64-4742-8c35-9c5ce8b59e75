use super::super::{<PERSON>T<PERSON>, Query<PERSON><PERSON><PERSON><PERSON><PERSON>er, Row<PERSON>ield};
use crate::tokenator::{TokenError, TokenIter};

/// the job of this function is to parse the ... part of:
///     `row = ... ;`
/// the `;` will be consumed by the caller
pub(super) fn parse_value_into_row(
    token_iter: &mut TokenIter,
    qbb: &mut QueryBundleBuilder,
) -> Result<(), TokenError> {
    let mut fields: Vec<RowField> = Vec::new();

    // convert the token iter into a brace group iter
    let mut token_iter = token_iter.take_brace_group_iter()?;
    // step to the first token
    // TODO: when starting any token iterator, we should always step to the first token because it always needs done.
    token_iter.step();

    loop {
        let ident = token_iter.take_ident()?;
        token_iter.take_colon()?;
        let ty =
            match token_iter.get_ident_as_string()?.as_str() {
                "Uuid" => {
                    token_iter.step();
                    FieldType::Uuid
                }
                "String" => {
                    token_iter.step();
                    FieldType::String
                }
                "bool" => {
                    token_iter.step();
                    FieldType::bool
                }
                "i32" => {
                    token_iter.step();
                    FieldType::i32
                }
                "i64" => {
                    token_iter.step();
                    FieldType::i64
                }
                "Decimal" => {
                    token_iter.step();
                    FieldType::Decimal
                }
                "DateTimeUtc" => {
                    token_iter.step();
                    FieldType::DateTime
                }
                "DateUtc" => {
                    token_iter.step();
                    FieldType::Date
                }
                "IpAddr" => {
                    token_iter.step();
                    FieldType::IpAddr
                }
                "Option" => {
                    token_iter.step();
                    let inner = match token_iter.take_less_than_path_greater_than() {
                        Ok(ty) => match ty.as_str() {
                            "Uuid" => FieldType::Uuid,
                            "String" => FieldType::String,
                            "bool" => FieldType::bool,
                            "i32" => FieldType::i32,
                            "i64" => FieldType::i64,
                            "Decimal" => FieldType::Decimal,
                            "DateTimeUtc" => FieldType::DateTime,
                            "DateUtc" => FieldType::Date,
                            "IpAddr" => FieldType::IpAddr,
                            t => {
                                return Err(token_iter
                                    .error(format!("invalid nested type: `{}`", t).as_str()));
                            }
                        },
                        Err(_) => return Err(token_iter.error("expected type inside Option")),
                    };
                    FieldType::Option(Box::new(inner))
                }
                "Vec" => {
                    token_iter.step();
                    let inner = match token_iter.take_less_than_path_greater_than() {
                        Ok(ty) => match ty.as_str() {
                            "Uuid" => FieldType::Uuid,
                            "String" => FieldType::String,
                            "bool" => FieldType::bool,
                            "i32" => FieldType::i32,
                            "i64" => FieldType::i64,
                            "Decimal" => FieldType::Decimal,
                            "DateUtc" => FieldType::Date,
                            "DateTimeUtc" => FieldType::DateTime,
                            "IpAddr" => FieldType::IpAddr,
                            t => {
                                return Err(token_iter
                                    .error(format!("invalid nested type: `{}`", t).as_str()));
                            }
                        },
                        Err(_) => return Err(token_iter.error("expected type inside Vec")),
                    };
                    FieldType::Vec(Box::new(inner))
                }
                t => return Err(token_iter.error(format!("invalid type: `{}`", t).as_str())),
            };

        token_iter.take_comma()?;

        fields.push(RowField { ident, ty });

        if token_iter.is_end() {
            break;
        }
    }

    qbb.row = Some(fields);

    Ok(())
}

// TODO: unit tests
