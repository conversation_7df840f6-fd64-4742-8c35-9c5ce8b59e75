use std::collections::{HashMap, HashSet};
use std::path::{Path, PathBuf};

#[derive(Debug)]
pub enum MemFsError {
    Io(std::io::Error),
    IgnoreError(ignore::<PERSON><PERSON><PERSON>),
    FileNotFound(PathBuf),
    InvalidPathError(PathBuf),
    SerdeJsonError(serde_json::Error),
    DprintError(String),
}

pub enum FileContent {
    Bytes(Vec<u8>),
    String(String),
    <PERSON><PERSON>(String),
    <PERSON><PERSON>(serde_json::Value),
    TypeScript(String),
    RustFormatted(String),
}

/// MemFs - a struct used to manage writing of auto-generated files to a source code directory
///
/// ### Public API
///
/// `new(path: &Path)`
/// creates a new MemFs instance with the given root path
///
/// `write_*(path: &PathBuf, content: *)`
/// writes content to the given path in memory.
///
/// `set_to_clean(path: &PathBuf)`
/// adds a path to the clean set, which will be removed in `.clean()`
///
/// `commit()`
/// writes all files to disk that have been changed from their on-disk counterparts
///
/// `clean()`
/// removes all files in the clean set from disk
/// ///
/// ### Private Fields
///
/// `path`
/// * The root path of the memory file system
///
/// `file_map`
/// * A HashMap of paths to file content.  The content is stored as a `FileContent` enum
///
/// `clean_set`
/// * A HashSet of paths that should be removed at the conclusion of the build.
/// * Anytime file content is added, the path is removed from clean_set if it exists.
///
pub struct MemFs {
    path: PathBuf,
    file_map: HashMap<PathBuf, FileContent>,
    clean_set: HashSet<PathBuf>,
    dprint_plugin_typescript_configuration: dprint_plugin_typescript::configuration::Configuration,
    dprint_plugin_json_configuration: dprint_plugin_json::configuration::Configuration,
}

impl MemFs {
    pub fn path(&self) -> &Path {
        &self.path
    }

    pub fn new(path: &Path) -> Result<Self, MemFsError> {
        let file_map = HashMap::new();
        let clean_set = HashSet::new();

        let dprint_plugin_typescript_configuration =
            dprint_plugin_typescript::configuration::ConfigurationBuilder::new()
                .line_width(100)
                .indent_width(4)
                .use_tabs(false)
                .quote_style(dprint_plugin_typescript::configuration::QuoteStyle::AlwaysDouble)
                .semi_colons(dprint_plugin_typescript::configuration::SemiColons::Always)
                .paren_expression_space_around(false)
                .build();

        let dprint_plugin_json_configuration =
            dprint_plugin_json::configuration::ConfigurationBuilder::new()
                .line_width(100)
                .indent_width(4)
                .build();

        Ok(Self {
            path: path.to_owned(),
            file_map,
            clean_set,
            dprint_plugin_typescript_configuration,
            dprint_plugin_json_configuration,
        })
    }

    pub fn set_to_clean(&mut self, path: &PathBuf) {
        self.clean_set.insert(path.to_owned());
    }

    pub fn write_bytes(&mut self, path: &PathBuf, content: Vec<u8>) -> Result<(), MemFsError> {
        self.write(path, FileContent::Bytes(content))
    }

    pub fn write_string(&mut self, path: &PathBuf, content: String) -> Result<(), MemFsError> {
        self.write(path, FileContent::String(content))
    }

    pub fn write_toml(&mut self, path: &PathBuf, content: String) -> Result<(), MemFsError> {
        self.write(path, FileContent::Toml(content))
    }

    pub fn write_json(
        &mut self,
        path: &PathBuf,
        content: serde_json::Value,
    ) -> Result<(), MemFsError> {
        self.write(path, FileContent::Json(content))
    }

    pub fn write_typescript(&mut self, path: &PathBuf, content: String) -> Result<(), MemFsError> {
        self.write(path, FileContent::TypeScript(content))
    }

    pub fn write_rust(&mut self, path: &PathBuf, content: syn::File) -> Result<(), MemFsError> {
        self.write_rust_formatted(path, format_rust(content))
    }

    pub fn write_rust_formatted(
        &mut self,
        path: &PathBuf,
        content: String,
    ) -> Result<(), MemFsError> {
        self.write(path, FileContent::RustFormatted(content))
    }

    fn write(&mut self, path: &PathBuf, content: FileContent) -> Result<(), MemFsError> {
        // verify that the path is either a relative path or a sub-path to self.path, and convert to an abs path
        let path = if path.is_relative() {
            self.path.join(path)
        } else if path.starts_with(&self.path) {
            path.to_owned()
        } else {
            return Err(MemFsError::InvalidPathError(path.to_owned()));
        };

        // Add the content converted to Vec<u8> to the file_map
        self.file_map.insert(path.to_owned(), content);

        // Remove from the clean set
        self.clean_set.remove(&path);

        Ok(())
    }

    pub fn commit(&mut self) -> Result<(), MemFsError> {
        // drain the file_map, removing files that are None, and checking content of files that are Some
        for (path, content) in self.file_map.drain() {
            match content {
                FileContent::Bytes(content) => {
                    Self::write_if_different(&path, &content)?;
                }
                FileContent::String(content) => {
                    Self::write_if_different(&path, content.as_bytes())?;
                }
                FileContent::Toml(content) => {
                    Self::write_if_different(&path, content.as_bytes())?;
                }
                FileContent::Json(content) => {
                    let json_str =
                        serde_json::to_string(&content).map_err(MemFsError::SerdeJsonError)?;

                    let (formatted, e) = match dprint_plugin_json::format_text(
                        &path,
                        &json_str,
                        &self.dprint_plugin_json_configuration,
                    ) {
                        Ok(Some(formatted)) => (formatted, None),
                        Ok(None) => (json_str, None),
                        Err(e) => (json_str, Some(e)),
                    };

                    Self::write_if_different(&path, formatted.as_bytes())?;

                    if let Some(e) = e {
                        return Err(MemFsError::DprintError(e.to_string()));
                    }
                }
                FileContent::TypeScript(content) => {
                    let (formatted, e) = match dprint_plugin_typescript::format_text(
                        &path,
                        None,
                        content.clone(), // unfortunate API decision to not take a string reference
                        &self.dprint_plugin_typescript_configuration,
                    ) {
                        Ok(Some(formatted)) => (formatted, None),
                        Ok(None) => (content, None),
                        Err(e) => (content, Some(e)),
                    };

                    Self::write_if_different(&path, formatted.as_bytes())?;

                    if let Some(e) = e {
                        return Err(MemFsError::DprintError(e.to_string()));
                    }
                }
                FileContent::RustFormatted(content) => {
                    Self::write_if_different(&path, content.as_bytes())?;
                }
            }
        }
        Ok(())
    }

    pub fn clean(&mut self) -> Result<(), MemFsError> {
        for path in self.clean_set.drain() {
            std::fs::remove_file(path).map_err(MemFsError::Io)?;
        }
        Ok(())
    }

    fn write_if_different(path: &PathBuf, content: &[u8]) -> Result<(), MemFsError> {
        if let Ok(metadata) = std::fs::metadata(path) {
            if metadata.len() == content.len() as u64 {
                if let Ok(file_content) = std::fs::read(path) {
                    if file_content == content {
                        return Ok(());
                    }
                }
            }
        }

        // Either file doesn't exist, sizes differ, or content is different - write it
        println!("WRITTEN: {}", path.display());
        std::fs::write(path, content).map_err(MemFsError::Io)
    }
}

pub fn format_rust(content: syn::File) -> String {
    prettyplease::unparse(&content)
}
