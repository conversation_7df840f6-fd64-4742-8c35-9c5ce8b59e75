export * from "@granite-core/lib.mts";
import { type Result } from "@granite-core/lib.mts";

export {
    CUR0,
    CUR2,
    DASH,
    HS,
    type HTML,
    INT0,
    INT2,
    make_element,
    make_style,
    PER0,
    PER2,
    PLUR,
    QA,
    ROUND0,
    ROUND2,
    SE,
    SE_nullable,
    SEC,
} from "./util.mts";

export { age_in_years, parse_yyyy_mm_dd, position_in_calendar_year } from "./date.mts";

export type Struct$id<STRUCT extends object> = {
    $id: string;
} & STRUCT;

let NEWID_COUNTER = 1000;
export function NEWID(): string {
    return `newid-${NEWID_COUNTER++}`;
}

/**
 * Generic utility function to unwrap a Result type or return a default value
 * Replaces the repetitive "Ok" in result ? result.Ok : default_value pattern
 */
export function unwrap_or<T>(result: Result<T, any>, default_value: T): T {
    return "Ok" in result ? result.Ok : default_value;
}
