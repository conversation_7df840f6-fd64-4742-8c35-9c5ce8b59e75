use std::{io::Read, process::Command};

pub(crate) fn test(workspace: &'static crate::Workspace, packages: Vec<String>) {
    test_deno(workspace, packages.clone());
    test_rust(workspace, packages);
}

pub(crate) fn test_deno(workspace: &'static crate::Workspace, packages: Vec<String>) {
    crate::process::call_deno_test(workspace, packages);
}

pub(crate) fn test_rust(workspace: &'static crate::Workspace, packages: Vec<String>) {
    let mut cmd = std::process::Command::new("cargo");
    cmd.current_dir(&workspace.path);
    cmd.arg("test");
    for package in packages {
        cmd.arg("-p");
        cmd.arg(package);
    }
    cmd.status().expect("failed to execute process");
}

pub(crate) fn test_postgres(workspace: &'static crate::Workspace, packages: Vec<String>) {
    if !packages.is_empty() {
        todo!("`--package` is not supported for `test postgres`");
    }
    let mut script_path = workspace.path.clone();
    script_path.push("meta/Docker/test-postgres/");

    let mut command = Command::new("sh");
    command.arg("run.sh");
    command.current_dir(&script_path);

    let mut child = command.spawn().expect("Unable to run shellscript");
    let exit_status = &child.wait().unwrap();
    if !exit_status.success() {
        if let Some(mut stderr) = child.stderr {
            let mut buffer = vec![];
            stderr.read_to_end(&mut buffer).unwrap();

            let message = String::from_utf8_lossy(&buffer);
            eprintln!("run.sh was unsuccessful: [{}]\n{}", exit_status, message)
        } else {
            eprintln!("run.sh failed with {}", exit_status)
        }
    } else {
        println!("\nrun.sh completed successfully")
    }
}

pub(crate) fn test_redis(workspace: &'static crate::Workspace, packages: Vec<String>) {
    if !packages.is_empty() {
        todo!("`--package` is not supported for `test redis`");
    }
    let mut script_path = workspace.path.clone();
    script_path.push("meta/Docker/test-redis/");

    let mut command = Command::new("sh");
    command.arg("run.sh");
    command.current_dir(&script_path);

    let mut child = command.spawn().expect("Unable to run shellscript");
    let exit_status = &child.wait().unwrap();
    if !exit_status.success() {
        if let Some(mut stderr) = child.stderr {
            let mut buffer = vec![];
            stderr.read_to_end(&mut buffer).unwrap();

            let message = String::from_utf8_lossy(&buffer);
            eprintln!("run.sh was unsuccessful: [{}]\n{}", exit_status, message)
        } else {
            eprintln!("run.sh failed with {}", exit_status)
        }
    } else {
        println!("\nrun.sh completed successfully")
    }
}
