pub(super) fn check(workspace: &'static crate::Workspace) {
    println!("---- running `tsc` ----");
    crate::process::call_tsc(workspace, &["--build"]);
    println!("---- running `deno lint` ----");
    crate::process::call_deno::<_, &str>(workspace, "lint", Vec::new());
    println!("---- running `cargo clippy` ----");
    crate::process::call_cargo_all(workspace, &["clippy"]);
    println!("---- done ----")
}
