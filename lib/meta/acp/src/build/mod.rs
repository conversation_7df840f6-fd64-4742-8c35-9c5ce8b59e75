mod cargo;
mod config;
pub(crate) mod deno2;
pub(crate) mod esbuild;
mod package;
mod pgmig;
mod routing;
mod tsconfig;
mod version_file;

pub fn build(
    workspace: &'static crate::Workspace,
    packages: Option<&[String]>,
    release: bool,
    prep_only: bool,
) {
    // record start time
    let start_time = std::time::Instant::now();

    // create the memory file system
    let mut mem_fs = approck_memfs::MemFs::new(&workspace.path).unwrap_or_else(|e| {
        panic!("Error creating memfs: {:?}", e);
    });

    // Get the apps we are going to build
    let build_apps: Vec<&crate::Application> = {
        match packages {
            Some(packages) => workspace.get_filtered_apps(packages),
            None => workspace.get_all_apps(),
        }
    };

    // Grab the full set of dependant module crates used by the build apps
    let build_crates = workspace.get_all_crate_refs_for_some_apps(&build_apps);

    // For each crate involved in the build, add all λ files to the clean set, as well as several others
    for build_crate in build_crates {
        for entry in walkdir::WalkDir::new(&build_crate.abs_path) {
            let entry = entry.unwrap_or_else(|e| panic!("Error walking directory: {:?}", e));
            let p = entry.path();
            if p.is_file() {
                if let Some(file_name) = p.file_name() {
                    if file_name.to_string_lossy().contains("λ") {
                        mem_fs.set_to_clean(&p.to_owned());
                    }
                }
            }
        }
    }

    // write the configs
    println!("writing config files");
    self::config::write_config(workspace, &mut mem_fs, release);

    // Install npm packages
    println!("installing npm packages");
    self::deno2::write_deno_configs(workspace, &mut mem_fs, release);

    println!("running deno install");
    self::deno2::deno_install(workspace);

    // write the tsconfig.json for each crate and the root tsconfig.json
    println!("writing tsconfig.json files");
    self::tsconfig::write_tsconfig_files(workspace, &mut mem_fs, release);

    println!("writing root tsconfig.json");
    self::tsconfig::write_root_tsconfig_file(workspace, &mut mem_fs, release);

    // write out the package.json files
    println!("writing package.json files");
    self::package::write_package_json_files(workspace, &mut mem_fs, release);

    println!("generate gtypes");
    approck_compiler::gtype_macro::scan_and_generate(&mut mem_fs);

    println!("process pgmig");
    self::pgmig::process(workspace);

    // Commit all changes
    mem_fs.commit().unwrap_or_else(|e| {
        panic!("Error committing changes: {:?}", e);
    });

    println!("running esbuild on each app in parallel");

    // Use thread::scope to manage thread lifetimes
    std::thread::scope(|scope| {
        let (rs_tx, rs_rx) = std::sync::mpsc::channel();
        let (ts_tx, ts_rx) = std::sync::mpsc::channel();

        // for each app, run esbuild
        for app_ref in build_apps {
            // spawn a scoped thread for each app
            let rs_tx = rs_tx.clone();
            let ts_tx = ts_tx.clone();

            scope.spawn(move || {
                println!("- {} starting", app_ref.crate_name);
                let node_tree = match crate::build::esbuild::run_esbuild(workspace, app_ref) {
                    Ok(node_tree) => node_tree,
                    Err(e) => {
                        println!("--- Compile Error ---\n\n{}", e);
                        std::process::exit(1);
                    }
                };

                let route_tree = approck_compiler::path_info::into_route_tree(node_tree);

                let p = app_ref.get_libλ_rs_path();
                let version_file_path = workspace.path.join("target/version");
                let router_file = route_tree.codegen_router(&version_file_path);
                let content = approck_memfs::format_rust(router_file);
                rs_tx.send((p, content)).unwrap();

                let p = app_ref.get_libλ_ts_path();
                let content = "// this is a stub for auto-generated typescript code".to_string();
                ts_tx.send((p, content)).unwrap();

                println!("- {} DONE", app_ref.crate_name);
            });
        }

        // Drop the senders so the iterator below finishes when all the threads hang up.
        drop(rs_tx);
        drop(ts_tx);

        for (p, content) in rs_rx.iter() {
            mem_fs
                .write_rust_formatted(&p, content)
                .unwrap_or_else(|e| {
                    panic!("Error writing {:?}: {:?}", p, e);
                });
        }

        for (p, content) in ts_rx.iter() {
            mem_fs.write_typescript(&p, content).unwrap_or_else(|e| {
                panic!("Error writing {:?}: {:?}", p, e);
            });
        }
    });

    // --
    // Write version file.  This MUST be last step before build because all code gen is now complete.
    // Sometimes codegen affects committed files, so we need to make sure, if they are not committed,
    // that they show up in the diff which is produced in this function.

    // [1]
    // LOCKED TO [2] - do not move
    println!("writing version file");
    self::version_file::generate_version_file(workspace, &mut mem_fs);

    // Commit remaining changes
    mem_fs.commit().unwrap_or_else(|e| {
        panic!("Error committing changes: {:?}", e);
    });

    // Clean up any extra files that were created, but only if this is a full build
    // This is so we don't get weired file not found errors if you want the efficiency of a partial build
    if packages.is_none() {
        mem_fs.clean().unwrap_or_else(|e| {
            panic!("Error cleaning changes: {:?}", e);
        });
    }

    let prep_duration = start_time.elapsed().as_secs_f32();
    println!("Prep complete in {:.3?} seconds.", prep_duration);

    let start_time = std::time::Instant::now();

    if prep_only {
        println!("Skipping cargo build due to --prep-only flag.");
        return;
    }

    // [2]
    // LOCKED TO [1] - do not move
    println!("running cargo build");
    if release {
        crate::process::call_cargo_limited(workspace, packages, "build", &["--release"]);
    } else {
        crate::process::call_cargo_limited(workspace, packages, "build", &[]);
    }

    let build_duration = start_time.elapsed().as_secs_f32();
    println!("Build complete in {:.3?} seconds.", build_duration);
    println!("Total time: {:.3?}", prep_duration + build_duration);
}
