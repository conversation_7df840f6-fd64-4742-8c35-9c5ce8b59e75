use granite::JsonObject;

use crate::process::call_deno;
use approck_memfs::MemFs;

pub(super) fn write_deno_configs(
    workspace: &'static crate::Workspace,
    mem_fs: &mut MemFs,
    _release: bool,
) {
    for crate_ref in workspace.get_typescript_crates() {
        let target_folder = workspace.path.join("target");

        let mut imports = JsonObject::new();
        imports.insert("@crate/*".to_string(), "./src/*".into());

        for ts_crate_ref in crate_ref.get_typescript_crates(workspace) {
            imports.insert(
                format!("@{}/*", ts_crate_ref.crate_name),
                format!("{}/src/*", ts_crate_ref.abs_path.to_string_lossy()).into(),
            );
        }

        // format settings
        let fmt = serde_json::json!({
            "fmt": {
                "options": {
                    "useTabs": false,
                    "indentWidth": 4,
                    "singleQuote": true,
                    "semiColons": true,
                    "lineWidth": 120
                },
                "exclude": [ "**/*.json", target_folder.to_string_lossy() ]
            }
        });

        // lint settings
        let lint = serde_json::json!({
            "tags": ["recommended"],
            "exclude": [ target_folder.to_string_lossy() ]
        });

        // Typescript settings
        let compiler_options = serde_json::json!({
            "allowUnreachableCode": false,
            "allowUnusedLabels": false,
            "checkJs": true,
            "exactOptionalPropertyTypes": true,
            "noFallthroughCasesInSwitch": true,
            "noImplicitOverride": true,
            "noImplicitReturns": true,
            "noPropertyAccessFromIndexSignature": true,
            "noUncheckedIndexedAccess": true,
            "noUnusedLocals": true,
            "noUnusedParameters": true,
            "strict": true,
            "lib": [
                "dom",
                "esnext",
                "dom.iterable",
                "dom.asynciterable",
                "deno.ns"
            ]
        });

        // test settings
        let test_folder = crate_ref.abs_path.join("src/tests");
        let test = serde_json::json!({
            "test": {
                "exclude": [ target_folder.to_string_lossy() ],
                "include": [ test_folder.to_string_lossy() ]
            }
        });

        // always ignored
        let config_path = crate_ref.abs_path.join("deno.json");

        let config = serde_json::json!({
            "exclude": [
                target_folder.to_string_lossy()
            ],
            "imports": imports,
            "fmt": fmt,
            "lint": lint,
            "compilerOptions": compiler_options,
            "test": test
        });

        mem_fs.write_json(&config_path, config).unwrap_or_else(|e| {
            panic!("Error writing {:?}: {:?}", config_path, e);
        });
    }
}

#[inline(always)]
pub fn deno_install(workspace: &'static crate::Workspace) {
    let status = call_deno::<_, &str>(workspace, "install", Vec::new());
    if !status.success() {
        panic!("`deno install` failed: {:?}, Args = ?", status);
    }
}
