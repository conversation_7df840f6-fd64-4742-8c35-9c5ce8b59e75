// TODO: switch to relative paths and allow tsconfig to be committed to the repo like any other file

use approck_memfs::MemFs;
use indexmap::IndexMap;
use serde_json::json;

pub(super) fn write_tsconfig_files(
    workspace: &'static crate::Workspace,
    mem_fs: &mut MemFs,
    _release: bool,
) {
    // iterate over references in pnpm crate names and update package.json and tsconfig.json
    // Weird, we are doing this for everything, not just the referenced crates... is that okay?
    for crate_ref in &workspace.get_typescript_crates() {
        let extended_crate_refs = workspace.get_extended_crate_refs(&crate_ref.extends);

        let esbuild_target = workspace
            .path
            .join("target/esbuild")
            .join(crate_ref.crate_name.to_string());

        let tsbuildinfo_file = esbuild_target.join("tsconfig.tsbuildinfo");

        let ts_config_path = crate_ref.abs_path.join("tsconfig.json");

        let mut paths = IndexMap::new();
        paths.insert("@crate/*".to_string(), vec!["./src/*".to_string()]);

        for extended_crate_ref in &extended_crate_refs {
            paths.insert(
                format!("@{}/*", extended_crate_ref.crate_name),
                vec![format!(
                    "{}/src/*",
                    extended_crate_ref.abs_path.to_string_lossy()
                )],
            );
        }

        let references = extended_crate_refs
            .iter()
            .map(|extended_crate_ref| json!({"path": extended_crate_ref.abs_path}))
            .collect::<Vec<_>>();

        let ts_config_json = json!({
            "compilerOptions": {
                "allowImportingTsExtensions": true,
                "allowUnreachableCode": false,
                "allowUnusedLabels": false,
                "checkJs": true,
                "composite": true,
                "esModuleInterop": true,
                "exactOptionalPropertyTypes": true,
                "forceConsistentCasingInFileNames": true,
                "isolatedModules": true,
                "lib": ["dom", "esnext"],
                "module": "esnext",
                "moduleResolution": "Bundler",
                "emitDeclarationOnly": true, // we don't need the output because we are using esbuild
                "noFallthroughCasesInSwitch": true,
                "noImplicitOverride": true,
                "noImplicitReturns": true,
                "noPropertyAccessFromIndexSignature": true,
                "noUncheckedIndexedAccess": true,
                "noUnusedLocals": true,
                "noUnusedParameters": true,
                "outDir": esbuild_target,
                "paths": paths,
                "rootDir": "./src",
                "skipLibCheck": true,
                "sourceMap": true,
                "strict": true,
                "target": "es2022",
                "tsBuildInfoFile": tsbuildinfo_file,
            },
            "exclude": ["tests"],
            "references": references
        });

        mem_fs
            .write_json(&ts_config_path, ts_config_json)
            .unwrap_or_else(|e| {
                panic!("Error writing tsconfig.json: {:?}", e);
            });
    }
}

pub(super) fn write_root_tsconfig_file(
    workspace: &'static crate::Workspace,
    mem_fs: &mut MemFs,
    _release: bool,
) {
    // handle root tsconfig.json
    {
        let extended_crate_refs = workspace.get_typescript_crates();
        let tsconfig_json_path = workspace.path.join("tsconfig.json");

        let ts_config_json = json!({
            "files": [],
            "compilerOptions": {
                "outDir": "./target/tsc"
            },
            "references": extended_crate_refs.iter().map(|extended_crate_ref| json!({"path": extended_crate_ref.rel_path})).collect::<Vec<_>>(),
        });

        mem_fs
            .write_json(&tsconfig_json_path, ts_config_json)
            .unwrap_or_else(|e| {
                panic!("Error writing {:?}: {:?}", tsconfig_json_path, e);
            });
    }
}
