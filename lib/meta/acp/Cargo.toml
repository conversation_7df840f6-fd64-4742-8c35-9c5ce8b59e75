[package]
name = "acp"
version = "0.1.32"
edition = "2024"

# 0.1.22: fixed up ./acp clean
# 0.1.23: run esbuild/codegen on all apps so we always have approck-generated
# 0.1.25: switched to mold linker
# 0.1.26: added noEmit and allowImportingTsExtensions to tsconfig
# 0.1.27: added Option<AuthBasic> and AuthBasic support
# 0.1.28: moved init tasks out to acp bash script
# 0.1.29: added PostJson types (struct,enum,type) to approck::http
# 0.1.30: added noUselessElse and others to biome.json
# 0.1.31: added es2022 as target
# 0.1.32: gtype macro


# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
acp-config = { workspace = true }
approck-compiler = { workspace = true }
approck-memfs = { workspace = true }
granite = { workspace = true }
granite-compiler = { workspace = true }
pgmig = { workspace = true }

aes-gcm = { workspace = true }
base64_light = { workspace = true }
boxcar = { workspace = true }
clap = { workspace = true, features = ["derive", "color"] }
git2 = { workspace = true }
ignore = { workspace = true }
indexmap = { workspace = true, features = ["serde"] }
itertools = { workspace = true }
lightningcss = { workspace = true, features = ["bundler"] }
petgraph = { workspace = true }
prettyplease = { workspace = true }
proc-macro2 = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json.workspace = true
serde_toml.workspace = true
serde_yaml.workspace = true
termcolor = { workspace = true }
tokio = { workspace = true, features = ["tokio-macros", "full"] }
toml = { workspace = true }
walkdir = { workspace = true }