#!/bin/bash

# change to the directory of this script
cd $(git rev-parse --show-toplevel) || exit 1

# if the first arg is `--help` or `-h`, print the help message
if [[ $1 == "init" ]]; then
    # fail on errors, echo commands
    set -ex

    rm -rf .cargo

    mkdir -p target
    touch target/version # Put empty version file in place so include_str! macro doesn't fail

    cargo run -p acp-init
fi

if [[ $1 == "write-version" ]]; then
    git log -1 > target/version
    git diff >> target/version
    exit 0
fi

if [[ $1 == "clean" ]]; then
    # fail on errors, echo commands
    set -e

    # Check if force flag is provided
    force=false
    if [[ $2 == "-f" ]]; then
        force=true
    fi

    # Check for untracked files
    if ! $force; then
        untracked=$(git ls-files --others --exclude-standard)
        if [[ -n "$untracked" ]]; then
            echo "Error: Untracked files present:"
            echo
            echo "$untracked"
            echo
            echo "Use './acp clean -f' if you really want to remove these files"
            exit 1
        fi
    fi

    # Create empty LOCAL.toml if it doesn't exist
    if [[ ! -f LOCAL.toml ]]; then
        echo "Creating empty LOCAL.toml"
        touch LOCAL.toml
    fi

    # Save LOCAL.toml content
    echo "Saving LOCAL.toml"
    mv LOCAL.toml /tmp/LOCAL.toml || {
        echo "Error: Failed to save LOCAL.toml"
        exit 1
    }

    # Remove target dir (git clean was having issues with this for some reason)
    rm -rf ./target
    
    # Run git clean
    echo "Running git clean"
    if git clean -fdx; then
        clean_status="success"
    else
        clean_status="failed"
    fi

    # Restore LOCAL.toml
    echo "Restoring LOCAL.toml"
    mv /tmp/LOCAL.toml LOCAL.toml || {
        echo "Error: Failed to restore LOCAL.toml. Your LOCAL.toml is in /tmp/LOCAL.toml"
        exit 1
    }

    # Output appropriate message based on clean status
    if [[ $clean_status == "success" ]]; then
        echo "cleaned!"
    else
        echo "clean failed!"
    fi
    echo "run \`./acp init\` to install acp"
    exit 0
fi

# if target/release/acp is not there and executable, then emit a message to run ./acp init
if [[ ! -x target/release/acp ]]; then
    echo "run \`./acp init\` to install acp"
    exit 1
fi

# run the local acp command
exec cargo run --release --quiet --package acp -- "$@"

