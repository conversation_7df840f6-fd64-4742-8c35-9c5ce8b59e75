use crate::Mark<PERSON>;

pub fn name_value(name: &str, value: Option<&str>) -> Markup {
    super::bux_input_text_string(name, None, value, None, false)
}

pub fn name_label_value(name: &str, label: &str, value: Option<&str>) -> Markup {
    super::bux_input_text_string(name, Some(label), value, None, false)
}

pub fn name_label_value_help(name: &str, label: &str, value: Option<&str>, help: &str) -> Markup {
    super::bux_input_text_string(name, Some(label), value, Some(help), false)
}

pub fn first_name() -> Markup {
    name_label_value("first_name", "First Name", None)
}

pub fn name_label_readonly(name: &str, label: &str, value: &str) -> Markup {
    super::bux_input_text_string(name, Some(label), Some(value), None, true)
}
