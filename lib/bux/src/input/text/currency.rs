use crate::{Mark<PERSON>, html};
use granite::Decimal;
use std::collections::HashMap;

pub fn name_value(name: &str, value: Option<Decimal>) -> Markup {
    html! {
        bux-input-text-currency name=(name) {
            input-group {
                span class="currency-symbol" { "$" }
                input type="text" name=(name) value=(value.map(crate::format_decimal_us_2).unwrap_or_default()) {}
            }
        }
    }
}

// Renders a basic currency input field with a label and a "$" symbol.
pub fn currency_input(name: &str, label: &str, value: Option<Decimal>) -> Markup {
    html! {
        bux-input-text-currency name=(name) {
            label { (label) }
            input-group {
                span class="currency-symbol" { "$" }
                input type="text" value=(value.map(crate::format_decimal_us_2).unwrap_or_default()) {}
            }
        }
    }
}

// Renders a currency input with an additional help tooltip/text.
pub fn currency_input_with_help(
    name: &str,
    label: &str,
    value: Option<Decimal>,
    help: &str,
) -> Markup {
    html! {
        bux-input-text-currency name=(name) help=(help) {
            label { (label) }
            input-group {
                span class="currency-symbol" { "$" }
                input type="text" name=(name) value=(value.map(crate::format_decimal_us_2).unwrap_or_default()) {}
            }
        }
    }
}

// Renders a currency input with help text that can highlight specific words in different colors.
pub fn currency_input_highlighted_help(
    name: &str,
    label: &str,
    value: Option<Decimal>,
    help: &str,
    highlights: HashMap<&str, &str>,
) -> Markup {
    let mut help_text = help.to_string();

    for (word, color) in highlights {
        let replacement = format!(
            "<span style=\"background-color:{};\">{}</span>",
            color, word
        );
        help_text = help_text.replace(word, &replacement);
    }

    html! {
        div {
            label { b { (label) } }
            bux-input-text-currency name=(name) {
                input-group {
                    span class="currency-symbol" { "$" }
                    input type="text" name=(name) value=(value.map(crate::format_decimal_us_2).unwrap_or_default()) {}
                }
            }
            small style="display:block;margin-bottom:1rem;margin-top:0;" {
                (maud::PreEscaped(help_text))
            }
        }
    }
}
