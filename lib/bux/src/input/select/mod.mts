import "./mod.mcss";

import { type NestedError, NEWID, type Option, type Result } from "@granite/lib.mts";
import { BuxInput } from "../mod.mts";

// sid, label, value
export type BuxInputSelectOption<T extends { toString(): string }> = {
    value: T;
    label: string;
};

abstract class BuxInputSelect<P extends { toString(): string }>
    extends BuxInput<P, string | undefined> {
    protected options: BuxInputSelectOption<P>[] = [];
    protected placeholder: string | null = "";

    private $select: HTMLSelectElement;
    private $label: HTMLLabelElement;
    private option_map: Map<string, P> = new Map();

    constructor() {
        super();
        this.$label = document.createElement("label") as HTMLLabelElement;
        this.$label.innerHTML = this.innerHTML;
        this.$select = document.createElement("select") as HTMLSelectElement;
    }

    connectedCallback(): void {
        this.innerHTML = "";

        const id = NEWID();
        this.$select.id = id;
        this.$label.htmlFor = id;

        if (this.placeholder !== null) {
            const option = document.createElement("option") as HTMLOptionElement;
            option.value = "";
            option.textContent = this.placeholder;
            this.$select.appendChild(option);
        }

        const value = this.getAttribute("value");

        for (const opt of this.options) {
            this.option_map.set(opt.value.toString(), opt.value);
            const option = document.createElement("option") as HTMLOptionElement;
            option.value = opt.value.toString();
            option.textContent = opt.label;

            if (value !== null && option.value === value) {
                option.selected = true;
            }

            this.$select.appendChild(option);
        }

        if (this.$label.textContent !== "") {
            this.appendChild(this.$label);
        }

        this.$select.addEventListener("change", this.event_on_change.bind(this));

        this.appendChild(this.$select);
    }

    disconnectedCallback(): void {
        this.$select.removeEventListener("change", this.event_on_change.bind(this));
    }

    protected event_on_change(): void {
        this.$select.setCustomValidity("");
        if (this.on_change) {
            const result = this.get();
            if ("Ok" in result) {
                this.on_change(result.Ok);
            } else {
                this.on_change(undefined);
            }
        }
    }

    public override get(): Result<P | undefined, string> {
        const value = this.option_map.get(this.$select.value);
        if (value === undefined) {
            return { Ok: undefined };
        }
        return { Ok: value };
    }

    public override set_p(value: P | undefined): void {
        if (value === undefined) {
            this.$select.value = "";
        } else {
            this.$select.value = value.toString();
        }
    }

    public override set_e<E>(
        value:
            | string
            | null
            | undefined
            | { Ok: string | undefined }
            | { Err: string | undefined }
            | { Some: string | undefined }
            | { None: string | undefined }
            | NestedError<E>,
    ): void {
        if (typeof value === "object" && value !== null && !Array.isArray(value)) {
            if ("Ok" in value) {
                value = value.Ok;
            } else if ("Err" in value) {
                value = value.Err;
            } else if ("Some" in value) {
                value = value.Some;
            } else if ("None" in value) {
                value = value.None;
            } else if ("Outer" in value) {
                value = value.Outer;
            }
        }

        if (typeof value === "string") {
            this.$select.setCustomValidity(value);
            this.$select.title = value;
        } else {
            this.$select.setCustomValidity("");
            this.$select.title = "";
        }
    }

    public override get_e(): string | undefined {
        return this.$select.validationMessage || undefined;
    }

    protected override on_attr_name(value: string): void {
        this.$select.name = value;
    }

    protected override on_attr_value(value: string): void {
        this.$select.value = value;
    }

    protected on_attr_help(_value: string | null): void {
        //TODO: implement
    }

    public get value_option(): Option<P> {
        const result = this.get();
        if ("Ok" in result && result.Ok !== undefined) {
            return { Some: result.Ok };
        }
        return { None: true };
    }

    public set value_option(value: Option<P>) {
        this.value = value;
    }
}

export default BuxInputSelect;
