use crate::{Mark<PERSON>, html};
use granite::Date;

pub fn name_value(name: &str, value: Option<Date>) -> Markup {
    html! {
        bux-input-date name=(name) value=(value.unwrap_or_default()) {
            input type="date" name=(name) value=[value] {}
        }
    }
}

pub fn bux_input_date(name: &str, label: &str, value: Option<Date>) -> Markup {
    html! {
        bux-input-date name=(name) value=[&value] {
            label { (label) }
            input type="date" name=(name) value=[value] {}
        }
    }
}

pub fn bux_input_date_with_help(
    name: &str,
    label: &str,
    value: Option<Date>,
    help: &str,
) -> Markup {
    html! {
        bux-input-date name=(name) value=[&value] help=(help) {
            label { (label) }
            input type="date" name=(name) value=[value] {}
            div.help-text {
                { (help) }
            }
        }
    }
}
