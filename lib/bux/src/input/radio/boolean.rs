use crate::{Mark<PERSON>, html};

/// Renders a basic Yes/No radio button group
pub fn radio_yesno(name: &str, value: bool) -> Markup {
    html! {
        bux-input-radio-boolean name=(name) flavor="YesNo" value=(value) {}
    }
}

/// Renders a Yes/No radio button group with a label
pub fn radio_yesno_label(name: &str, label: &str, value: bool) -> Markup {
    html! {
        bux-input-radio-boolean name=(name) flavor="YesNo" value=(value) {
            (label)
        }
    }
}

/// Renders a Yes/No radio button group with help text
pub fn radio_yesno_value_help(name: &str, value: bool, help: &str) -> Markup {
    html! {
        bux-input-radio-boolean name=(name) flavor="YesNo" value=(value) help=(help) {}
    }
}

/// Renders a Yes/No radio button group with both label and help text
pub fn radio_yesno_label_value_help(name: &str, label: &str, value: bool, help: &str) -> Markup {
    html! {
        bux-input-radio-boolean name=(name) flavor="YesNo" value=(value) help=(help) {
            (label)
        }
    }
}
