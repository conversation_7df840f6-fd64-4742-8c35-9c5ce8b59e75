use crate::{Mark<PERSON>, html};

pub fn name_value(name: &str, checked: bool) -> Markup {
    html! {
        bux-input-checkbox name=(name) checked=(checked) {}
    }
}

pub fn bux_checkbox(name: &str, label: &str, checked: bool) -> Markup {
    html! {
        bux-input-checkbox name=(name) checked=(checked) {
            (label)
        }
    }
}

pub fn bux_checkbox_value(
    name: &str,
    label: &str,
    value: &impl std::fmt::Display,
    checked: bool,
) -> Markup {
    html! {
        bux-input-checkbox name=(name) value=(value) checked=(checked) {
            (label)
        }
    }
}

pub fn bux_checkbox_with_help(name: &str, label: &str, checked: bool, help: &str) -> Markup {
    html! {
        bux-input-checkbox name=(name) checked=(checked) help=(help) {
            (label)
        }
    }
}

pub fn bux_checkbox_value_with_help(
    name: &str,
    label: &str,
    value: &impl std::fmt::Di<PERSON><PERSON>,
    checked: bool,
    help: &str,
) -> Markup {
    html! {
        bux-input-checkbox name=(name) value=(value) checked=(checked) help=(help) {
            (label)
        }
    }
}
