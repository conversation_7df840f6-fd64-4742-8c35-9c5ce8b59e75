import "./date.mcss";

import { type Option, type Result } from "@granite/lib.mts";
import { NEWID } from "@granite/lib.mts";
import { BuxInput } from "./mod.mts";

class BuxInputDate extends BuxInput<Date | null, string | undefined> {
    private $input: HTMLInputElement;
    private $label: HTMLLabelElement | null = null;

    constructor() {
        super();
        this.$label = this.querySelector("label") as HTMLLabelElement | null;
        this.$input = this.querySelector("input") as HTMLInputElement;
        this.$input.type = "date";

        // feed initial value in through standard mechanism
        if (this.hasAttribute("value")) {
            this.attributeChangedCallback("value", "", this.getAttribute("value") || "");
        }
    }

    connectedCallback(): void {
        const id = NEWID();
        this.$input.id = id;
        if (this.$label) {
            this.$label.htmlFor = id;
        }

        this.$input.addEventListener("input", this.event_on_input.bind(this));
        this.$input.addEventListener("change", this.event_on_change.bind(this));
    }

    disconnectedCallback(): void {
        this.$input.removeEventListener("input", this.event_on_input.bind(this));
        this.$input.removeEventListener("change", this.event_on_change.bind(this));
    }

    on_attr_name(value: string): void {
        this.$input.name = value;
    }

    on_attr_value(value: string): void {
        try {
            const date = new Date(value);
            this.set_p(date);
        } catch (e) {
            console.error(e);
            this.set_p(null);
        }
    }

    on_attr_help(_value: string | null): void {
        //TODO: implement
    }

    // clear custom messages while typing
    private event_on_input(): void {
        this.$input.setCustomValidity("");
    }

    // revalidate once the user has finished typing
    private event_on_change(): void {
        const result = this.get();

        if ("Ok" in result) {
            this.set_e(undefined);
        } else {
            this.set_e(result.Err);
        }

        if (this.on_change) {
            if ("Ok" in result) {
                this.on_change(result.Ok);
            } else {
                this.on_change(null);
            }
        }
    }

    /// Calling .value_result will return a Result<T, string> which you can manually check .is_ok on
    public override get(): Result<Date | null, string> {
        const value = this.$input.value.trim();
        if (value === "") {
            return { Ok: null };
        }
        try {
            const date = new Date(value);
            return { Ok: date };
        } catch (_e) {
            return { Err: "invalid date input" };
        }
    }

    /// Setting undefined has zero effect on the field.
    public override set_p(value: Date | null) {
        if (value === null) {
            this.$input.value = "";
        } else {
            this.$input.value = value.toISOString().substring(0, 10);
        }
    }

    public override set_e(value: string | null | undefined): void {
        this.$input.setCustomValidity(value || "");
        this.$input.title = value || "";
    }

    public override get_e(): string | undefined {
        return this.$input.validationMessage || undefined;
    }

    // add a value_option getter
    public get value_option(): Option<Date> {
        const result = this.get();
        if ("Ok" in result && result.Ok !== undefined && result.Ok !== null) {
            return { Some: result.Ok };
        }
        return { None: true };
    }
}

globalThis.customElements.define("bux-input-date", BuxInputDate);
export default BuxInputDate;
