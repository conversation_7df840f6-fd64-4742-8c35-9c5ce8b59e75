import "./mod.mcss";
import "../lib.mcss";

import "@approck/app_socket.mts";

import { InfoBox } from "@crate/floater/info_box.mts";
import { SearchSimple } from "@crate/floater/search_simple.mts";

// Example would be `#info:...`
const ACTION_MATCH_HREF = /^#([a-zA-Z][a-zA-Z0-9]*):(.*)$/;

/// Wraps the document object and provides a simple API for interacting with the DOM specifically
/// with items that need to be handled globally.
/// It is a singleton, exposed as bux_document.
export abstract class BuxDocument {
    /// If this is set, then any click that is not inside of it should result in closing it automatically
    private active_info_box_to_close_on_click: InfoBox | null = null;
    protected $body = document.body as HTMLBodyElement;
    protected $gear: HTMLElement | null = document.querySelector("#gear-icon");
    protected $toggleHorizontalNav: HTMLAnchorElement | null = document.getElementById(
        "toggle-horizontal-nav",
    ) as HTMLAnchorElement;
    protected $toggleVerticalNav: HTMLAnchorElement = document.getElementById(
        "toggle-vertical-nav",
    ) as HTMLAnchorElement;
    protected $dropdownToggles: NodeListOf<HTMLElement> = document.querySelectorAll(
        '.dropdown-menu-toggle[data-toggle="dropdown"]',
    );
    protected $back_button: HTMLAnchorElement = document.getElementById(
        "bux-back-button",
    ) as HTMLAnchorElement;
    protected $back_menu: HTMLUListElement = document.getElementById(
        "bux-back-menu",
    ) as HTMLUListElement;
    protected $bux_document_exception: HTMLElement | null = document.getElementById(
        "bux-document-exception",
    );

    constructor() {
        // global click handler
        // TODO: This needs proper integration into bux api
        //this.$body.addEventListener("click", (event) => this.on_body_click(event));
        //if (this.$gear) {
        //    this.$gear.addEventListener("click", (event) => this.on_search_simple_click(event));
        //}

        for (let i = 0; i < this.$dropdownToggles.length; i++) {
            const toggle = this.$dropdownToggles[i] as HTMLElement; // Cast each item as HTMLElement if necessary
            toggle.addEventListener("click", (event) => {
                event.preventDefault();
                const dropdownMenu = toggle.nextElementSibling as HTMLElement;

                if (dropdownMenu) {
                    const isExpanded = toggle.getAttribute("aria-expanded") === "true";
                    // Pass the dropdown menu if it is not currently expanded, otherwise pass null
                    this.close_all_dropdowns(isExpanded ? null : dropdownMenu);

                    if (!isExpanded) {
                        dropdownMenu.classList.add("show");
                        toggle.setAttribute("aria-expanded", "true");
                    } else {
                        dropdownMenu.classList.remove("show");
                        toggle.setAttribute("aria-expanded", "false");
                    }
                } else {
                    console.error("Dropdown menu not found!");
                }
            });
        }

        document.addEventListener("click", (event) => {
            const target = event.target as Element; // Type assertion to Element
            if (!target.matches(".dropdown-menu-toggle, .dropdown-menu-toggle *")) {
                this.close_all_dropdowns();
            }
        });

        // Listen for the 'Esc' key to close all dropdowns
        document.addEventListener("keydown", (event) => {
            if (event.key === "Escape") {
                this.close_all_dropdowns();
            }
        });

        globalThis.addEventListener("error", (event: ErrorEvent) => {
            if (this.$bux_document_exception) {
                this.$bux_document_exception.textContent =
                    `There was an unexpected problem with the application.  You may need to refresh the page. Here are the details: ${event.message}. ${event.filename}:${event.lineno}:${event.colno} ${event.timeStamp}`;
                // You might want to add more details from event.error if available
                // For example: this.$bux_document_exception.textContent += `\nStack: ${event.error?.stack}`;
            }
        });

        globalThis.addEventListener("unhandledrejection", (event: PromiseRejectionEvent) => {
            if (this.$bux_document_exception) {
                this.$bux_document_exception.textContent =
                    `There was an unexpected problem with the application.  You may need to refresh the page. Here are the details: ${event.reason}. ${event.timeStamp}`;
            }
        });
    }

    protected on_search_simple_click(event: MouseEvent) {
        const toolbox = new SearchSimple({ $trigger: event.target as HTMLElement });
        toolbox.show();
    }

    /// When the body is clicked, this function is called to evaluate the bux-document-action attribute
    protected on_body_click(event: MouseEvent) {
        const $target = event.target as HTMLElement;

        const href = $target.getAttribute("href");

        // if there is an active info box to close on click, then close it
        if (
            this.active_info_box_to_close_on_click !== null && $target.closest(".InfoBox") === null
        ) {
            // this should have the effect of setting active_info_box_to_close_on_click to null within the close handler
            this.active_info_box_to_close_on_click.close();
        }

        // outer if is for efficiency
        if (href?.startsWith("#")) {
            // eg: #verb:<data>
            const match = href.match(ACTION_MATCH_HREF);
            if (match !== null) {
                event.preventDefault();
                this.on_action({
                    $trigger: $target,
                    title: $target.getAttribute("title") || undefined,
                    //biome-ignore lint/style/noNonNullAssertion: regex has 2 groups
                    verb: match[1]!,
                    //biome-ignore lint/style/noNonNullAssertion: regex has 2 groups
                    data: match[2]!,
                });
            }
        }
    }

    protected on_action(action: {
        $trigger: HTMLElement;
        title?: string | undefined;
        verb: string;
        data: string;
    }) {
        switch (action.verb) {
            // If the href = `#info:topic`, then:
            //   `action.verb = "info"`
            //   `action.data = "topic"`
            case "info":
                this.on_action_info({
                    $trigger: action.$trigger,
                    title: action.title,
                    topic: action.data,
                });
                break;

            default:
                console.error("Unknown action verb", action);
        }
    }

    /// only call the super class if the topic is not recognized
    protected on_action_info(opts: {
        $trigger: HTMLElement;
        title?: string | undefined;
        topic: string;
    }) {
        console.error("Unknown topic", opts);
    }

    /// function to show an InfoBox with the given html content near the target
    /// other info boxes will be destroyed
    protected show_info_box(opts: {
        $trigger: HTMLElement;
        title?: string | undefined;
        html?: string | undefined;
        text?: string | undefined;
    }) {
        if (this.active_info_box_to_close_on_click !== null) {
            this.active_info_box_to_close_on_click.close();
        }

        if (!opts.title) {
            opts.title = "Information";
        }

        this.active_info_box_to_close_on_click = new InfoBox({
            $trigger: opts.$trigger,
            title: opts.title,
            html: opts.html,
            text: opts.text,
            on_close: () => {
                this.active_info_box_to_close_on_click = null;
            },
        });

        this.active_info_box_to_close_on_click.show();
    }

    protected close_all_dropdowns(exceptMenu?: HTMLElement | null) {
        const dropdownMenus = document.querySelectorAll(".dropdown-menu-list");

        for (let i = 0; i < dropdownMenus.length; i++) {
            const menu = dropdownMenus[i] as HTMLElement;
            if (menu !== exceptMenu) {
                menu.classList.remove("show");
                const toggle = menu.previousElementSibling as HTMLElement;
                if (toggle?.classList.contains("dropdown-menu-toggle")) {
                    toggle.setAttribute("aria-expanded", "false");
                }
            }
        }
    }
}
