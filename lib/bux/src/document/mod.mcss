
:root {
    /* header bar */
    --bux-hb-bg-color: #B2B3D5;
    --bux-hb-padding: 0.5rem 0.75rem;

    /* nav-wrapper */
    --bux-nav-wrap-bg-color: #333;
    --bux-nav-wrap-padding: 0.5rem 0;

    /* navigation */
    --bux-nav-a-color: #fff;
    --bux-nav-a-hover-color: #fff;
    --bux-nav-a-border: none;
    --bux-nav-a-br: 0;
    --bux-nav-a-lh: inherit;
    --bux-nav-a-bg-color: transparent;
    --bux-nav-a-hover-bg-color: transparent;
    --bux-nav-a-bg-image: none;
    --bux-nav-a-hover-bg-image: none;

    /* page navigation */
    --bux-page-nav-bg-color: #e4e6e7;
    --bux-page-nav-border-bottom: 1px solid #c4c7ca;
    --bux-page-nav-a-color: rgb(78, 78, 255);
    --bux-page-nav-a-hover-color: #f00;
    --bux-page-nav-a-bg-color: #fff;
    --bux-page-nav-a-border: 1px solid #fff;
    --bux-page-nav-a-selected-bg-color: #707070;
    --bux-page-nav-a-selected-border-color: #707070;

    /* footer */
    --bux-footer-bg-color: #e4e6e7;
    --bux-footer-color: #000;
    --bux-footer-social-color: #6b6b6b;
}

/*********************************************************/
/* Styles needed to create a sticky footer */

html, body {
    height: 100%;
}

/* This contains everything inside of the body */
layout-wrapper-outer {
    display: flex;
    flex-direction: column;
    min-height: 100%;

    /* 
    This contains everything excluding the footer element. 
    It pushes the footer to the bottom of the screen.
    */ 
    layout-wrapper-inner {
        flex: 1 0 auto;
    }
}

/*********************************************************/
/* Menu alignment modifier classes */

.nav-dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/*********************************************************/
/* Header bar styles */

header-bar {
    display: block;
    padding: var(--bux-hb-padding);
    background-color: var(--bux-hb-bg-color);

    &.disabled {
        display: none;
    }
}

/*********************************************************/
/* Base styles for the primary/secondary navigation */

nav-wrapper {
    display: block;
    background-color: var(--bux-nav-wrap-bg-color);
    padding: var(--bux-nav-wrap-padding);

    content-container {

        @media (min-width: 992px) {
            display: grid;
            grid-template-columns: max-content auto auto;
            align-items: center;
            column-gap: 1rem;
        }

        > nav-header {
            
            a {
                img {
                    height: 30px;
                    width: auto;
                }
            }

            @media (min-width: 992px) {
                grid-area: 1 / 1 / 2 / 2;
            }
        }

        nav {

            ul.nav-menu {
                padding-left: 0;
                margin-bottom: 0;
                list-style-type: none;

                li.menu-item, 
                li.menu-item-dropdown {

                    a.menu-link, 
                    a.dropdown-menu-toggle {
                        text-decoration: none;
                        display: block;
                        padding: 0.5rem 0;
                        color: var(--bux-nav-a-color);
                        border: var(--bux-nav-a-border);
                        border-radius: var(--bux-nav-a-br);
                        background-color: var(--bux-nav-a-bg-color);
                        background-image: var(--bux-nav-a-bg-image);
                        line-height: var(--bux-nav-a-lh);

                        @media (min-width: 992px) {
                            padding: .375rem .75rem;
                        }

                        &:hover {
                            background-color: var(--bux-nav-a-hover-bg-color);
                            background-image: var(--bux-nav-a-hover-bg-image);
                            color: var(--bux-nav-a-hover-color);
                        }
                    }
                }

                li.menu-item-dropdown {

                    a.dropdown-menu-toggle {
                        display: flex;
                        align-items: center;
                        column-gap: 0.5rem;

                        @media (min-width: 992px) {
                            display: block;
                        }

                        /* Add an icon to signify a dropdown menu */
                        &::after {
                            content: "\f078";
                            font-family: "Font Awesome 5 Free";
                            font-weight: 900; /* Font weight for solid Font Awesome icons */
                            font-size: 8pt;
                            margin-left: auto;
                        }

                        @media (min-width: 992px) {
                            &::after {
                                margin-left: 0.5rem;
                            }
                        }

                        img {
                            border-radius: 25px;
                            height: 20px;
                            width: auto;
                        }

                        @media (min-width: 992px) {
                            img {
                                height: 25px;
                                margin-right: 0.5rem;
                            }
                        }
                    }

                    ul.dropdown-menu-list {
                        display: none;
                        background-color: #fff;
                        border: 1px solid rgba(0, 0, 0, 0.15);
                        border-radius: 0.25rem;
                        padding: 0.5rem 0;
                        text-align: left;
                        list-style-type: none;

                        li.dropdown-menu-item {

                            a.dropdown-menu-link, span {
                                text-decoration: none;
                                padding: .5rem 1rem;
                                color: #212529;
                                font-size: 1rem;
                                font-weight: 500;
                                display: block;

                                h6, p {
                                    margin-bottom: 0;
                                }

                                p {
                                    font-weight: normal;
                                    font-size: 11pt;
                                    margin-top: 0.25rem;
                                }
                            }

                            a.dropdown-menu-link:hover {
                                color: #1e2125;
                                background-color: #e9ecef;
                            }

                            &:has(> dropdown-header) {
                                display: none; /* Hide the dropdown header by default*/
                            }
                        }

                        @media (min-width: 992px) {
                            position: absolute;
                            z-index: 1000;
                            left: 0;
                            right: auto;
                            top: 100%;
                            min-width: 18rem;
                        }
                    }

                    ul.dropdown-menu-list.show {
                        display: block;
                    }

                    @media (min-width: 992px) {
                        position: relative;
                    }
                }

                li.menu-item-dropdown#user-menu {

                    a.nav-dropdown-toggle {
                        white-space: nowrap;
                    }
                }

                @media (min-width: 992px) {
                    li.menu-item,
                    li.menu-item-dropdown {
                        display: inline-block; /* Positions the top level menu items side by side */
                    }
                }
            }
        }

        @media (min-width: 992px) {
            > nav#primary-navigation {
                grid-area: 1 / 2 / 2 / 3;
            }

            > nav#secondary-navigation {
                grid-area: 1 / 3 / 2 / 4;
            }
        }
    }
}

/*********************************************************/
/* Styles specific to the secondary navigation */

nav#secondary-navigation {

    @media (min-width: 992px) {
        margin-left: auto; /* Align the secondary navigation to the right */
        display: flex;
        align-items: center;
    }
}

/*********************************************************/
/* These styles control the nav-header display */

nav-header#horizontal-nav-header {
    /* Show by default */
    display: block; 
}

nav-header#vertical-nav-header {
    /* Hide by default */
    display: none;
}

/*********************************************************/
/* Admin Toolbox */

nav#secondary-navigation {

    ul.nav-menu{

        li.menu-item#admin-tools {

            &.disabled {
                display: none;
            }
        }
    }
}

/*********************************************************/
/* Page Navigation */

page-nav-wrapper {
    display: block;
    padding: 1rem 0;
    background-color: var(--bux-page-nav-bg-color);
    border-bottom: var(--bux-page-nav-border-bottom);
    
    content-container {
        display: flex;
        justify-content: space-between;
        gap: 1rem;
        align-items: flex-start;

        nav#page-navigation {
        
            ul {
                padding-left: 0;
                margin-bottom: 0;
                display: flex;
                flex-wrap: wrap;
                gap: .5rem;
        
                li {
                    list-style-type: none;
                    display: inline-block;
                    vertical-align: middle;
        
                    a {
                        text-decoration: underline;
                        color: var(--bux-page-nav-a-color);
                        font-weight: 500;
                        display: block;
                        background-color: var(--bux-page-nav-a-bg-color);
                        border: var(--bux-page-nav-a-border);
                        padding: .25rem .5rem;
                        font-size: .875rem;
                        border-radius: .375rem;
                        
                        u::before {
                            content: " 🞂 ";
                        }

                        &.selected {
                            background-color: var(--bux-page-nav-a-selected-bg-color);
                            border-color: var(--bux-page-nav-a-selected-border-color);
                            color: #fff;
                        }

                    }

                    a:hover {
                        color: var(--bux-page-nav-a-hover-color);
                    }
                }
            }
        }

        button-wrapper {
            margin-left: auto;
            display: flex;
            gap: .25rem;
        }
    }
}

/*********************************************************/
/* Footer */

body > layout-wrapper-outer > footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 1.5rem;
    background-color: var(--bux-footer-bg-color);
    padding: 2rem;
    text-align: center;
    border-top: 1px solid #c4c7ca;

    ul#footer-socials {
        padding-left: 0;
        margin: 0;
        list-style-type: none;
        display: flex;
        align-items: center;
        column-gap: 1rem;
        justify-content: center;

        li {
            display: inline-block;

            a {
                color: var(--bux-footer-social-color);
                font-size: 10pt;
            }
        }

        &.disabled {
            display: none;
        }
    }

    ul#footer-links {
        padding-left: 0;
        margin: 0;
        list-style-type: none;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 0.5rem;

        li {
            display: inline-block;

            a {
                color: var(--bux-footer-color);
            }
        }

        &.disabled {
            display: none;
        }

        @media (min-width: 768px) {
            flex-direction: row;
            gap: 1.5rem;
        }
    }

    p#footer-copyright {
        margin-bottom: 0;
        color: var(--bux-footer-color);
    }
}

/*********************************************************/
/* Bux Panel Components */

panel.bux-form-panel {
    
    header {
        h5 {
            text-align: center;
        }
    }

    content {
        /* Used for data lists */
        dl {
            dt {
                font-size: 0.875em;
                font-weight: normal;
            }

            dd, dd * {
                font-size: 14pt;
                font-weight: normal;
            }

            dd {
                border-bottom: 1px solid #ccc;
                padding-bottom: 1rem;
                margin-bottom: 1rem;
            }
        }
    }
}

/*********************************************************/
/* Link styles */
a.inline-action {
    display: inline-block;
    padding-left: 0.5em;
    font-size: 10pt;
}

/*********************************************************/
/* Content container and responsive classes */

/* 
This is the default, responsive, fixed width container.
Its max-width changes at each breakpoint.
*/
content-container {
    display: block;
    width: 100%;
    padding-right: 1rem;
    padding-left: 1rem;
    margin-right: auto;
    margin-left: auto;

    @media (min-width: 576px) {
        max-width: 540px;
    }

    @media (min-width: 768px) {
        max-width: 720px;
    }

    @media (min-width: 992px) {
        max-width: 960px;
    }

    @media (min-width: 1200px) {
        max-width: 1140px;
    }

    @media (min-width: 1400px) {
        max-width: 1320px;
    }

    /* Full width container at all breakpoints */
    &.fluid {
        max-width: 100%;
    }
}

/* Responsive content container classes */

content-container.sm {
    max-width: 100%;

    /* 100% wide up to the small (576px) breakpoint */
    @media (min-width: 576px) {
        max-width: 540px;
    }

    @media (min-width: 768px) {
        max-width: 720px;
    }

    @media (min-width: 992px) {
        max-width: 960px;
    }
    
    @media (min-width: 1200px) {
        max-width: 1140px;
    }

    @media (min-width: 1400px) {
        max-width: 1320px;
    }
}

content-container.md {
    max-width: 100%;

    /* 100% wide up to the medium (768px) breakpoint */
    @media (min-width: 768px) {
        max-width: 720px;
    }

    @media (min-width: 992px) {
        max-width: 960px;
    }

    @media (min-width: 1200px) {
        max-width: 1140px;
    }

    @media (min-width: 1400px) {
        max-width: 1320px;
    }
}

content-container.lg {
    max-width: 100%;

    /* 100% wide up to the large (992px) breakpoint */
    @media (min-width: 992px) {
        max-width: 960px;
    }

    @media (min-width: 1200px) {
        max-width: 1140px;
    }

    @media (min-width: 1400px) {
        max-width: 1320px;
    }
}

content-container.xl {
    max-width: 100%;

    /* 100% wide up to the x-large (1200px) breakpoint */
    @media (min-width: 1200px) {
        max-width: 1140px;
    }

    @media (min-width: 1400px) {
        max-width: 1320px;
    }
}

content-container.xxl {
    max-width: 100%;

    /* 100% wide up to the xx-large (1400px) breakpoint */
    @media (min-width: 1400px) {
        max-width: 1320px;
    }
}

/*
Need to target content-container as a direct child of layout-wrapper-inner
in order to avoid affecting content-container within other layout components.
This padding replaces the bottom margin on the nav-wrapper and the top margin
on the footer.
*/
layout-wrapper-inner > content-container {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

#bux-document-exception:not(:empty) {
    display: block;
    position: fixed;
    bottom: 1em;
    left: 1em;
    right: 1em;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    padding: 1rem;
    text-align: center;
    z-index: 100000;
    box-shadow: 0 0 2em #000000ff;
    border-radius: 1em;
}

#bux-document-exception {
    display: none; /* Hidden by default when empty */
}

/*********************************************************/

contact {
    name {
        display: block;
        font-size: 1rem;
        font-weight: 500;
    }

    title {
        display: block;
        font-size: 0.9rem;
    }

    company {
        display: block;
        font-size: 0.9rem;
    }
}

