use maud::{Mark<PERSON>, html};

// Use one of these helper functions when you need a link that looks like a button.

// Icon only
pub fn icon(label: &str, icon: &str, href: &str) -> Markup {
    html! {
        a.btn href=(href) aria-label=(label) {
            i.(icon) aria-hidden="true" {}
        }
    }
}

// Icon and class
pub fn icon_class(label: &str, icon: &str, href: &str, css_class: &str) -> Markup {
    html! {
        a.{"btn " (css_class)} href=(href) aria-label=(label) {
            i.(icon) aria-hidden="true" {}
        }
    }
}

// Label only
pub fn label(label: &str, href: &str) -> Markup {
    html! {
        a.btn href=(href) {
            span { (label) }
        }
    }
}

// Label and class
pub fn label_class(label: &str, href: &str, css_class: &str) -> Markup {
    html! {
        a.{"btn " (css_class)} href=(href) {
            span { (label) }
        }
    }
}

// Label and icon
pub fn label_icon(label: &str, icon: &str, href: &str) -> Markup {
    html! {
        a.btn href=(href) {
            i.(icon) aria-hidden="true" {}
            " "
            span { (label) }
        }
    }
}

// Label, icon, and class
pub fn label_icon_class(label: &str, icon: &str, href: &str, css_class: &str) -> Markup {
    html! {
        a.{"btn " (css_class)} href=(href) {
            i.(icon) aria-hidden="true" {}
            " "
            span { (label) }
        }
    }
}

// Cancel
// Use this when you need to cancel an action, such as a form submission.
pub fn cancel(href: &str) -> Markup {
    html! {
        a.btn.bux-button-cancel href=(href) {
            i.fas.fa-times aria-hidden="true" {}
            " "
            span { "Cancel" }
        }
    }
}

// Details
// Use this when you need to link to a details screen.
pub fn details(href: &str) -> Markup {
    html! {
        a.btn href=(href) {
            i.fas.fa-chevron-right aria-hidden="true" {}
            " "
            span { "Details" }
        }
    }
}

// Add
// Use this when you need to link to an add screen.
pub fn add(href: &str) -> Markup {
    html! {
        a.btn href=(href) {
            i.fas.fa-plus aria-hidden="true" {}
            " "
            span { "Add" }
        }
    }
}

// Edit
// Use this when you need to link to an edit screen.
pub fn edit(href: &str) -> Markup {
    html! {
        a.btn href=(href) {
            i.fas.fa-pencil-alt aria-hidden="true" {}
            " "
            span { "Edit" }
        }
    }
}

// Delete
// Use this when you need to link to a delete screen.
pub fn delete(href: &str) -> Markup {
    html! {
        a.btn.danger href=(href) {
            i.far.fa-trash-alt aria-hidden="true" {}
            " "
            span { "Delete" }
        }
    }
}

// Go Back
// Use this when you need to link to the previous screen.
pub fn go_back(href: &str) -> Markup {
    html! {
        a.btn.primary href=(href) {
            i.fas.fa-arrow-left aria-hidden="true" {}
            " "
            span { "Go Back" }
        }
    }
}
