use maud::{Ren<PERSON>, html};

pub mod link;
pub mod submit;

pub enum Button {
    Link {
        href: Option<String>,
        aria_label: Option<String>,
        label: Option<String>,
        fa_icon_class: Option<String>,
        class: Option<String>,
        id: Option<String>,
    },
    Submit {
        aria_label: Option<String>,
        label: Option<String>,
        fa_icon_class: Option<String>,
        class: Option<String>,
        id: Option<String>,
    },
    SaveLink {
        href: String,
    },
    CancelLink {
        href: String,
    },
}

impl Render for Button {
    fn render(&self) -> maud::Markup {
        match self {
            Button::Link {
                href,
                aria_label,
                label,
                fa_icon_class,
                class,
                id,
            } => {
                html! {
                    a id=[id] href=[href] class=[class] aria-label=[aria_label] {
                        @if let Some(fa_icon_class) = fa_icon_class {
                            i class=(fa_icon_class) aria-hidden="true" {}
                        }
                        @if fa_icon_class.is_some() && label.is_some() {
                            " "
                        }
                        @if let Some(label) = label {
                            span { (label) }
                        }
                    }
                }
            }
            Button::Submit {
                aria_label,
                label,
                fa_icon_class,
                class,
                id,
            } => {
                html! {
                    button id=[id] class=[class] type="submit" aria-label=[aria_label] {
                        @if let Some(fa_icon_class) = fa_icon_class {
                            i class=(fa_icon_class) aria-hidden="true" {}
                        }
                        @if fa_icon_class.is_some() && label.is_some() {
                            " "
                        }
                        @if let Some(label) = label {
                            span { (label) }
                        }
                    }
                }
            }
            Button::SaveLink { href } => Button::Link {
                href: Some(href.to_string()),
                aria_label: Some("Save".to_string()),
                label: Some("Save".to_string()),
                fa_icon_class: Some("fas fa-check".to_string()),
                class: Some("btn primary".to_string()),
                id: None,
            }
            .render(),

            Button::CancelLink { href } => Button::Link {
                href: Some(href.to_string()),
                aria_label: Some("Cancel".to_string()),
                label: Some("Cancel".to_string()),
                fa_icon_class: Some("fas fa-times".to_string()),
                class: Some("btn".to_string()),
                id: None,
            }
            .render(),
        }
    }
}
