// Navigation stack type definitions
type StackItem = {
    pathname: string;
    title: string;
};

// The key used to store the navigation stack in sessionStorage
const STORAGE_KEY = "bux-document-navigationstack";

// Maximum number of URLs to keep in the navigation stack
const STACK_SIZE = 20;

// Array of pathname strings representing the navigation history
const STACK: StackItem[] = [];

// Saves the current navigation stack to sessionStorage.
// Automatically trims the stack to the maximum size before saving.
function save(): void {
    // Limit the stack to the configured stack size
    if (STACK.length > STACK_SIZE) {
        STACK.splice(0, STACK.length - STACK_SIZE);
    }
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(STACK));
}

// Adds a pathname to the navigation stack.
// If the pathname already exists in the stack, it is removed first to avoid duplicates.
// The pathname is then added to the end of the stack.
// Automatically trims the stack if it exceeds the maximum size.
function push(pathname: string, title: string): void {
    // Find and remove existing entry with same pathname
    const existingIndex = STACK.findIndex((item) => item.pathname === pathname);
    if (existingIndex !== -1) {
        STACK.splice(existingIndex, 1);
    }

    // Add new item to the end
    STACK.push({ pathname, title });

    // Trim if needed
    if (STACK.length > STACK_SIZE) {
        STACK.splice(0, STACK.length - STACK_SIZE);
    }
}

// Removes the most recent pathname from the navigation stack and returns it
function pop(): StackItem | undefined {
    return STACK.pop();
}

// Navigates back to the previous URL in the navigation stack.
// Removes the current URL from the stack and navigates to the previous one.
// The updated stack is automatically saved to sessionStorage.
export function go_back(default_pathname: string = "/"): void {
    // pop the current page off the stack
    pop();

    // pop the previous page off the stack, capturing it.
    // This is so if it is not found, it won't be tried again
    const pathname = pop()?.pathname ?? default_pathname;

    // Save
    save();

    // redirect
    window.location.href = pathname;
}

// On add type pages, will go to the details page or next page while removing the current page from the stack
export function go_next(pathname: string): void {
    pop();
    save();
    window.location.href = pathname;
}

// Return all items in the stack except the last one in reverse order
export function* items(): Generator<StackItem> {
    for (const item of [...STACK].slice(0, STACK.length - 1).reverse()) {
        // copy the return so modifications don't affect the stack
        yield { ...item };
    }
}

// Initialize the navigation stack when the module is loaded
{
    // Load from storage if possible
    const json = sessionStorage.getItem(STORAGE_KEY);
    if (json) {
        let parsed_stack;
        try {
            parsed_stack = JSON.parse(json);
        } catch (e) {
            console.error("Error parsing nav stack", e);
        }

        if (
            typeof parsed_stack === "object" && parsed_stack !== null && Array.isArray(parsed_stack)
        ) {
            for (const item of parsed_stack) {
                if (typeof item === "object" && item !== null && !Array.isArray(item)) {
                    if (
                        typeof item.pathname === "string" && typeof item.title === "string"
                    ) {
                        STACK.push({ pathname: item.pathname, title: item.title });
                    }
                }
            }
        }
    }

    push(globalThis.location.pathname, document.title);
    save();

    const $back_button = document.getElementById("bux-back-button") as HTMLAnchorElement | null;
    const $back_menu = document.getElementById("bux-back-menu") as HTMLUListElement | null;

    if ($back_button) {
        $back_button.addEventListener("click", (event) => {
            event.preventDefault();
            go_back();
        });
    } else {
        console.error("#bux-back-button not found");
    }

    if ($back_menu) {
        for (const item of items()) {
            const li = document.createElement("li");
            const a = document.createElement("a");
            a.href = item.pathname;
            a.textContent = item.title;
            li.appendChild(a);
            $back_menu.appendChild(li);
        }
    } else {
        console.error("#bux-back-menu not found");
    }
}
