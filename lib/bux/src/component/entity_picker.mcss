bux-component-entity-picker {
    h2 {
        font-size: 20px;
        color: #555;
        margin-bottom: 0.75rem;
    }

    h3 {
        font-size: 18px;
        font-weight: bold;
        color: #444;
    }

    ul {
        list-style-type: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 8px;

        li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #fff;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;

            .entity-info {
                display: flex;
                align-items: center;
                gap: 12px;
                flex: 1;
                text-align: center;
            }

            .entity-icon {
                font-size: 24px;
                min-width: 30px;
                text-align: center;
            }

            .entity-role {
                font-size: 12px;
                color: #666;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                min-width: 60px;
                text-align: center;
            }

            .entity-name {
                text-decoration: none;
                color: #007bff;
                font-weight: bold;
                font-size: 18px;
                flex: 1;
                text-align: left;

                &:hover {
                    text-decoration: underline;
                }
            }

            .entity-uuid {
                font-size: 12px;
                color: #999;
                font-family: monospace;
                margin-left: 12px;
                flex-shrink: 0;
            }
        }
    }
}