use crate::{<PERSON><PERSON>, <PERSON><PERSON>, html};

pub struct EntityPicker<'a> {
    pub heading: &'a str,
    pub entities: Vec<Entity<'a>>,
}

pub struct Entity<'a> {
    pub icon: &'a str,
    pub name: &'a str,
    pub href: &'a str,
    pub role: &'a str,
    pub uuid: &'a str,
}

pub fn new<'a>() -> EntityPicker<'a> {
    EntityPicker {
        heading: "Select Entity",
        entities: Vec::new(),
    }
}

impl<'a> EntityPicker<'a> {
    pub fn append(
        &mut self,
        icon: &'a str,
        name: &'a str,
        href: &'a str,
        role: &'a str,
        uuid: &'a str,
    ) {
        self.entities.push(Entity {
            icon,
            name,
            href,
            role,
            uuid,
        });
    }

    pub fn set_heading(&mut self, heading: &'a str) {
        self.heading = heading;
    }
}

impl Render for EntityPicker<'_> {
    fn render(&self) -> Markup {
        html! {
            bux-component-entity-picker {
                h2 { (self.heading) }
                ul {
                    @for entity in &self.entities {
                        li {
                            span.entity-info {
                                span.entity-icon { (entity.icon) }
                                span.entity-role { (entity.role) }
                                a.entity-name href=(entity.href) { (entity.name) }
                            }
                            span.entity-uuid { (entity.uuid) }
                        }
                    }
                }
            }
        }
    }
}
