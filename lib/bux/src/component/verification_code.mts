import "./verification_code.mcss";

// Auto-focus and navigation functionality for verification code inputs
document.addEventListener("DOMContentLoaded", function () {
    const codeInputs = document.querySelectorAll(".code-digit") as NodeListOf<HTMLInputElement>;

    console.log("Verification code inputs found:", codeInputs.length);

    if (codeInputs.length === 0) return;

    // Auto-focus first empty input
    const firstEmpty = Array.from(codeInputs).find((input) => !input.value);
    if (firstEmpty) {
        firstEmpty.focus();
    }

    codeInputs.forEach((input, index) => {
        // Handle input - move to next field
        input.addEventListener("input", function (e) {
            const target = e.target as HTMLInputElement;

            // Filter out any non-numeric characters
            target.value = target.value.replace(/[^0-9]/g, "");

            // Only allow single digit
            if (target.value.length > 1) {
                target.value = target.value.slice(-1); // ✅ keeps only the last digit
            }

            // Move to next input if current is filled
            if (target.value && index < codeInputs.length - 1) {
                const nextInput = codeInputs[index + 1];
                if (nextInput) {
                    nextInput.focus();
                }
            }

            // Update border color for filled inputs
            updateInputStyles();
        });

        // Handle backspace - move to previous field
        input.addEventListener("keydown", function (e) {
            if (e.key === "Backspace" && !input.value && index > 0) {
                const prevInput = codeInputs[index - 1];
                if (prevInput) {
                    prevInput.focus();
                }
            }
        });

        // Handle paste - distribute characters across inputs
        input.addEventListener("paste", function (e) {
            e.preventDefault();
            const pasteData = e.clipboardData?.getData("text") || "";
            const digits = pasteData.replace(/\D/g, "").slice(0, codeInputs.length);

            digits.split("").forEach((digit, i) => {
                const input = codeInputs[i];
                if (input) {
                    input.value = digit;
                }
            });

            // Focus next empty input or last input
            const nextEmpty = Array.from(codeInputs).find((input) => !input.value);
            if (nextEmpty) {
                nextEmpty.focus();
            } else {
                const lastInput = codeInputs[codeInputs.length - 1];
                if (lastInput) {
                    lastInput.focus();
                }
            }

            updateInputStyles();
        });

        // Only allow numeric input
        input.addEventListener("keypress", function (e) {
            // Allow control keys
            if (e.ctrlKey || e.metaKey || e.altKey) {
                return;
            }

            // Allow navigation keys
            if (
                ["Backspace", "Delete", "Tab", "Enter", "ArrowLeft", "ArrowRight"].includes(e.key)
            ) {
                return;
            }

            // Only allow digits 0-9
            if (!/^[0-9]$/.test(e.key)) {
                e.preventDefault();
            }
        });

        // Additional input validation to ensure only numbers
        input.addEventListener("beforeinput", function (e) {
            if (e.inputType === "insertText" && e.data && !/^[0-9]$/.test(e.data)) {
                e.preventDefault();
            }
        });
    });

    function updateInputStyles() {
        codeInputs.forEach((input) => {
            if (input.value) {
                input.style.borderColor = "#007bff";
                input.style.backgroundColor = "#f8f9fa";
            } else {
                input.style.borderColor = "#ddd";
                input.style.backgroundColor = "white";
            }
        });
    }

    // Initialize styles
    updateInputStyles();
});
