use crate::{<PERSON><PERSON>, <PERSON><PERSON>, html};

pub struct VerificationCode<'a> {
    pub title: &'a str,
    pub description: &'a str,
    pub phone_ending: &'a str,
    pub code_length: usize,
    pub prefilled_value: Option<&'a str>,
    pub resend_link: Option<&'a str>,
    pub email_link: Option<&'a str>,
}

pub fn new<'a>() -> VerificationCode<'a> {
    VerificationCode {
        title: "", // Will be set explicitly now
        description: "",
        phone_ending: "",
        code_length: 4,
        prefilled_value: None,
        resend_link: None,
        email_link: None,
    }
}

impl<'a> VerificationCode<'a> {
    pub fn set_title(&mut self, title: &'a str) {
        self.title = title;
    }

    pub fn set_description(&mut self, description: &'a str) {
        self.description = description;
    }

    pub fn set_phone_ending(&mut self, phone_ending: &'a str) {
        self.phone_ending = phone_ending;
    }

    pub fn set_code_length(&mut self, length: usize) {
        self.code_length = length;
    }

    pub fn set_prefilled_value(&mut self, value: &'a str) {
        self.prefilled_value = Some(value);
    }

    pub fn set_resend_link(&mut self, link: &'a str) {
        self.resend_link = Some(link);
    }

    pub fn set_email_link(&mut self, link: &'a str) {
        self.email_link = Some(link);
    }

    pub fn disable_resend_link(&mut self) {
        self.resend_link = None;
    }

    pub fn disable_email_link(&mut self) {
        self.email_link = None;
    }
}

impl Render for VerificationCode<'_> {
    fn render(&self) -> Markup {
        html! {
            bux-component-verification-code {
                div.verification-container {
                    h2.verification-title { (self.title) }

                    @if !self.description.is_empty() {
                        p.verification-description {
                            (self.description)
                            @if !self.phone_ending.is_empty() {
                                br; strong { (self.phone_ending) }
                            }
                        }
                    }

                    div.code-input-container {
                        @for i in 0..self.code_length {
                            @let name_attr = format!("code-{}", i);
                            @let id_attr = format!("verification-code-{}", i);
                            input.code-digit type="tel" maxlength="1" inputmode="numeric" pattern="[0-9]*" name=(name_attr) id=(id_attr) autocomplete="one-time-code" {}
                        }
                    }

                    @if self.resend_link.is_some() || self.email_link.is_some() {
                        p.verification-help {
                            "Didn't get a code? "
                            @if let Some(resend_link) = self.resend_link {
                                a href=(resend_link) { "Resend code" }
                            }
                            @if self.resend_link.is_some() && self.email_link.is_some() {
                                " or "
                            }
                            @if let Some(email_link) = self.email_link {
                                a href=(email_link) { "Resend code" }
                            }
                            " ."
                        }
                    }
                }
            }
        }
    }
}
