use chrono::{Datelike, NaiveDate, Utc};

pub fn current_year() -> i32 {
    let now = chrono::Utc::now();
    now.year()
}

pub fn current_date() -> NaiveDate {
    let now = chrono::Utc::now();
    now.date_naive()
}

/// calculate the age in years from a NaiveDate
pub fn age_in_years(birth_date: NaiveDate) -> i32 {
    let now = Utc::now();
    let age = now.year() - birth_date.year();
    if now.month() < birth_date.month()
        || (now.month() == birth_date.month() && now.day() < birth_date.day())
    {
        age - 1
    } else {
        age
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_current_year() {
        let year = current_year();
        assert_eq!(year, Utc::now().year());
    }

    #[test]
    fn test_age_in_years() {
        let birth_date = NaiveDate::from_ymd_opt(1980, 1, 1).unwrap();
        let age = age_in_years(birth_date);
        assert_eq!(age, Utc::now().year() - 1980);
    }
}
