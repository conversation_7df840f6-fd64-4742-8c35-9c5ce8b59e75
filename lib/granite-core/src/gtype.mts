// This does not use a wrapping object because it is more ideal to have the bare value in the normal case.
// This is a different paradigm than the Result<T,E> type, which should be explicitly checked each time it's returned.

export type GTypeEncode<T> = (value: T) => JsonValue;
export type GTypeValidate<TYPE, PARTIAL, ERROR> = (value: PARTIAL) => Result<TYPE, ERROR>;
export type GTypeDecode<T> = (data: JsonValue) => Result<T, string>;

///////////////////////////////////////////////////////////////////////////////////////////////////

export type NestedError<E> = {
    Outer: string;
    Inner?: E;
};

export function NestedError_encode<INNER>(
    value: NestedError<INNER>,
    inner_encode?: GTypeEncode<INNER>,
): JsonValue {
    if (inner_encode !== undefined && value.Inner !== undefined) {
        return { Outer: value.Outer, Inner: inner_encode(value.Inner) };
    } else {
        return { Outer: value.Outer };
    }
}

export function NestedError_decode<E>(
    data: JsonValue,
    inner_decode: GTypeDecode<E>,
): Result<NestedError<E>, string> {
    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
        let outer;
        {
            const result = string_decode(data["Outer"] ?? null);
            if ("Err" in result) {
                return { Err: "Error decoding the outer error message" };
            }
            outer = result.Ok;
        }

        if ("Inner" in data) {
            const result = inner_decode(data["Inner"] ?? null);
            if ("Err" in result) {
                return { Err: `Error decoding the inner error message: ${result.Err}` };
            }
            return { Ok: { Outer: outer, Inner: result.Ok } };
        } else {
            return { Ok: { Outer: outer } };
        }
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `char` in memory as a `string` with length 1 character
/// `char` encoded to the hex string of the character between 0 and 65535

export type char = string;

export function char_validate(
    value: string | undefined,
): Result<char, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "string") {
        return { Err: "must be a string" };
    }

    if (value.length !== 1) {
        return { Err: "must be 1 character" };
    }
    return { Ok: value };
}

/// Serializing a char to json using hex string
export function char_encode(value: string): JsonValue {
    // get the char code as a hex string
    const code = value.charCodeAt(0) || 0;

    // convert to hex string
    return code.toString(16);
}

/// Char validation function
export function char_decode(
    data: JsonValue,
): Result<char, string> {
    if (typeof data === "string") {
        // convert from hex stirng to number
        const code = Number.parseInt(data, 16);

        if (Number.isNaN(code)) {
            return { Err: "invalid hex input" };
        }

        if (code < 0 || code > 65535) {
            return { Err: "value out of range" };
        }

        // convert from number to char
        const value = String.fromCharCode(code);

        const result = char_validate(value);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `bool` type is aliased as `boolean` in javascript
/// `boolean` is in memory as a `boolean`
/// `boolean` encoded to json as `true` or `false`

export function boolean_validate(
    value: boolean | undefined,
): Result<boolean, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "boolean") {
        return { Err: "must be a boolean" };
    }

    return { Ok: value };
}

export function boolean_encode(value: boolean): JsonValue {
    return value;
}

export function boolean_decode(
    data: JsonValue,
): Result<boolean, string> {
    if (typeof data === "boolean") {
        const result = boolean_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `i8` in memory as a `number`
/// `i8` encoded to json as a string with a value between -128 and 127

export type i8 = number;

export function i8_validate(
    value: i8 | undefined,
): Result<i8, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "number") {
        return { Err: "must be a number" };
    }

    if (Number.isNaN(value)) {
        return { Err: "integer number required" };
    }

    if (!Number.isInteger(value)) {
        return { Err: "decimal number not allowed" };
    }

    if (value < -128 || value > 127) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function i8_encode(value: number): JsonValue {
    return value.toString();
}

export function i8_decode(
    data: JsonValue,
): Result<i8, string> {
    if (typeof data === "string") {
        data = Number.parseInt(data, 10);
    }

    if (typeof data === "number") {
        const result = i8_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `u8` in memory as a `number`
/// `u8` encoded to json as a string with a value between 0 and 255

export type u8 = number;

export function u8_validate(
    value: u8 | undefined,
): Result<u8, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "number") {
        return { Err: "must be a number" };
    }

    if (Number.isNaN(value)) {
        return { Err: "integer number required" };
    }

    if (!Number.isInteger(value)) {
        return { Err: "decimal number not allowed" };
    }

    if (value < 0 || value > 255) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function u8_encode(value: number): JsonValue {
    return value.toString();
}

export function u8_decode(
    data: JsonValue,
): Result<u8, string> {
    if (typeof data === "string") {
        data = Number.parseInt(data, 10);
    }

    if (typeof data === "number") {
        const result = u8_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `i16` in memory as a `number`
/// `i16` encoded to json as a string with a value between -32768 and 32767

export type i16 = number;

export function i16_validate(
    value: i16 | undefined,
): Result<i16, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "number") {
        return { Err: "must be a number" };
    }

    if (Number.isNaN(value)) {
        return { Err: "integer number required" };
    }

    if (!Number.isInteger(value)) {
        return { Err: "decimal number not allowed" };
    }

    if (value < -32768 || value > 32767) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function i16_encode(value: number): JsonValue {
    return value.toString();
}

export function i16_decode(
    data: JsonValue,
): Result<i16, string> {
    if (typeof data === "string") {
        data = Number.parseInt(data, 10);
    }

    if (typeof data === "number") {
        const result = i16_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `u16` in memory as a `number`
/// `u16` encoded to json as a string with a value between 0 and 65535

export type u16 = number;

export function u16_validate(
    value: u16 | undefined,
): Result<u16, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "number") {
        return { Err: "must be a number" };
    }

    if (Number.isNaN(value)) {
        return { Err: "integer number required" };
    }

    if (!Number.isInteger(value)) {
        return { Err: "decimal number not allowed" };
    }

    if (value < 0 || value > 65535) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function u16_encode(value: number): JsonValue {
    return value.toString();
}

export function u16_decode(
    data: JsonValue,
): Result<u16, string> {
    if (typeof data === "string") {
        data = Number.parseInt(data, 10);
    }

    if (typeof data === "number") {
        const result = u16_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `i32` in memory as a `number`
/// `i32` encoded to json as a string with a value between -2147483648 and 2147483647

export type i32 = number;

export function i32_validate(
    value: i32 | undefined,
): Result<i32, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "number") {
        return { Err: "must be a number" };
    }

    if (Number.isNaN(value)) {
        return { Err: "integer number required" };
    }

    if (!Number.isInteger(value)) {
        return { Err: "decimal number not allowed" };
    }

    if (value < -2147483648 || value > 2147483647) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function i32_encode(value: number): JsonValue {
    return value.toString();
}

export function i32_decode(
    data: JsonValue,
): Result<i32, string> {
    if (typeof data === "string") {
        data = Number.parseInt(data, 10);
    }

    if (typeof data === "number") {
        const result = i32_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `u32` in memory as a `number`
/// `u32` encoded to json as a string with a value between 0 and 4294967295

export type u32 = number;

export function u32_validate(
    value: u32 | undefined,
): Result<u32, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "number") {
        return { Err: "must be a number" };
    }

    if (Number.isNaN(value)) {
        return { Err: "integer number required" };
    }

    if (!Number.isInteger(value)) {
        return { Err: "decimal number not allowed" };
    }

    if (value < 0 || value > 4294967295) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function u32_encode(value: number): JsonValue {
    return value.toString();
}

export function u32_decode(
    data: JsonValue,
): Result<u32, string> {
    if (typeof data === "string") {
        data = Number.parseInt(data, 10);
    }

    if (typeof data === "number") {
        const result = u32_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `i64` in memory as a `bigint`
/// `i64` encoded to json as a string with a value between -9223372036854775808 and 9223372036854775807

export type i64 = bigint;

export function i64_validate(
    value: i64 | undefined,
): Result<i64, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "bigint") {
        return { Err: "must be a bigint" };
    }

    const MIN = -9223372036854775808n;
    const MAX = 9223372036854775807n;

    if (value < MIN || value > MAX) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function i64_encode(value: bigint): JsonValue {
    return value.toString();
}

export function i64_decode(
    data: JsonValue,
): Result<i64, string> {
    if (typeof data === "string" || typeof data === "number") {
        let result;
        try {
            result = i64_validate(BigInt(data));
        } catch {
            return { Err: "invalid bigint format" };
        }

        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `u64` in memory as a `bigint`
/// `u64` encoded to json as a string with a value between 0 and 18446744073709551615

export type u64 = bigint;

export function u64_validate(
    value: u64 | undefined,
): Result<u64, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "bigint") {
        return { Err: "must be a bigint" };
    }

    const MIN = 0n;
    const MAX = 18446744073709551615n;

    if (value < MIN || value > MAX) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function u64_encode(value: bigint): JsonValue {
    return value.toString();
}

export function u64_decode(
    data: JsonValue,
): Result<u64, string> {
    if (typeof data === "string" || typeof data === "number") {
        let result;
        try {
            result = u64_validate(BigInt(data));
        } catch {
            return { Err: "invalid bigint format" };
        }

        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `usize` in memory as a `bigint`
/// `usize` encoded to json as a string with a value between 0 and 18446744073709551615
/// Note: Using u64 range for maximum compatibility

export type usize = bigint;

export function usize_validate(
    value: usize | undefined,
): Result<usize, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "bigint") {
        return { Err: "must be a bigint" };
    }

    const MIN = 0n;
    const MAX = 18446744073709551615n;

    if (value < MIN || value > MAX) {
        return { Err: "out of range" };
    }

    return { Ok: value };
}

export function usize_encode(value: bigint): JsonValue {
    return value.toString();
}

export function usize_decode(
    data: JsonValue,
): Result<usize, string> {
    if (typeof data === "string" || typeof data === "number") {
        let result;
        try {
            result = usize_validate(BigInt(data));
        } catch {
            return { Err: "invalid bigint format" };
        }

        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `string` in memory as a `string`
/// `string` encoded to json as a string

export function string_validate(
    value: string | undefined,
): Result<string, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "string") {
        return { Err: "must be a string" };
    }

    return { Ok: value };
}

export function string_encode(value: string): JsonValue {
    return value;
}

export function string_decode(
    data: JsonValue,
): Result<string, string> {
    if (typeof data === "string") {
        const result = string_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `Uuid` in memory as a `string`
/// `Uuid` encoded to json as a string

export type Uuid = string;

export function Uuid_validate(
    value: string | undefined,
): Result<Uuid, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "string") {
        return { Err: "must be a string" };
    }

    if (!value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
        return { Err: "invalid Uuid" };
    }

    return { Ok: value };
}

export function Uuid_encode(value: string): JsonValue {
    return value;
}

export function Uuid_decode(
    data: JsonValue,
): Result<Uuid, string> {
    if (typeof data === "string") {
        const result = Uuid_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `Decimal` in memory as a `Decimal` class
/// `Decimal` encoded to json as a string

import { Decimal } from "./lib.mts";

export function Decimal_validate(
    value: Decimal | undefined,
): Result<Decimal, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (!(value instanceof Decimal)) {
        return { Err: "must be a Decimal" };
    }

    return { Ok: value };
}

export function Decimal_encode(value: Decimal): JsonValue {
    return value.to_string();
}

export function Decimal_decode(
    data: JsonValue,
): Result<Decimal, string> {
    if (typeof data === "string" || typeof data === "number") {
        const result = Decimal.from(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        const validate_result = Decimal_validate(result.Ok);
        if ("Err" in validate_result) {
            return { Err: validate_result.Err };
        }
        return validate_result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `DateUtc` in memory as a Date from ./lib.mts
/// `DateUtc` encoded to json as a string in the form "yyyy-mm-dd"

export type DateUtc = Date;

export function DateUtc_validate(
    value: DateUtc | undefined,
): Result<DateUtc, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (!(value instanceof Date)) {
        return { Err: "must be a Date" };
    }

    // make sure it's a valid date
    if (isNaN(value.getTime())) {
        return { Err: "invalid date" };
    }

    return { Ok: value };
}

export function DateUtc_encode(value: Date): JsonValue {
    return value.toISOString().substring(0, 10);
}

export function DateUtc_decode(
    data: JsonValue,
): Result<DateUtc, string> {
    if (typeof data === "string") {
        try {
            const date = new Date(data);
            if (isNaN(date.getTime())) {
                return { Err: "invalid date format" };
            }
            const result = DateUtc_validate(date);
            if ("Err" in result) {
                return { Err: result.Err };
            }
            return result;
        } catch (_e) {
            return { Err: "invalid date format" };
        }
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////

export type DateTimeUtc = Date;

export function DateTimeUtc_validate(
    value: DateTimeUtc | undefined,
): Result<DateTimeUtc, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (!(value instanceof Date)) {
        return { Err: "must be a Date" };
    }

    // make sure it's a valid date
    if (isNaN(value.getTime())) {
        return { Err: "invalid date" };
    }

    return { Ok: value };
}

export function DateTimeUtc_encode(value: Date): JsonValue {
    return value.toISOString();
}

export function DateTimeUtc_decode(
    data: JsonValue,
): Result<DateTimeUtc, string> {
    if (typeof data === "string") {
        try {
            const date = new Date(data);
            if (isNaN(date.getTime())) {
                return { Err: "invalid date format" };
            }
            const result = DateTimeUtc_validate(date);
            if ("Err" in result) {
                return { Err: result.Err };
            }
            return result;
        } catch (_e) {
            return { Err: "invalid date format" };
        }
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////

/// TODO: Date
/// TODO: DateTime
/// TODO: Time

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `Option` in memory as a { Some: T } | { None: true }
/// `Option` encoded to json as a { Some: T } | { None: true }

export type Option<T> = { Some: T } | { None: true };

export function Option_validate<TYPE, PARTIAL, ERROR>(
    value: Option<PARTIAL> | undefined,
    some_validate: GTypeValidate<TYPE, PARTIAL, ERROR>,
): Result<Option<TYPE>, ERROR> {
    if (value === undefined) {
        return { Ok: { None: true } };
    }

    if ("Some" in value) {
        const result = some_validate(value.Some);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return { Ok: { Some: result.Ok } };
    } else {
        return { Ok: { None: true } };
    }
}

export function Option_encode<T>(value: Option<T>, some_encode: GTypeEncode<T>): JsonValue {
    if ("Some" in value) {
        return { Some: some_encode(value.Some) };
    }
    return { None: true };
}

export function Option_decode<T>(
    data: JsonValue,
    some_decode: GTypeDecode<T>,
): Result<Option<T>, string> {
    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
        if ("Some" in data) {
            const result = some_decode(data["Some"]);
            if ("Err" in result) {
                return { Err: result.Err };
            }
            return { Ok: { Some: result.Ok } };
        } else if ("None" in data) {
            return { Ok: { None: true } };
        } else {
            return { Err: "Invalid variant" };
        }
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////

export type Result<OK, ERR> = { Ok: OK } | { Err: ERR };

export function Result_encode<OK, ERR>(
    value: Result<OK, ERR>,
    ok_encode: GTypeEncode<OK>,
    err_encode: GTypeEncode<ERR>,
): JsonValue {
    if ("Ok" in value) {
        return { Ok: ok_encode(value.Ok) };
    }
    return { Err: err_encode(value.Err) };
}

export function Result_decode<OK, ERR>(
    data: JsonValue,
    ok_decode: GTypeDecode<OK>,
    err_decode: GTypeDecode<ERR>,
): Result<Result<OK, ERR>, string> {
    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
        if ("Ok" in data) {
            const result = ok_decode(data["Ok"]);
            if ("Err" in result) {
                return { Err: result.Err };
            }
            return { Ok: { Ok: result.Ok } };
        } else if ("Err" in data) {
            const result = err_decode(data["Err"]);
            if ("Err" in result) {
                return { Err: result.Err };
            }
            return { Ok: { Err: result.Ok } };
        } else {
            return { Err: "Invalid variant" };
        }
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `Vec` in memory as a T[]
/// `Vec` encoded to json as a [T, T, ...]
/// `Vec_Error` is a Vec<E | undefined> where E is the error type of each item

export type Vec<T> = T[];

export function Vec_validate<TYPE, PARTIAL, ERROR>(
    value: Vec<PARTIAL> | undefined,
    value_validate: GTypeValidate<TYPE, PARTIAL, ERROR>,
): Result<Vec<TYPE>, NestedError<Vec<ERROR | undefined>>> {
    if (value === undefined) {
        return {
            Err: {
                Outer: "value is missing",
            },
        };
    }

    if (!Array.isArray(value)) {
        return {
            Err: {
                Outer: "wrong data type",
            },
        };
    }

    const ok_values: TYPE[] = [];
    const err_values: (ERROR | undefined)[] = [];
    let hasError = false;

    for (const item of value) {
        const result = value_validate(item);
        if ("Ok" in result) {
            ok_values.push(result.Ok);
            err_values.push(undefined);
        } else {
            err_values.push(result.Err);
            hasError = true;
        }
    }

    if (hasError) {
        return {
            Err: {
                Outer: "Vec validation failed",
                Inner: err_values,
            },
        };
    }

    return { Ok: ok_values };
}

export function Vec_encode<T>(value: Vec<T>, value_encode: GTypeEncode<T>): JsonValue {
    return value.map((item) => value_encode(item));
}

export function Vec_decode<T>(
    data: JsonValue,
    value_decode: GTypeDecode<T>,
): Result<Vec<T>, string> {
    if (Array.isArray(data)) {
        const ok_values: T[] = [];
        const err_values: string[] = [];

        for (const item of data) {
            const result = value_decode(item);
            if ("Err" in result) {
                err_values.push(result.Err);
            } else {
                ok_values.push(result.Ok);
            }
        }

        if (err_values.length > 0) {
            return { Err: err_values.join("; ") };
        }

        return { Ok: ok_values };
    }

    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////
/// `HashSet` in memory as a `Set`
/// `HashSet` encoded to json as an array of values
/// TODO

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `HashMap` in memory as a `Map`
/// `HashMap` encoded to json as an array of key-value pairs

export type HashMap<K, V> = Map<K, V>;

export function HashMap_validate<KEY, VALUE, KEY_ERROR, VALUE_ERROR>(
    value: HashMap<KEY, VALUE> | undefined,
    key_validate: GTypeValidate<KEY, KEY, KEY_ERROR>,
    value_validate: GTypeValidate<VALUE, VALUE, VALUE_ERROR>,
): Result<HashMap<KEY, VALUE>, NestedError<HashMap<KEY, VALUE_ERROR>>> {
    if (value === undefined) {
        return {
            Err: {
                Outer: "value is missing",
            },
        };
    }

    if (!(value instanceof Map)) {
        return {
            Err: {
                Outer: "wrong data type",
            },
        };
    }
    const ok_values: HashMap<KEY, VALUE> = new Map();
    const err_values: HashMap<KEY, VALUE_ERROR> = new Map();

    for (const [k, v] of value.entries()) {
        const key_result = key_validate(k);
        const value_result = value_validate(v);

        if ("Err" in key_result) {
            return { Err: { Outer: "Key validation failed" } };
        }

        if ("Ok" in value_result) {
            ok_values.set(k, value_result.Ok);
        } else {
            err_values.set(k, value_result.Err);
        }
    }

    if (err_values.size > 0) {
        return {
            Err: {
                Outer: "HashMap validation failed",
                Inner: err_values,
            },
        };
    }

    return { Ok: ok_values };
}

export function HashMap_encode<K, V>(
    value: HashMap<K, V>,
    key_encode: GTypeEncode<K>,
    value_encode: GTypeEncode<V>,
): JsonValue {
    const rval: JsonValue[] = [];
    for (const [k, v] of value.entries()) {
        rval.push([key_encode(k), value_encode(v)]);
    }
    return rval;
}

export function HashMap_decode<K, V>(
    data: JsonValue,
    key_decode: GTypeDecode<K>,
    value_decode: GTypeDecode<V>,
): Result<HashMap<K, V>, string> {
    const ok_values: [K, V][] = [];
    const err_values: string[] = [];

    if (typeof data === "object" && data !== null && Array.isArray(data)) {
        for (const item of data) {
            if (
                Array.isArray(item) && item.length === 2 && typeof item[0] !== "undefined" &&
                typeof item[1] !== "undefined"
            ) {
                const key_result = key_decode(item[0]);
                const value_result = value_decode(item[1]);

                if ("Err" in key_result) {
                    err_values.push(key_result.Err);
                } else if ("Err" in value_result) {
                    err_values.push(`${key_result.Ok}: ${value_result.Err}; `);
                } else {
                    ok_values.push([key_result.Ok, value_result.Ok]);
                }
            } else {
                err_values.push("Invalid key-value pair");
            }
        }
    }

    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
        for (const [k, v] of Object.entries(data)) {
            const key_result = key_decode(k);
            const value_result = value_decode(v);

            if ("Err" in key_result) {
                err_values.push(key_result.Err);
            } else if ("Err" in value_result) {
                err_values.push(value_result.Err);
            } else {
                ok_values.push([key_result.Ok, value_result.Ok]);
            }
        }
    }

    if (err_values.length > 0) {
        return { Err: err_values.join("") };
    }

    return { Ok: new Map(ok_values) };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `JsonValue` in memory as a `JsonValue`
/// `JsonValue` encoded to json as itself
export type JsonValue = string | number | boolean | null | JsonArray | JsonObject;

export function JsonValue_validate(
    value: JsonValue | undefined,
): Result<JsonValue, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    // check type
    if (
        typeof value === "string" || typeof value === "number" || typeof value === "boolean" ||
        value === null || Array.isArray(value) || (typeof value === "object" && value !== null)
    ) {
        return { Ok: value };
    }
    return { Err: "wrong data type" };
}

export function JsonValue_encode(value: JsonValue): JsonValue {
    return value;
}

export function JsonValue_decode(
    data: JsonValue,
): Result<JsonValue, string> {
    if (data === undefined) {
        return { Err: "value is missing" };
    }

    // check type
    if (
        typeof data === "string" || typeof data === "number" || typeof data === "boolean" ||
        data === null || Array.isArray(data) || (typeof data === "object" && data !== null)
    ) {
        return { Ok: data };
    }
    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `JsonObject` in memory as a `JsonObject`
/// `JsonObject` encoded to json as a JSON object

export type JsonObject = { [key: string]: JsonValue };

export function JsonObject_validate(
    value: JsonObject | undefined,
): Result<JsonObject, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value === "object" && value !== null && !Array.isArray(value)) {
        return { Ok: value };
    }
    return { Err: "wrong data type" };
}

export function JsonObject_encode(value: JsonObject): JsonValue {
    return value;
}

export function JsonObject_decode(
    data: JsonValue,
): Result<JsonObject, string> {
    if (data === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
        return { Ok: data };
    }
    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `JsonArray` in memory as a `JsonArray`
/// `JsonArray` encoded to json as a JSON array

export type JsonArray = JsonValue[];

export function JsonArray_validate(
    value: JsonArray | undefined,
): Result<JsonArray, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (Array.isArray(value)) {
        return { Ok: value };
    }
    return { Err: "wrong data type" };
}

export function JsonArray_encode(value: JsonArray): JsonValue {
    return value;
}

export function JsonArray_decode(
    data: JsonValue,
): Result<JsonArray, string> {
    if (data === undefined) {
        return { Err: "value is missing" };
    }

    if (Array.isArray(data)) {
        return { Ok: data };
    }
    return { Err: "wrong data type" };
}

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `JsonValue` in memory as a `JsonValue`
/// `JsonValue` encoded to json as itself

///////////////////////////////////////////////////////////////////////////////////////////////////
/// `IpAddr` in memory as a `string`
/// `IpAddr` encoded to json as a string
export type IpAddr = string;

export function IpAddr_validate(
    value: string | undefined,
): Result<IpAddr, string> {
    if (value === undefined) {
        return { Err: "value is missing" };
    }

    if (typeof value !== "string") {
        return { Err: "must be a string" };
    }

    // Regex for IPv4 and IPv6 validation
    const ipv4_regex =
        /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // Simplified IPv6 regex for common cases, full validation is complex
    // This regex covers most common IPv6 formats including compressed ones (::)
    const ipv6_regex =
        /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9]))$/;

    if (ipv4_regex.test(value) || ipv6_regex.test(value)) {
        return { Ok: value };
    }

    return { Err: "invalid IpAddr format" };
}

export function IpAddr_encode(value: IpAddr): JsonValue {
    return value;
}

export function IpAddr_decode(
    data: JsonValue,
): Result<IpAddr, string> {
    if (typeof data === "string") {
        const result = IpAddr_validate(data);
        if ("Err" in result) {
            return { Err: result.Err };
        }
        return result;
    }

    if (data === undefined) {
        return { Err: "value is missing" };
    }

    return { Err: "wrong data type" };
}
