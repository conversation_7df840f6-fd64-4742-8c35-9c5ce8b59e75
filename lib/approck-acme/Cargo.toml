[package]
name = "approck-acme"
version = "0.1.0"
edition = "2024"

[dependencies]
approck = { workspace = true }
granite = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true, features = ["full"] }
hyper = { workspace = true }
hyper-rustls = { workspace = true }
hyper-util = { workspace = true, features = ["tokio", "server", "http1", "http2"] }
http = { workspace = true }
http-body-util = { workspace = true }
rustls-pemfile = { workspace = true }
rustls = { workspace = true, features = ["aws_lc_rs"] }
instant-acme = { workspace = true }
rcgen = { workspace = true }
x509-parser = { workspace = true }
chasetls = { workspace = true }