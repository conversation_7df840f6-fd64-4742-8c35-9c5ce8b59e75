[package]
name = "approck-redis"
version = "0.1.0"
edition = "2024"

[dependencies]
approck = { workspace = true }
granite = { workspace = true }

bb8 = { workspace = true }
bb8-redis = { workspace = true }
error-stack = { workspace = true }
redis = { workspace = true, features = ["json"] }
serde = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
serde_json.workspace = true
tracing = { workspace = true }

[dev-dependencies]
tokio-test = "0.4.4"
futures = "0.3.30"
