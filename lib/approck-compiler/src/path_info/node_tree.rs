//! A tree-based data structure for organizing and managing routing nodes.
//!
//! The `NodeTree` module provides a hierarchical structure for storing and manipulating
//! routing information, supporting various types of nodes including literal paths,
//! capture parameters, index nodes, and a root node. It is designed to handle different
//! types of routing items such as HTTP modules, prefix modules, and static files.
//!
//! Nodes are stored in a `Vec<Node>` where each node's `id` corresponds to its index
//! in the vector. The implementation uses unchecked index operations (`[]`) for
//! performance, as internal logic ensures index validity, and no external access to
//! the raw vector is exposed, maintaining safety guarantees.
//!
//! # Key Components
//! - `NodeTree`: The main structure that holds a vector of `Node`s in memory.
//! - `Node`: An enum representing different node types in the tree.
//! - `RootNode`: A node representing the root of the tree.
//!   In a URL this is the part before the first `/`
//! - `LiteralNode`: A node representing a literal PathSegment
//! - `CaptureNode`: A node representing a capture PathSegment.
//! - `IndexNode`: A node representing an index PathSegment.
//! - `EmptyNode`: A node representing an empty PathSegment
//! - `NodeItem`: An enum representing the items that can be stored in nodes.
//!
//! # Features
//! - Path ingestion and node creation
//! - Support for static file routes
//! - HTTP module ingestion
//! - Prefix module ingestion
//! - Path lineage (e.g. the path from root to a specific node)
//! - Tree flattening for sorted path access

use crate::{ApiModule, HttpModule, PrefixModule, StaticFile};
use granite::{WebPath, WebPathCapture, WebPathSegment};
use std::collections::HashMap;
use std::path::{Path, PathBuf};

pub struct NodeTree {
    app_rel_path: PathBuf,

    /// An append-only vec of Nodes in memory, internally referenced by index
    memory: Vec<Node>,

    /// mapping of a web_dir to its self.memory[n] index
    web_dir_map: HashMap<String, usize>,

    es_build_map: crate::EsBuildMap,

    api_modules: Vec<crate::ApiModule>,
}

pub enum Node {
    WebDir(WebDirNode),
    Literal(LiteralNode),
    Capture(CaptureNode),
    Index(IndexNode),
}

pub struct WebDirNode {
    #[allow(dead_code)]
    id: usize,
    web_dir: String,
    children: HashMap<WebPathSegment, usize>,
    items: Vec<NodeItem>,
}

pub struct LiteralNode {
    #[allow(dead_code)]
    id: usize,
    parent_id: usize,
    literal: String,
    children: HashMap<WebPathSegment, usize>,
    items: Vec<NodeItem>,
}

pub struct CaptureNode {
    #[allow(dead_code)]
    id: usize,
    parent_id: usize,
    capture_name: String,
    capture_type: WebPathCapture,
    children: HashMap<WebPathSegment, usize>,
    items: Vec<NodeItem>,
}

pub struct IndexNode {
    #[allow(dead_code)]
    id: usize,
    parent_id: usize,
    items: Vec<NodeItem>,
}

#[allow(clippy::large_enum_variant)]
pub enum NodeItem {
    HttpModule(crate::HttpModule),
    PrefixModule(crate::PrefixModule),
    StaticFile(crate::StaticFile),
}

impl NodeTree {
    pub fn new(app_rel_path: PathBuf, es_build_map: crate::EsBuildMap, capacity: usize) -> Self {
        Self {
            app_rel_path,
            memory: Vec::with_capacity(capacity),
            web_dir_map: HashMap::new(),
            es_build_map,
            api_modules: Vec::new(),
        }
    }

    pub fn get_app_bundle(&self) -> Option<&crate::EsBuildOutput> {
        let path = self.app_rel_path.join("src/lib.ts");
        self.es_build_map.get_from_rel_path(&path)
    }

    /// Obtain the EsBuildOutput for a given relative path ending in .rs
    pub fn get_esbuild_output(&self, rel_path: &Path) -> Option<&crate::EsBuildOutput> {
        // replace the .rs with .ts
        let ts_rel_path = rel_path.with_extension("ts");
        self.es_build_map.get_from_rel_path(&ts_rel_path)
    }

    pub fn iter_web_dirs(&self) -> impl Iterator<Item = &Node> {
        self.web_dir_map.values().map(|&id| &self.memory[id])
    }

    pub fn iter_api_modules(&self) -> impl Iterator<Item = &ApiModule> {
        self.api_modules.iter()
    }

    pub fn ingest_http_module(&mut self, http_module: HttpModule) {
        let node_id = self.ingest_path(&http_module.web_path);
        self.push_item(node_id, NodeItem::HttpModule(http_module));
    }

    pub fn ingest_prefix_module(&mut self, prefix_module: PrefixModule) {
        let node_id = self.ingest_path(&prefix_module.web_path);
        self.push_item(node_id, NodeItem::PrefixModule(prefix_module));
    }

    pub fn ingest_api_module(&mut self, api_module: ApiModule) {
        self.api_modules.push(api_module);
    }

    fn ingest_path(&mut self, web_path: &WebPath) -> usize {
        // init the current node to the "root" node and skip the root node of the incoming path

        let mut current_id = self.get_webdir(web_path.web_dir());

        for path_segment in web_path.iter() {
            match self.lookup_child(current_id, path_segment) {
                Some(child_id) => {
                    current_id = child_id;
                }
                None => {
                    current_id = self.add_child(current_id, path_segment);
                }
            }
        }

        current_id
    }

    /// (create if needed) and get a WebDirNode's id
    fn get_webdir(&mut self, web_dir: &str) -> usize {
        match self.web_dir_map.get(web_dir) {
            Some(id) => *id,
            None => {
                // add the web dir node
                let id = self.memory.len();
                self.memory.push(Node::WebDir(WebDirNode {
                    id,
                    web_dir: web_dir.to_string(),
                    children: HashMap::new(),
                    items: Vec::new(),
                }));
                self.web_dir_map.insert(web_dir.to_string(), id);
                id
            }
        }
    }

    fn add_child(&mut self, parent_id: usize, path_segment: &WebPathSegment) -> usize {
        let child_id = self.memory.len();

        // Create the new child node
        let child_node = match path_segment {
            WebPathSegment::Index => Node::Index(IndexNode {
                id: child_id,
                parent_id,
                items: Vec::new(),
            }),
            WebPathSegment::Literal(literal) => Node::Literal(LiteralNode {
                id: child_id,
                parent_id,
                literal: literal.clone(),
                children: HashMap::new(),
                items: Vec::new(),
            }),
            WebPathSegment::Capture { name, capture } => Node::Capture(CaptureNode {
                id: child_id,
                parent_id,
                capture_name: name.clone(),
                capture_type: capture.clone(),
                children: HashMap::new(),
                items: Vec::new(),
            }),
        };

        // Add child reference to parent's children map
        match &mut self.memory[parent_id] {
            Node::WebDir(node) => {
                node.children.insert(path_segment.clone(), child_id);
            }
            Node::Literal(node) => {
                node.children.insert(path_segment.clone(), child_id);
            }
            Node::Capture(node) => {
                node.children.insert(path_segment.clone(), child_id);
            }
            Node::Index(_) => {
                panic!("cannot add child to empty node");
            }
        }

        // Add the new node to memory
        self.memory.push(child_node);

        child_id
    }

    fn lookup_child(&self, node_id: usize, path_segment: &WebPathSegment) -> Option<usize> {
        match &self.memory[node_id] {
            Node::WebDir(node) => node.children.get(path_segment).cloned(),
            Node::Literal(node) => node.children.get(path_segment).cloned(),
            Node::Capture(node) => node.children.get(path_segment).cloned(),
            Node::Index(_) => None,
        }
    }

    fn push_item(&mut self, node_id: usize, item: NodeItem) {
        match &mut self.memory[node_id] {
            Node::WebDir(node) => node.items.push(item),
            Node::Literal(node) => node.items.push(item),
            Node::Capture(node) => node.items.push(item),
            Node::Index(node) => node.items.push(item),
        }
    }

    fn iter_items(&self, node_id: usize) -> impl Iterator<Item = &NodeItem> {
        match &self.memory[node_id] {
            Node::WebDir(node) => node.items.iter(),
            Node::Literal(node) => node.items.iter(),
            Node::Capture(node) => node.items.iter(),
            Node::Index(node) => node.items.iter(),
        }
    }

    // for a given `id`, returns `Some(parent_id)` or `None` if it's the root node.
    fn parent(&self, id: usize) -> Option<usize> {
        match &self.memory[id] {
            Node::WebDir(_) => None,
            Node::Literal(node) => Some(node.parent_id),
            Node::Capture(node) => Some(node.parent_id),
            Node::Index(node) => Some(node.parent_id),
        }
    }

    // Calculates the lineage from the root node to the specified node
    fn lineage(&self, id: usize) -> Vec<usize> {
        let mut path = Vec::new();
        let mut current_id = id;
        loop {
            path.push(current_id);
            match self.parent(current_id) {
                Some(parent_id) => current_id = parent_id,
                None => break,
            }
        }
        path.reverse();
        path
    }

    /// This function traverses the node's lineage from the root to the specified node,
    /// aggregating all prefix modules encountered along the way.
    ///
    /// The order of the modules in the returned vector corresponds to:
    /// 1. the order of nodes from the root down to the given node,
    /// 2. the topologicial order of modules within this application
    pub fn lineage_prefix_modules(&self, node: &Node) -> Vec<&PrefixModule> {
        self.lineage(node.id())
            .iter()
            .flat_map(|&path_node_id| self.iter_items(path_node_id))
            .filter_map(|item| match item {
                NodeItem::PrefixModule(prefix_module) => Some(prefix_module),
                _ => None,
            })
            .collect()
    }

    pub fn self_prefix_modules(&self, node: &Node) -> Vec<&PrefixModule> {
        self.iter_items(node.id())
            .filter_map(|item| match item {
                NodeItem::PrefixModule(prefix_module) => Some(prefix_module),
                _ => None,
            })
            .collect()
    }

    // Extracts the Path of the specified node, not including the Root Node
    fn path(&self, id: usize) -> WebPath {
        let lineage = self.lineage(id);

        let web_dir = match &self.memory[lineage[0]] {
            Node::WebDir(node) => &node.web_dir,
            _ => panic!("root node must be a WebDir node"),
        };

        let segments = lineage[1..]
            .iter()
            .map(|&id| self.memory[id].path_segment());

        WebPath::from_segments(web_dir.clone(), segments.collect())
    }

    // return a vec of &Node references for the children of a node

    /*
    /// return a vec of MenuFunction references for the menu functions of the whole path
    fn menu_functions(&self, id: usize) -> Vec<&MenuFunction> {
        self.lineage(id)
            .iter()
            .flat_map(|&path_node_id| match &self.memory[path_node_id] {
                Node::Root(node) => Some(&node.menu_functions),
                Node::Literal(node) => Some(&node.menu_functions),
                Node::Capture(node) => Some(&node.menu_functions),
                Node::Empty(node) => Some(&node.menu_functions),
            })
            .flatten()
            .collect()
    }
    */
    /// Returns a Vec of (Path, Node) where root is represented by empty path.
    /// Sorting is done by the PathSegment rules.
    pub fn flatten(&self) -> Vec<(WebPath, &Node)> {
        let mut rval = Vec::new();
        for (id, node) in self.memory.iter().enumerate() {
            rval.push((self.path(id), node));
        }
        rval.sort_by(|a, b| a.0.cmp(&b.0));
        rval
    }

    pub fn children(&self, node: &Node) -> Vec<&Node> {
        match node {
            Node::WebDir(node) => node.children.values().map(|&id| &self.memory[id]).collect(),
            Node::Literal(node) => node.children.values().map(|&id| &self.memory[id]).collect(),
            Node::Capture(node) => node.children.values().map(|&id| &self.memory[id]).collect(),
            Node::Index(_) => Vec::new(),
        }
    }

    /// produce an iterator of owned (/URI, PathBuf)
    pub fn iter_static_files(&self) -> impl Iterator<Item = (String, PathBuf)> + '_ {
        self.es_build_map.iter_abs_output_paths().map(|p| {
            let uri = format!(
                "/{}",
                p.file_name()
                    .expect("static file must have a filename")
                    .to_string_lossy()
            );
            (uri, p.to_owned())
        })
    }
}

impl Node {
    fn id(&self) -> usize {
        match self {
            Node::WebDir(node) => node.id,
            Node::Literal(node) => node.id,
            Node::Capture(node) => node.id,
            Node::Index(node) => node.id,
        }
    }

    pub fn web_dir(&self) -> &str {
        match self {
            Node::WebDir(node) => &node.web_dir,
            _ => panic!("Only WebDirNode has a web_dir"),
        }
    }

    pub fn path_segment(&self) -> WebPathSegment {
        match self {
            Node::WebDir(_) => panic!("WebDirNode does not have a WebPathSegment"),
            Node::Literal(node) => WebPathSegment::Literal(node.literal.clone()),
            Node::Capture(node) => WebPathSegment::Capture {
                name: node.capture_name.clone(),
                capture: node.capture_type.clone(),
            },
            Node::Index(_) => WebPathSegment::Index,
        }
    }

    pub fn http_modules(&self) -> Vec<&HttpModule> {
        match self {
            Node::WebDir(node) => &node.items,
            Node::Literal(node) => &node.items,
            Node::Capture(node) => &node.items,
            Node::Index(node) => &node.items,
        }
        .iter()
        .filter_map(|item| match item {
            NodeItem::HttpModule(http_module) => Some(http_module),
            _ => None,
        })
        .collect()
    }

    pub fn prefix_modules(&self) -> Vec<&PrefixModule> {
        match self {
            Node::WebDir(node) => &node.items,
            Node::Literal(node) => &node.items,
            Node::Capture(node) => &node.items,
            Node::Index(node) => &node.items,
        }
        .iter()
        .filter_map(|item| match item {
            NodeItem::PrefixModule(prefix_module) => Some(prefix_module),
            _ => None,
        })
        .collect()
    }

    pub fn static_files(&self) -> Vec<&StaticFile> {
        match self {
            Node::WebDir(node) => &node.items,
            Node::Literal(node) => &node.items,
            Node::Capture(node) => &node.items,
            Node::Index(node) => &node.items,
        }
        .iter()
        .filter_map(|item| match item {
            NodeItem::StaticFile(static_file) => Some(static_file),
            _ => None,
        })
        .collect()
    }
}
