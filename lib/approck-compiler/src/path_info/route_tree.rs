#![allow(dead_code)]
//! Defines a hierarchical routing structure for HTTP request handling.
//! `RouteTree` serves as the root container for `Route` nodes, each representing
//! a path segment with optional request handlers (`RequestHandler` variants:
//! dynamic, static files, static content, redirects), authentication functions (`AuthFunction`),
//! and menu functions (`MenuFunction`). Supports tree traversal and flattening
//! for route resolution.

use granite::{WebPath, WebPathSegment};
use std::collections::HashMap;
use std::{fmt, path::PathBuf};
use syn::Ident;

#[derive(Debug)]
pub struct RouteTree {
    children: Vec<RouteTreeWebDir>,
    static_files: Vec<RouteTreeStaticFile>,
    api_modules: Vec<RouteTreeApiModule>,
}

impl RouteTree {
    pub fn get(&self, web_dir: &str) -> Option<&RouteTreeWebDir> {
        self.children.iter().find(|child| child.web_dir == web_dir)
    }

    // Add this method to iterate over static files using references
    pub fn iter_static_files(&self) -> impl Iterator<Item = &RouteTreeStaticFile> {
        self.static_files.iter()
    }
}

#[derive(Debug)]
pub struct RouteTreeStaticFile {
    pub uri: String,
    pub abs_path: PathBuf,
    pub content_type: &'static str,
}

#[derive(Debug)]
pub struct RouteTreeWebDir {
    pub web_dir: String,
    pub children: Vec<RouteTreeRoute>,
    pub self_auth_functions: Vec<AuthFunction>,
    pub self_menu_functions: Vec<MenuFunction>,
}

#[derive(Debug)]
pub struct RouteTreeRoute {
    pub path_segment: WebPathSegment,
    pub children: Vec<RouteTreeRoute>,
    pub request_handler: Option<RequestHandler>,
    pub lineage_auth_functions: Vec<AuthFunction>,
    pub lineage_menu_functions: Vec<MenuFunction>,
    pub self_auth_functions: Vec<AuthFunction>,
    pub self_menu_functions: Vec<MenuFunction>,
}

pub struct RouteTreeApiModule {
    pub rel_path: String,
    pub line_number: usize,
    pub api_name: String,
    pub syn_path: syn::Path,
}

impl std::fmt::Debug for RouteTreeApiModule {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{} @ {}:{}",
            self.api_name, self.rel_path, self.line_number
        )
    }
}

#[derive(Debug)]
pub enum RequestHandler {
    Dynamic(DynamicHandler),
    StaticFile(StaticFileHandler),
    StaticContent(StaticContentHandler),
    Redirect(RedirectHandler),
}

pub struct DynamicHandler {
    pub rel_path: String,
    pub app_js_bundle_uri: Option<String>,
    pub app_css_bundle_uri: Option<String>,
    pub js_bundle_uri: Option<String>,
    pub css_bundle_uri: Option<String>,
    pub document: Option<DynamicHandlerDocument>,
    pub line_number: usize,
    pub syn_path_to_wrap_fn: syn::Path,
    pub capture_arg_names: Vec<Ident>,
}

pub struct DynamicHandlerDocument {
    pub ident: Ident,
    pub rel_path: PathBuf,
    pub js_bundle_uri: Option<String>,
    pub css_bundle_uri: Option<String>,
}

impl std::fmt::Debug for DynamicHandler {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}:{}", self.rel_path, self.line_number)
    }
}

#[derive(Debug)]
pub struct StaticFileHandler {
    pub abs_path: std::path::PathBuf,
}

impl StaticFileHandler {
    pub fn rel_path(&self, base_path: &std::path::Path) -> String {
        match self.abs_path.strip_prefix(base_path) {
            Ok(rel_path) => rel_path.to_string_lossy().to_string(),
            Err(_) => self.abs_path.to_string_lossy().to_string(),
        }
    }
}

#[derive(Debug)]
pub struct StaticContentHandler {
    pub rel_path: String,
    pub line_number: usize,
    pub status_code: http::StatusCode,
    pub headers: http::HeaderMap,
    pub content: Vec<u8>,
}

#[derive(Debug)]
pub struct RedirectHandler {
    pub rel_path: String,
    pub line_number: usize,
    pub status_code: http::StatusCode,
    pub location: String,
}

pub struct AuthFunction {
    pub web_path: WebPath,
    pub rel_path: String,
    pub line_number: usize,
    pub syn_path: syn::Path,
    pub params: crate::AuthParams,
}

impl std::fmt::Debug for AuthFunction {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}:{}", self.rel_path, self.line_number)
    }
}

pub struct MenuFunction {
    pub web_path: WebPath,
    pub rel_path: String,
    pub line_number: usize,
    pub syn_path: syn::Path,
    pub params: crate::MenuParams,
}

impl std::fmt::Debug for MenuFunction {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}:{}", self.rel_path, self.line_number)
    }
}

pub trait RouteExt {
    fn children(&self) -> Vec<&RouteTreeRoute>;
    fn append_route(&mut self, route: RouteTreeRoute);
    fn sort(&mut self);
}

impl RouteExt for RouteTreeWebDir {
    fn children(&self) -> Vec<&RouteTreeRoute> {
        self.children.iter().collect()
    }
    fn append_route(&mut self, route: RouteTreeRoute) {
        self.children.push(route);
    }
    fn sort(&mut self) {
        // First sort children by path_segment
        self.children
            .sort_by(|a, b| a.path_segment.cmp(&b.path_segment));

        // Then recursively sort each child's children
        for child in &mut self.children {
            child.sort();
        }
    }
}

impl RouteExt for RouteTreeRoute {
    fn children(&self) -> Vec<&RouteTreeRoute> {
        self.children.iter().collect()
    }
    fn append_route(&mut self, route: RouteTreeRoute) {
        self.children.push(route);
    }
    fn sort(&mut self) {
        // First sort children by path_segment
        self.children
            .sort_by(|a, b| a.path_segment.cmp(&b.path_segment));

        // Then recursively sort each child's children
        for child in &mut self.children {
            child.sort();
        }
    }
}

impl RouteTree {
    #[allow(clippy::new_without_default)]
    pub fn new<ITER1, ITER2, ITER3>(
        route_tree_static_files: ITER1,
        route_tree_web_dirs: ITER2,
        route_tree_api_modules: ITER3,
    ) -> Self
    where
        ITER1: Iterator<Item = RouteTreeStaticFile>,
        ITER2: Iterator<Item = RouteTreeWebDir>,
        ITER3: Iterator<Item = RouteTreeApiModule>,
    {
        // validate that api_modules are not duplicated
        let route_tree_api_modules = {
            let mut api_modules: HashMap<String, RouteTreeApiModule> = HashMap::new();
            for api_module in route_tree_api_modules {
                if let Some(duplicated_module) = api_modules.get(&api_module.api_name) {
                    panic!(
                        "#[approck::api] module duplication: {} and {}",
                        api_module.rel_path, duplicated_module.rel_path
                    );
                }
                api_modules.insert(api_module.api_name.clone(), api_module);
            }
            api_modules.into_values().collect()
        };

        Self {
            children: route_tree_web_dirs.collect(),
            static_files: route_tree_static_files.collect(),
            api_modules: route_tree_api_modules,
        }
    }

    pub fn sort(&mut self) {
        // First sort children by web_dir
        self.children.sort_by(|a, b| a.web_dir.cmp(&b.web_dir));

        // sort the static files
        self.static_files.sort_by(|a, b| a.uri.cmp(&b.uri));

        // Then recursively sort each child's children
        for child in &mut self.children {
            child.sort();
        }

        // sort the api modules
        self.api_modules.sort_by(|a, b| a.api_name.cmp(&b.api_name));
    }

    // Flatten the route tree into a vector of (Path, &Route)
    pub fn flatten(&self) -> Vec<(WebPath, &RouteTreeRoute)> {
        let mut result = Vec::new();

        // Helper function to recursively flatten the tree
        fn flatten_route<'a>(
            parent_path: &WebPath,
            route: &'a RouteTreeRoute,
            result: &mut Vec<(WebPath, &'a RouteTreeRoute)>,
        ) {
            // Create the current path by appending this route's segment to parent path
            let mut current_path = parent_path.clone();
            current_path.push(route.path_segment.clone());

            // Add this route to the result
            result.push((current_path.clone(), route));

            // Recursively process children
            for child in &route.children {
                flatten_route(&current_path, child, result);
            }
        }

        for route_tree_web_dir in &self.children {
            for route in &route_tree_web_dir.children {
                let web_path = WebPath::new(route_tree_web_dir.web_dir.clone());
                flatten_route(&web_path, route, &mut result);
            }
        }

        // Sort the results by path for consistent output
        result.sort_by(|a, b| a.0.cmp(&b.0));

        result
    }

    pub fn iter_api_modules(&self) -> impl Iterator<Item = &RouteTreeApiModule> {
        self.api_modules.iter()
    }
}

impl RouteTreeWebDir {
    pub fn new<I>(web_dir: String, children: I) -> Self
    where
        I: Iterator<Item = RouteTreeRoute>,
    {
        Self {
            web_dir,
            children: children.collect(),
            self_auth_functions: Vec::new(),
            self_menu_functions: Vec::new(),
        }
    }

    pub fn append_self_auth_function(&mut self, auth_fn: AuthFunction) {
        self.self_auth_functions.push(auth_fn);
    }

    pub fn append_self_menu_function(&mut self, menu_fn: MenuFunction) {
        self.self_menu_functions.push(menu_fn);
    }
}

impl RouteTreeRoute {
    pub fn new(segment: WebPathSegment) -> Self {
        Self {
            path_segment: segment,
            children: Vec::new(),
            request_handler: None,
            lineage_auth_functions: Vec::new(),
            lineage_menu_functions: Vec::new(),
            self_auth_functions: Vec::new(),
            self_menu_functions: Vec::new(),
        }
    }

    pub fn set_request_handler(&mut self, handler: RequestHandler) {
        self.request_handler = Some(handler);
    }

    pub fn append_lineage_auth_function(&mut self, auth_fn: AuthFunction) {
        self.lineage_auth_functions.push(auth_fn);
    }

    pub fn append_lineage_menu_function(&mut self, menu_fn: MenuFunction) {
        self.lineage_menu_functions.push(menu_fn);
    }

    pub fn append_self_auth_function(&mut self, auth_fn: AuthFunction) {
        self.self_auth_functions.push(auth_fn);
    }

    pub fn append_self_menu_function(&mut self, menu_fn: MenuFunction) {
        self.self_menu_functions.push(menu_fn);
    }
}
