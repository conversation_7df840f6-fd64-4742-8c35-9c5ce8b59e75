use std::path::{Path, PathBuf};

use indexmap::IndexMap;

#[derive(Debug)]
pub struct EsBuildMap {
    rel_entry_point_map: IndexMap<PathBuf, EsBuildOutput>,
    abs_output_paths: Vec<PathBuf>,
}

#[derive(Debug)]
pub struct EsBuildOutput {
    pub js_abs_path: PathBuf,
    pub css_abs_path: Option<PathBuf>,
}

impl EsBuildMap {
    pub fn new_empty() -> Self {
        Self {
            rel_entry_point_map: IndexMap::new(),
            abs_output_paths: Vec::new(),
        }
    }

    pub fn get_from_rel_path(&self, key: &PathBuf) -> Option<&EsBuildOutput> {
        self.rel_entry_point_map.get(key)
    }

    pub fn iter_abs_output_paths(&self) -> impl Iterator<Item = &PathBuf> {
        self.abs_output_paths.iter()
    }

    pub fn iter_abs_output_css_paths(&self) -> impl Iterator<Item = &PathBuf> {
        self.abs_output_paths
            .iter()
            .filter(|p| p.extension().unwrap_or_default() == "css")
    }
}

impl EsBuildOutput {
    pub fn js_bundle_uri(&self) -> Option<String> {
        self.js_abs_path
            .file_name()
            .map(|f| format!("/{}", f.to_string_lossy()))
    }

    pub fn css_bundle_uri(&self) -> Option<String> {
        self.css_abs_path
            .as_ref()
            .and_then(|p| p.file_name())
            .map(|f| format!("/{}", f.to_string_lossy()))
    }
}

/// Parse an esbuild metafile and return a mapping of entry points to their output files
pub fn parse_esbuild_metafile(workspace_path: &Path, path: &PathBuf) -> EsBuildMap {
    // Use the parse_esbuild_output function from the parse module
    let esbuild = parse::parse_esbuild_output(path).unwrap_or_else(|err| {
        panic!(
            "Failed to read/parse esbuild metafile at {:?}: {}",
            path, err
        );
    });

    // Build the entry point map
    let mut entry_point_map = IndexMap::new();
    let mut output_paths = Vec::new();

    for (static_path, output_struct) in esbuild.outputs {
        output_paths.push(workspace_path.join(&static_path));

        if let Some(entry_point) = output_struct.entry_point {
            let rel_entry_path = PathBuf::from(entry_point);
            let js_abs_path = workspace_path.join(&static_path);
            let css_abs_path = output_struct
                .css_bundle
                .map(|css_bundle| workspace_path.join(css_bundle));

            entry_point_map.insert(
                rel_entry_path,
                EsBuildOutput {
                    js_abs_path,
                    css_abs_path,
                },
            );
        }
    }

    // need determinstic output
    entry_point_map.sort_keys();
    output_paths.sort();

    EsBuildMap {
        rel_entry_point_map: entry_point_map,
        abs_output_paths: output_paths,
    }
}

mod parse {

    /*
    "outputs": {
        "target/esbuild/df4l-app/entry-YC5S3UNR.js.map": {
            "imports": [],
            "exports": [],
            "inputs": {},
            "bytes": 93
            },
        "target/esbuild/df4l-app/entry-YC5S3UNR.js": {
            "imports": [
                {
                    "path": "target/esbuild/df4l-app/chunk-27FF33BF.js",
                    "kind": "import-statement"
                },
                {
                    "path": "target/esbuild/df4l-app/chunk-N2GZ2BJP.js",
                    "kind": "import-statement"
                }
            ],
            "exports": [],
            "entryPoint": "smart/df4l-advisor/src/web/advisor/mapper/client/mapper/df4l/edit.ts",
            "cssBundle": "target/esbuild/df4l-app/entry-D4HO36VB.css",
            "inputs": {
                "smart/df4l-advisor/src/web/advisor/mapper/client/mapper/df4l/edit.mcss": {
                "bytesInOutput": 0
                },
                "smart/df4l-advisor/src/web/advisor/mapper/client/mapper/df4l/edit.ts": {
                "bytesInOutput": 0
                }
            },
            "bytes": 223
        },
    }
    */

    use serde::Deserialize;
    use std::collections::HashMap;
    use std::fs;
    use std::io;
    use std::path::Path;

    #[derive(Debug, Deserialize)]
    pub struct EsBuild {
        pub outputs: HashMap<String, EsBuildOutput>,
    }

    #[derive(Debug, Deserialize)]
    pub struct EsBuildOutput {
        #[serde(default)]
        #[serde(rename = "entryPoint")]
        pub entry_point: Option<String>,
        #[serde(default)]
        #[serde(rename = "cssBundle")]
        pub css_bundle: Option<String>,
    }

    pub fn parse_esbuild_output(path: &Path) -> Result<EsBuild, io::Error> {
        let content = match fs::read_to_string(path) {
            Ok(content) => content,
            Err(err) => {
                if err.kind() == io::ErrorKind::NotFound {
                    return Ok(EsBuild {
                        outputs: HashMap::new(),
                    });
                }
                return Err(err);
            }
        };

        let output: EsBuild = serde_json::from_str(&content)
            .map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;

        Ok(output)
    }
}
