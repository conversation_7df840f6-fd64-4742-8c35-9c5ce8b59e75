[package]
name = "approck"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["granite"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
approck-macros = { path = "../approck-macros" }
chasetls = { workspace = true }
granite = { workspace = true }

bytes = { workspace = true }
chrono = { workspace = true }
clap = { workspace = true, features = ["derive"]}
cookie = { workspace = true }
dashmap = { workspace = true }
futures = { workspace = true }
headers = { workspace = true }
http = { workspace = true }
http-body-util = { workspace = true }
hyper = { workspace = true }
hyper-rustls = { workspace = true }
hyper-tungstenite = { workspace = true }
hyper-util = { workspace = true, features = ["tokio", "server", "http1", "http2"] }
maud = {workspace = true}
mime = { workspace = true }
phf = { workspace = true }
rand = { workspace = true }
reqwest = { workspace = true, features = ["stream"] }
rustls = { workspace = true, features = ["aws_lc_rs"] }
rustls-pemfile = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
serde_qs = { workspace = true }
tokio = { workspace = true , features = ["full"] }
tokio-rustls = { workspace = true }
tokio-stream = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
tungstenite = { workspace = true }
url = { workspace = true }
