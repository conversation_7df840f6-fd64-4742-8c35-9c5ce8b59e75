#[path = "libλ.rs"]
pub mod libλ;
mod module;
mod web;

pub trait App: approck::App + approck::server::App {}
pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base {}

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub webserver: approck::server::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
}

#[derive(Debug)]
pub struct IdentityStruct {}

bux::document! {
    pub struct DocumentStruct {}

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            use bux::document::Base;
            use maud::html;
            html!(
                (Base::render_body_inner(self))
            )
        }
    }
}

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;
    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct {})
    }
}

impl App for AppStruct {}
impl Identity for IdentityStruct {}
impl Document for DocumentStruct {}
