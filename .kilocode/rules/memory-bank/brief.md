Meta points:

1. We value consistency
2. We minimize boilerplate
3. We value convention over configuration
4. We avoid complexity

--

### Crate types:

1. Regular crates
2. Application crates - Cargo.toml has [package.metadata.acp] with `app` key
3. Module crates - Cargo.toml has [package.metadata.acp] with `module` key

### Application traits:

    pub trait App {}
    pub trait Identity {
        fn web_usage(&self) -> bool { true }
        fn api_usage(&self) -> bool { true }
    }
    pub trait Document {}

### Naming Conventions:

1. snake_case for both rust and typescript variables and methods.
2. CamelCase for rust types and typescript classes.
3. ALL_CAPS for rust constants and typescript const variables.

### Architecture:

- `approck`: main application framework with modules like `approck-postgres` and `approck-redis`
- Applications: core application crate + extended modules
- <PERSON><PERSON><PERSON> can extend other modules via `extends` key in Cargo.toml

### Use these commands:

- `./acp build` for full check of build
- `./acp build -p <app>` for building a specific app
- `./acp check` for checking syntax
- `./acp format` for formatting
- `./acp test` for testing
- `./acp test -p <crate>` for testing a specific crate
- `./acp workspace` for workspace info about apps, modules, and crates
- `rg ...` for finding code
- `fd ...` for finding files
- `find ...` for finding files
