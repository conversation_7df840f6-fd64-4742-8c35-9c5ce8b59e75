# Architecture Overview

acp7r has two distinct sides:

1. compiler ecosystem
2. runtime ecosystem

## Compiler Ecosystem

The following crates make up the compiler ecosystem:

- `approck-compiler`: used to build approck application generated source code
- `approck-macros`: provides macros for the approck framework
- `bux-compiler` : provides macro generation for `bux-macros`
- `bux-macros`: exposes proc macros for the `bux` user inteface framework
- `granite-compiler`: powers proc macros defined in `granite-macros`
- `granite-macros`: exposes proc macros for the `granite` framework
- `meta/acp`: top level wrapper around cargo, the compilation process, typescript, and esbuild
- `meta/acp-config`: provides configuration for the `acp` build system
- `meta/acp-init`: bootstraping project to setup the monorepo correctly

## Runtime Ecosystem

The following components

### Foundation Layer

- **granite** (`lib/granite`): Foundation utilities and error handling system
- **approck** (`lib/approck`): Main web application framework
- **bux** (`lib/bux`): User interface framework for building web applications

### Database Modules

- **approck postgres** (`lib/approck-postgres`): PostgreSQL integration with connection pooling
- **approck redis** (`lib/approck-redis`): Redis integration for caching and sessions

### Module System

- `auth-fence`: Authentication and authorization
- `api-sentry`: Sentry error tracking integration
- `api-stripe`: Stripe payment processing
- `api-twilio`: Twilio SMS/voice integration
- `msg-io`: Message handling system

### Application Layer

Applications are composed of multiple crates following this pattern.

1. Each app lives in a subfolder, usually named after the client or project
2. Each app is given a short alphanumeric crate name. Examples of this are `df4l`, `appcove`, `reo`, `ergo`.
3. The top level app crate contains the main application logic and configuration. It can see all of the sub crates, and implement traits defined in their respective App and Module traits.
4. The name of each sub crate must be {app}-{feature}. Examples of this are `df4l-admin`, `df4l-public`, `df4l-advisor`.
5. Each application has a `{app}-zero` crate which contains shared types and utilities for the application family, because all of the crates in the application can see it, avoiding cyclic dependancies. It should be minimally used, only for things that are truly shared.

## Key Design Patterns

### Application `Cargo.toml`

- Each application gets a port unique to the workspace
- Each application specifies a list of module crates that it extends

Here is an example of a section of `Cargo.toml` for `myapp`:

```toml
[package.metadata.acp]
app.port = 3009
extends = ["myapp-zero", "myapp-admin", "myapp-public", "approck", "bux", "granite", "auth-fence"]
```

Any crate mentioned in `extends`:

- must have `package.metadata.acp.module` defined in it's `Cargo.toml`,
- must be included as a dependency in the top level app crate.
- must have a coresponding `{app}src/module/{crate_name}.rs` file. This is where the module traits are implemented by the application.

### Application Structure Pattern

Every application, it it's `lib.rs`, must define:

1. `pub struct AppConfig`
2. `pub struct AppStruct`
3. `pub struct IdentityStruct`
4. `impl approck::App for AppStruct`
5. `impl approck::Identity for IdentityStruct`

#### `AppConfig` struct

- must contain fields for each module that is included into the application.
- for example: `pub redis: approck_redis::ModuleConfig,`
- must implment `serde::Deserialize`

#### `AppStruct` struct

- must contain a field for each module that is included into the application.
- for example: `pub redis: approck_redis::ModuleStruct,`
- must implement `approck::App`

#### `IdentityStruct` struct

- This struct represents the user or service accessing the application
- It must contain enough information to authenticate/authorize requests
- Generally it will have an attibute for each module that deals with authentication related matters, like `.auth_fence`

#### `impl approck::App for AppStruct`

- Must define a `new(config: Self::Config)` which takes the `AppConfig` as an owned type and produces an `AppStruct`
- `new(...)` is sync
- Must define an `init(&self)` which is async and returns a `granite::Result<()>`
- `init(...)` is async
- `init(...)` must call `.init()` on each module field.

#### Example of an approck application setup that uses 4 modules

```rust
///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub redis: approck_redis::ModuleConfig,
    pub postgres: approck_postgres::ModuleConfig,
    pub webserver: approck::server::ModuleConfig,
    pub auth_fence: auth_fence::types::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
    pub postgres: approck_postgres::ModuleStruct,
    pub redis: approck_redis::ModuleStruct,
    pub auth_fence: auth_fence::types::ModuleStruct,
}

#[derive(Debug)]
pub enum IdentityStruct {
    Anonymous,
    User(auth_fence::api::identity::Identity),
}

pub use crate::web::Document::Document as DocumentStruct;

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;

    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
            postgres: approck_postgres::ModuleStruct::new(config.postgres)?,
            redis: approck_redis::ModuleStruct::new(config.redis)?,
            auth_fence: auth_fence::types::ModuleStruct::new(config.auth_fence)?,
        })
    }

    async fn init(&self) -> granite::Result<()> {
        approck::Module::init(&self.redis).await?;
        approck::Module::init(&self.postgres).await?;
        approck::Module::init(&self.webserver).await?;
        approck::Module::init(&self.auth_fence).await?;

        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);
        Ok(())
    }

    async fn auth(&self, req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        use auth_fence::App;
        let auth_fence = self.auth_fence_system();

        let mut redis = match self.redis.get_dbcx().await {
            Ok(redis) => redis,
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting Redis connection: {}",
                    e
                ));
            }
        };

        match auth_fence
            .get_user_identity(&req.session_token(), &mut redis)
            .await
        {
            Ok(user_info) => match user_info {
                Some(user_info) => Ok(IdentityStruct::User(user_info)),
                None => Ok(IdentityStruct::Anonymous),
            },
            Err(_e) => Ok(IdentityStruct::Anonymous),
        }
    }
}

impl approck::Identity for IdentityStruct {}
```

## Application and Module Organziation

App crates must have these subdirectories and files:

- `src/lib.rs` - main application code
- `src/libλ.rs` - GENERATED code for route handling
- `src/libλ.ts` - GENERATED typescript code for the application
- `src/main.rs` - entry point for the application - this should have `approck::main!()` in it.
- `src/module/mod.rs` - module index file to import all module
- `src/module/{module_name}.rs` - module implementation code
- `src/web/Document.rs` - The UI/Layout/Document that powers the html structure of the application
- `src/web/Document.ts` - Typescript code for the document
- `src/web/Document.mcss` - styles for the document

Module crates must have these subdirectories and files:

- `src/lib.rs` - Defines the module with `struct ModuleConfig`, `struct ModuleStruct`, `trait App`, `trait Identity`

Module and Web crates may have the following:

- `src/core/` - contains core types and utilities for the module
- `src/api/` - contains the app/module API which does database queries and business logic
- `src/web/` - web related handlers, can be considered a "document root"
