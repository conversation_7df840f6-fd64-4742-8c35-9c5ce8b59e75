[package]
name = "df4l-admin"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["df4l-advisor", "df4l-zero", "approck", "bux", "granite", "addr-iso"]


[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }

maud = { workspace = true }

df4l-advisor = { path = "../df4l-advisor" }
df4l-zero = { path = "../df4l-zero" }
auth-fence = { workspace = true }
addr-iso = { workspace = true }
rand = { workspace = true }
chrono = { workspace = true }
