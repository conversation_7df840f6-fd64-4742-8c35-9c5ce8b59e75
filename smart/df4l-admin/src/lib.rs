pub mod api;
pub mod web;

pub trait App: approck::App + approck_postgres::App + auth_fence::App {}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;

    fn agency_list(&self) -> bool;
    fn agency_add(&self) -> bool;
    fn agency_read(&self, agency_uuid: granite::Uuid) -> bool;
    fn agency_write(&self, agency_uuid: granite::Uuid) -> bool;
    fn agency_advisor_list(&self, agency_uuid: granite::Uuid) -> bool;
    fn agency_client_list(&self, agency_uuid: granite::Uuid) -> bool;

    fn advisor_list(&self) -> bool;
    fn advisor_add(&self) -> bool;
    fn advisor_read(&self, advisor_uuid: granite::Uuid) -> bool;
    fn advisor_write(&self, advisor_uuid: granite::Uuid) -> bool;

    fn client_list(&self) -> bool;
    fn client_add(&self) -> bool;
    fn client_write(&self, client_uuid: granite::Uuid) -> bool;
    fn client_read(&self, client_uuid: granite::Uuid) -> bool;
}

pub trait Document: bux::document::Cliffy {}

pub trait DocumentPublic: bux::document::Base {}

pub fn ml_agency(agency_uuid: granite::Uuid) -> String {
    format!("/admin/agency/{}/", agency_uuid)
}

pub fn ml_advisor(advisor_uuid: granite::Uuid) -> String {
    format!("/admin/advisor/{}/", advisor_uuid)
}

pub fn ml_client(client_uuid: granite::Uuid) -> String {
    format!("/admin/client/{}/", client_uuid)
}
pub fn ml_client_edit(client_uuid: granite::Uuid) -> String {
    format!("/admin/client/{}/edit", client_uuid)
}
pub fn ml_client_assign_advisor(client_uuid: granite::Uuid) -> String {
    format!("/admin/client/{}/assign_advisor", client_uuid)
}
pub fn ml_client0(client_uuid: granite::Uuid) -> String {
    format!("/admin/client0/{}/", client_uuid)
}
pub fn ml_client0_edit(client_uuid: granite::Uuid) -> String {
    format!("/admin/client0/{}/edit", client_uuid)
}

pub fn ml_agency_edit(agency_uuid: granite::Uuid) -> String {
    format!("/admin/agency/{}/edit", agency_uuid)
}

pub fn ml_agency_delete(agency_uuid: granite::Uuid) -> String {
    format!("/admin/agency/{}/delete", agency_uuid)
}

pub fn ml_agency_advisorlist(agency_uuid: granite::Uuid) -> String {
    format!("/admin/agency/{}/advisorlist/", agency_uuid)
}

pub fn ml_agency_clientlist(agency_uuid: granite::Uuid) -> String {
    format!("/admin/agency/{}/clientlist/", agency_uuid)
}

pub fn ml_agency_clientlist0(agency_uuid: granite::Uuid) -> String {
    format!("/admin/agency/{}/clientlist0/", agency_uuid)
}

pub fn ml_agency_add_advisor(agency_uuid: granite::Uuid) -> String {
    format!("/admin/agency/{}/add_advisor", agency_uuid)
}

pub fn ml_admin_advisor_client_add(advisor_uuid: granite::Uuid) -> String {
    format!("/admin/advisor/{}/client/add", advisor_uuid)
}

pub fn ml_admin_advisor_client_list(advisor_uuid: granite::Uuid) -> String {
    format!("/admin/advisor/{}/client/", advisor_uuid)
}

pub fn ml_admin_advisor_client0_list(advisor_uuid: granite::Uuid) -> String {
    format!("/admin/advisor/{}/client0/", advisor_uuid)
}

pub fn ml_advisor_edit(advisor_uuid: granite::Uuid) -> String {
    format!("/admin/advisor/{}/edit", advisor_uuid)
}

pub fn ml_advisor_delete(advisor_uuid: granite::Uuid) -> String {
    format!("/admin/advisor/{}/delete", advisor_uuid)
}

pub fn ml_advisor_add_agency(advisor_uuid: granite::Uuid) -> String {
    format!("/admin/advisor/{}/add_agency", advisor_uuid)
}

pub fn ml_client_list() -> String {
    "/admin/client/".to_string()
}

pub fn ml_client0_list() -> String {
    "/admin/client0/".to_string()
}

pub fn ml_client_add() -> String {
    "/admin/client/add".to_string()
}

pub fn ml_agency_list() -> String {
    "/admin/agency/".to_string()
}

pub fn ml_agency_add() -> String {
    "/admin/agency/add".to_string()
}
