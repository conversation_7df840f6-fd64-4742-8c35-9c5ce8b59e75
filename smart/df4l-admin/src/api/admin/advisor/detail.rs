#[approck::api]
pub mod admin_advisor_detail {
    use granite::return_authorization_error;
    use maud::{Markup, html};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub advisor_uuid: Uuid,
        pub identity_uuid: Option<Uuid>,
        pub agency_uuid: Option<Uuid>,
        pub agency_name: Option<String>,
        pub agency_esid: Option<String>,
        pub advisor_esid: String,
        pub gbu_advisor_esid: Option<String>,
        pub create_ts: DateTimeUtc,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub country: Option<String>,
        pub active: bool,
        pub admin_note: Option<String>,
        pub statelics: Vec<Statelic>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Statelic {
        pub state_code: String,
        pub label: String,
    }

    impl Output {
        pub fn name(&self) -> String {
            format!("{} {}", self.first_name, self.last_name)
        }

        pub fn address(&self) -> String {
            let mut parts = Vec::new();

            if let Some(addr1) = &self.address1 {
                parts.push(addr1.clone());
            }
            if let Some(addr2) = &self.address2 {
                parts.push(addr2.clone());
            }
            if let Some(city) = &self.city {
                parts.push(city.clone());
            }
            if let Some(state) = &self.state {
                parts.push(state.clone());
            }
            if let Some(zip) = &self.zip {
                parts.push(zip.clone());
            }
            if let Some(country) = &self.country {
                parts.push(country.clone());
            }

            parts.join(" ")
        }

        pub fn statelic_html(&self) -> Markup {
            html! {
                div.mb-2 {
                    @for statelic in &self.statelics {
                        label-tag.primary.me-1 {
                            (statelic.label)
                        }
                    }
                }
            }
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!(
                "insufficient permissions to advisor {}",
                input.advisor_uuid
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                advisor_uuid: Uuid,
                identity_uuid: Option<Uuid>,
                agency_uuid: Option<Uuid>,
                agency_name: Option<String>,
                agency_esid: Option<String>,
                advisor_esid: String,
                gbu_advisor_esid: Option<String>,
                create_ts: DateTimeUtc,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                country: Option<String>,
                active: bool,
                admin_note: Option<String>,
            };
            SELECT
                advisor_uuid,
                identity_uuid,
                agency_uuid,
                (
                    SELECT name
                    FROM df4l.agency
                    WHERE agency_uuid = df4l.advisor.agency_uuid
                ) AS agency_name,
                (
                    SELECT agency_esid
                    FROM df4l.agency
                    WHERE agency_uuid = df4l.advisor.agency_uuid
                ) AS agency_esid,
                advisor_esid,
                gbu_advisor_esid,
                create_ts,
                first_name,
                last_name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                country,
                active,
                admin_note
            FROM
                df4l.advisor
            WHERE true
                AND advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        // Get state licenses for this advisor
        let statelic_rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                state_code: String,
                label: String,
            };
            SELECT
                asl.state_code,
                sl.label
            FROM
                df4l.advisor_statelic asl
            JOIN
                df4l.statelic sl ON asl.state_code = sl.state_code
            WHERE
                asl.advisor_uuid = $advisor_uuid::uuid
            ORDER BY
                sl.label
        )
        .await?;

        // Convert the rows to Statelic structs
        let statelics = statelic_rows
            .into_iter()
            .map(|r| Statelic {
                state_code: r.state_code,
                label: r.label,
            })
            .collect();

        Ok(Output {
            advisor_uuid: row.advisor_uuid,
            identity_uuid: row.identity_uuid,
            agency_uuid: row.agency_uuid,
            agency_name: row.agency_name,
            agency_esid: row.agency_esid,
            advisor_esid: row.advisor_esid,
            gbu_advisor_esid: row.gbu_advisor_esid,
            create_ts: row.create_ts,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,
            country: row.country,
            active: row.active,
            admin_note: row.admin_note,
            statelics,
        })
    }
}
