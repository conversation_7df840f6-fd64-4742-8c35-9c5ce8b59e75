import "./add.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";
import "@addr-iso/input/address_us_select.mts";

import { SE } from "@granite/lib.mts";
import { admin_advisor_add } from "@crate/api/admin/advisor/addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";
import { AddressUsSelect } from "@addr-iso/input/address_us_select.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $gbu_advisor_esid: BuxInputTextString = SE($form, "[name=gbu_advisor_esid]");
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $email: BuxInputTextString = SE($form, "[name=email]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $address1: BuxInputTextString = SE($form, "[name=address1]");
const $address2: BuxInputTextString = SE($form, "[name=address2]");
const $city: BuxInputTextString = SE($form, "[name=city]");
const $state: AddressUsSelect = SE($form, "[name=state]");
const $zip: BuxInputTextString = SE($form, "[name=zip]");
const $admin_note: BuxInputTextareaString = SE($form, "[name=admin_note]");

new FormPanel({
    $form,
    api: admin_advisor_add.api,
    on_cancel: go_back,

    err: (errors) => {
        $gbu_advisor_esid.set_e(errors.gbu_advisor_esid);
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $email.set_e(errors.email);
        $phone.set_e(errors.phone);
        $address1.set_e(errors.address1);
        $address2.set_e(errors.address2);
        $city.set_e(errors.city);
        $state.set_e(errors.state);
        $zip.set_e(errors.zip);
        $admin_note.set_e(errors.admin_note);
    },

    get: () => {
        const r = {
            gbu_advisor_esid: $gbu_advisor_esid.value_option,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value_option,
            phone: $phone.value_option,
            address1: $address1.value_option,
            address2: $address2.value_option,
            city: $city.value_option,
            state: $state.value_option,
            zip: $zip.value_option,
            admin_note: $admin_note.value_option,
        };
        return r;
    },

    set: (_value) => {
        // Unused parameter is prefixed with underscore
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
