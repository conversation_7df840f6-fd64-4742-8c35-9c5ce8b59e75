#[approck::http(GET /admin/advisor/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Add Advisor");

        let mut form_panel =
            bux::component::add_cancel_form_panel("Advisor Information", "/admin/advisor/");

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("first_name", "First Name", None))
                    (bux::input::text::string::name_label_value("last_name", "Last Name", None))
                    (bux::input::text::string::name_label_value("gbu_advisor_esid", "GBU ID", None))
                    (bux::input::text::string::name_label_value("email", "Email:", None))
                    (bux::input::text::string::name_label_value("phone", "Phone:", None))
                }
                cell-6 {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", None))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", None))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", None))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", None, "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", None))
                    }
                    (bux::input::textarea::string::name_label_value("admin_note", "Admin Note", None))
                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
