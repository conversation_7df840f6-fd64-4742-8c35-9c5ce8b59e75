#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/reassign-identity; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::admin::advisor::detail::admin_advisor_detail;
        use auth_fence::api::admin::identity::list::list;

        let advisor = admin_advisor_detail::call(
            app,
            identity,
            admin_advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        // Get list of all active identities
        let identities = list::call(
            app,
            identity,
            list::Input {
                keyword: None,
                active: Some(true), // Only show active identities
            },
        )
        .await?;

        doc.set_title("Reassign Identity");
        doc.add_css("/admin/advisor/mapper/reassign_identity.css");
        doc.add_js("/admin/advisor/mapper/reassign_identity.js");

        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Reassign Identity for {}", advisor.name()),
            &crate::ml_advisor(advisor.advisor_uuid),
        );

        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);

        // Create entity picker for identities
        let mut entity_picker = bux::component::entity_picker::new();
        entity_picker.set_heading("Select an identity to assign to this advisor:");

        entity_picker.append(
            "🚫",
            "Remove Current Identity",
            "javascript:void(0)",
            "Action",
            "remove-identity",
        );


        // Add available identities
        let identity_data: Vec<(String, String, String, String, String, bool)> = identities.identities
            .iter()
            .map(|identity_item| {
                let icon = match identity_item.identity_type.as_str() {
                    "User" => "👤",
                    "Service" => "🤖",
                    _ => "❓",
                };

                // Check if this is the currently assigned identity
                let is_current = advisor.identity_uuid == Some(identity_item.identity_uuid);

                (
                    icon.to_string(),
                    identity_item.name.clone(),
                    "javascript:void(0)".to_string(),
                    identity_item.identity_type.clone(),
                    identity_item.identity_uuid.to_string(),
                    is_current,
                )
            })
            .collect();

        for (icon, name, href, role, uuid_str, _is_current) in &identity_data {
            entity_picker.append(
                icon,
                name,
                href,
                role,
                uuid_str,
            );
        }
        // Add "Remove Identity" option first

        // Find current identity details if assigned
        let current_identity_info = if let Some(current_uuid) = advisor.identity_uuid {
            identities.identities.iter().find(|id| id.identity_uuid == current_uuid)
        } else {
            None
        };

        form_panel.add_body(html!(
            div.current-assignment {
                h3 { "Current Assignment" }
                @if let Some(current_identity) = current_identity_info {
                    div.current-identity-details {
                        p {
                            "Currently assigned to: "
                            strong { (current_identity.name) }
                            " (" (current_identity.identity_type) ")"
                        }
                        p.identity-uuid {
                            "Identity UUID: "
                            code { (current_identity.identity_uuid) }
                        }
                    }
                } @else {
                    p.no-assignment { "No identity currently assigned" }
                }
            }
            
            div.identity-picker {
                (entity_picker)
            }

            input type="hidden" name="identity_uuid" value=""
                data-current-identity-uuid=(advisor.identity_uuid.map(|uuid| uuid.to_string()).unwrap_or_default()) {}
        ));

        doc.add_body(html!(
            bux-action-panel {
                (form_panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
