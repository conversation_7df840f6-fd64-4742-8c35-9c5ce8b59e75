#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            h1 {"Client Details"}
            p {"TODO:DF4L: essential details relating to the agency/advisor connection"}
        ));

        Response::HTML(doc.into())
    }
}
