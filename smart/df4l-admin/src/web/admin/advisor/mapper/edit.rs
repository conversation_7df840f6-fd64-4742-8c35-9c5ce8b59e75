#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        use crate::api::admin::advisor::detail::admin_advisor_detail;
        use crate::api::admin::agency::list::admin_agency_list;

        let advisor = admin_advisor_detail::call(
            app,
            identity,
            admin_advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        // Get list of all active agencies
        let agencies = admin_agency_list::call(
            app,
            identity,
            admin_agency_list::Input {
                keyword: None,
                active: Some(true), // Only show active agencies
            },
        )
        .await?;

        // Format agencies for dropdown: "Name (ESID)"
        let agency_options_strings: Vec<(String, String)> = agencies
            .agency_list
            .iter()
            .map(|a| {
                let display = format!("{} ({})", a.name, a.agency_esid);
                (a.agency_uuid.to_string(), display)
            })
            .collect();

        // Convert to the expected type for nilla_select
        let agency_options: Vec<(&str, &str)> = agency_options_strings
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        doc.set_title("Edit Advisor");

        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Edit Advisor Details For {}", advisor.name()),
            &crate::ml_advisor(advisor.advisor_uuid),
        );

        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("first_name", "First Name", Some(&advisor.first_name)))
                    (bux::input::text::string::name_label_value("last_name", "Last Name", Some(&advisor.last_name)))
                    (bux::input::text::string::name_label_readonly("advisor_esid", "Advisor ID", &advisor.advisor_esid))
                    (bux::input::text::string::name_label_value("gbu_advisor_esid", "GBU ID", advisor.gbu_advisor_esid.as_deref()))
                    (bux::input::select::nilla::nilla_select(
                        "agency_uuid",
                        "Agency",
                        &agency_options,
                        advisor.agency_uuid.map(|uuid| uuid.to_string()).as_deref()
                    ))
                    (bux::input::text::string::name_label_value("email", "Email:", advisor.email.as_deref()))
                    (bux::input::text::string::name_label_value("phone", "Phone:", advisor.phone.as_deref()))
                }
                cell-6 {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", advisor.address1.as_deref()))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", advisor.address2.as_deref()))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", advisor.city.as_deref()))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", advisor.state.as_deref(), "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", advisor.zip.as_deref()))
                    }
                    (bux::input::textarea::string::name_label_value("admin_note", "Admin Note", advisor.admin_note.as_deref()))
                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
