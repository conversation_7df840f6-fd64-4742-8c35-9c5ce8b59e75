#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::admin::advisor::detail::admin_advisor_detail;

        let advisor = admin_advisor_detail::call(
            app,
            identity,
            admin_advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        // Fetch identity details if advisor has an identity_uuid
        let identity_details = if let Some(identity_uuid) = advisor.identity_uuid {
            match auth_fence::api::admin::identity::detail::detail::call(
                app,
                identity,
                auth_fence::api::admin::identity::detail::detail::Input { identity_uuid },
            )
            .await
            {
                Ok(details) => Some(details),
                Err(e) => {
                    approck::warn!(
                        "Failed to fetch identity details for advisor {}: {}",
                        advisor.advisor_uuid,
                        e
                    );
                    None
                }
            }
        } else {
            None
        };

        // Fetch SSO providers if identity exists
        let sso_providers = if let Some(identity_uuid) = advisor.identity_uuid {
            match auth_fence::api::identity::sso::providers::index::call(
                app,
                identity,
                auth_fence::api::identity::sso::providers::index::Input { identity_uuid },
            )
            .await
            {
                Ok(providers) => Some(providers.providers),
                Err(e) => {
                    approck::warn!(
                        "Failed to fetch SSO providers for advisor {}: {}",
                        advisor.advisor_uuid,
                        e
                    );
                    None
                }
            }
        } else {
            None
        };

        doc.set_title("Advisor Details");

        // Prepare display values
        let display_email = advisor.email.as_deref().unwrap_or("Email not available");
        let display_phone = advisor.phone.as_deref().unwrap_or("Phone not available");

        doc.add_body(html!(
            admin-advisor-detail {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (advisor.name()) }
                                    p.phone.mb-0 {
                                        (display_phone)
                                    }
                                    p.email {
                                        @if let Some(email) = &advisor.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                    a.btn.primary.block href=(df4l_advisor::ml_advisor(advisor.advisor_uuid)) { "Visit Advisor Dashboard" }
                                    hr;
                                    @if advisor.active {
                                        label-tag.success { "Active Advisor" }
                                    } @else {
                                        label-tag.danger { "Inactive Advisor" }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        // Advisor Identification Panel
                        panel.advisor-identification {
                            header {
                                div {
                                    h5 { "Advisor Identification" }
                                    p { "Core advisor identifiers and agency assignment information." }
                                }
                                div.header-buttons {
                                    (bux::button::link::edit(&crate::ml_advisor_edit(advisor.advisor_uuid)))
                                    " "
                                    (bux::button::link::delete(&crate::ml_advisor_delete(advisor.advisor_uuid)))
                                }
                            }
                            content {
                                // Top section with Advisor ID and GBU ID
                                grid-2 {
                                    panel.gray-tile {
                                        content {
                                            hbox {
                                                i.far.fa-id-badge.advisor-id aria-hidden="true" {}
                                                dl {
                                                    dt { "Advisor ID" }
                                                    dd { (advisor.advisor_esid) }
                                                }
                                            }
                                        }
                                    }
                                    panel.gray-tile {
                                        content {
                                            hbox {
                                                i.fas.fa-pen.advisor-gbu aria-hidden="true" {}
                                                dl {
                                                    dt { "GBU Advisor ID" }
                                                    dd {
                                                        @if let Some(gbu_id) = &advisor.gbu_advisor_esid {
                                                            (gbu_id)
                                                        } @else {
                                                            "None"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-building aria-hidden="true" {}
                                            dl {
                                                dt { "Agency Assignment" }
                                                dd {
                                                    @if advisor.agency_uuid.is_some() {
                                                        a href=(crate::ml_agency(advisor.agency_uuid.unwrap_or_default())) {
                                                            (advisor.agency_name.clone().unwrap_or_default()) " (" (advisor.agency_esid.clone().unwrap_or_default()) ")"
                                                        }
                                                    } @else {
                                                        "No agency assigned "
                                                        span {
                                                            "("
                                                            a.edit-link href=(crate::ml_advisor_add_agency(advisor.advisor_uuid)) { "Assign Agency" }
                                                            ")"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-calendar-plus aria-hidden="true" {}
                                            dl {
                                                dt { "Created On" }
                                                dd {
                                                    (advisor.create_ts.format("%b %d, %Y %I:%M %p"))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // Identity Information Panel
                        panel.identity-information {
                            header {
                                div {
                                    h5 { "Identity Information" }
                                    p { "Login credentials and account access details for this advisor." }
                                }
                                @if advisor.identity_uuid.is_some() {
                                    div.header-buttons {
                                        (bux::button::link::label_icon_class("Reassign Advisor To Another Identity", "fas fa-exchange-alt", &format!("/admin/advisor/{}/reassign-identity", advisor.advisor_uuid), "sm primary"))
                                        " "
                                        (bux::button::link::label_icon_class("View Details", "fas fa-eye", &auth_fence::api::admin::identity::types::ml_admin(&advisor.identity_uuid.unwrap(), ""), "sm secondary"))
                                    }
                                }
                            }
                            content {
                                @if let Some(identity_uuid) = &advisor.identity_uuid {
                                    ul.data-list {
                                        li {
                                            hbox {
                                                i.fas.fa-user-circle aria-hidden="true" {}
                                                dl {
                                                    dt { "Identity UUID" }
                                                    dd {
                                                        a href=(auth_fence::api::admin::identity::types::ml_admin(identity_uuid, "")) {
                                                            (identity_uuid)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        @if let Some(ref details) = identity_details {
                                            li {
                                                hbox {
                                                    i.fas.fa-user aria-hidden="true" {}
                                                    dl {
                                                        dt { "Name" }
                                                        dd { (details.name) }
                                                    }
                                                }
                                            }
                                            @if let Some(ref email) = details.email {
                                                li {
                                                    hbox {
                                                        i.fas.fa-envelope aria-hidden="true" {}
                                                        dl {
                                                            dt { "Email" }
                                                            dd {
                                                                a href=(format!("mailto:{}", email)) { (email) }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        } @else {
                                            li {
                                                hbox {
                                                    i.fas.fa-exclamation-triangle aria-hidden="true" {}
                                                    dl {
                                                        dt { "Details" }
                                                        dd {
                                                            span.text-muted { "Unable to load identity details" }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        // Sign-in Methods Section
                                        li {
                                            hbox {
                                                i.fas.fa-key aria-hidden="true" {}
                                                dl {
                                                    dt { "Sign-in Methods" }
                                                    dd {
                                                        @if let Some(ref providers) = sso_providers {
                                                            @let google_provider = providers.iter().find(|p| p.ssopro_xsid.to_lowercase() == "google");
                                                            @let microsoft_provider = providers.iter().find(|p| p.ssopro_xsid.to_lowercase() == "microsoft");

                                                            div.signin-methods {
                                                                @if let Some(google) = google_provider {
                                                                    div.signin-method {
                                                                        i.fab.fa-google aria-hidden="true" {}
                                                                        span { "Sign in with Google: " }
                                                                        @if google.is_connected {
                                                                            label-tag.success { "Enabled" }
                                                                        } @else {
                                                                            label-tag.warning { "Disabled" }
                                                                        }
                                                                    }
                                                                } @else {
                                                                    div.signin-method {
                                                                        i.fab.fa-google aria-hidden="true" {}
                                                                        span { "Sign in with Google: " }
                                                                        label-tag.secondary { "Not Available" }
                                                                    }
                                                                }

                                                                @if let Some(microsoft) = microsoft_provider {
                                                                    div.signin-method {
                                                                        i.fab.fa-microsoft aria-hidden="true" {}
                                                                        span { "Sign in with Microsoft: " }
                                                                        @if microsoft.is_connected {
                                                                            label-tag.success { "Enabled" }
                                                                        } @else {
                                                                            label-tag.warning { "Disabled" }
                                                                        }
                                                                    }
                                                                } @else {
                                                                    div.signin-method {
                                                                        i.fab.fa-microsoft aria-hidden="true" {}
                                                                        span { "Sign in with Microsoft: " }
                                                                        label-tag.secondary { "Not Available" }
                                                                    }
                                                                }

                                                                div.signin-method {
                                                                    i.fas.fa-lock aria-hidden="true" {}
                                                                    span { "Sign in with Login and Password: " }
                                                                    @if identity_details.is_some() {
                                                                        label-tag.success { "Enabled" }
                                                                    } @else {
                                                                        label-tag.warning { "Unknown" }
                                                                    }
                                                                }
                                                            }
                                                        } @else {
                                                            span.text-muted { "Unable to load sign-in methods" }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } @else {
                                    ul.data-list {
                                        li {
                                            hbox {
                                                i.fas.fa-user-slash aria-hidden="true" {}
                                                dl {
                                                    dt { "Identity Status" }
                                                    dd {
                                                        "No identity linked "
                                                        span.text-muted { "(Login not available)" }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // Contact Information Panel
                        panel.contact-information {
                            header {
                                div {
                                    h5 { "Contact Information" }
                                    p { "Advisor contact details and address information." }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-envelope-open-text aria-hidden="true" {}
                                            dl {
                                                dt { "Email" }
                                                dd {
                                                    @if let Some(email) = &advisor.email {
                                                        a href=(format!("mailto:{}", email)) { (email) }
                                                    } @else {
                                                        "No email provided"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-phone aria-hidden="true" {}
                                            dl {
                                                dt { "Phone" }
                                                dd {
                                                    @if let Some(phone) = &advisor.phone {
                                                        (phone)
                                                    } @else {
                                                        "No phone provided"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-map-marker-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Address" }
                                                dd {
                                                    @if !advisor.address().trim().is_empty() {
                                                        (advisor.address())
                                                    } @else {
                                                        "No address provided"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // Administrative Information Panel
                        panel.administrative-information {
                            header {
                                div {
                                    h5 { "Administrative Information" }
                                    p { "Status, licensing, and administrative details for this advisor." }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-toggle-on aria-hidden="true" {}
                                            dl {
                                                dt { "Status" }
                                                dd {
                                                    @if advisor.active {
                                                        label-tag.success { "Active" }
                                                    } @else {
                                                        label-tag.danger { "Inactive" }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-id-badge aria-hidden="true" {}
                                            dl {
                                                dt { "States of Licensure" }
                                                dd {
                                                    @if advisor.statelics.is_empty() {
                                                        label-tag.warning { "None" }
                                                    } @else {
                                                        @for (i, statelic) in advisor.statelics.iter().enumerate() {
                                                            @if i > 0 {
                                                                " "
                                                            }
                                                            label-tag.primary { (statelic.label) }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    @if let Some(admin_note) = &advisor.admin_note {
                                        @if !admin_note.trim().is_empty() {
                                            li {
                                                hbox {
                                                    i.fas.fa-sticky-note aria-hidden="true" {}
                                                    dl {
                                                        dt { "Admin Note" }
                                                        dd {
                                                            (admin_note)
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
