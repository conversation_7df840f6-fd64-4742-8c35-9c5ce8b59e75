admin-advisor-detail {

    contact-info {
        text-align: center;

        h1 {
            font-size: 18pt;
        }

        *:last-child {
            margin-bottom: 0;
        }
    }

    dl {
        margin-bottom: 0;

        dt {
            font-size: .875em;
            font-weight: normal;
        }

        dd {

            &:last-child {
                margin-bottom: 0;
            }

            label-tag {
                font-size: 9pt;
            }
        }
    }


    panel {

        header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 1rem;
            background-color: #fff !important;
            border-bottom: none !important;
            padding-top: 1rem !important;

            h5 {
                font-size: 1.25rem;
                font-weight: 600;
                color: #212529;
                margin-bottom: 0.25rem;
            }

            p {
                margin-bottom: 0;
            }
        }

        content {

            ul.data-list {
                margin-bottom: 0;
                padding-left: 0;
                list-style-type: none;

                li {
                    border-bottom: 1px solid #ddd;
                    padding: .5rem 0;

                    hbox {
                        align-items: center;
                        justify-content: flex-start;

                        i {
                            width: 2rem;
                            text-align: center;
                        }

                        dl {
                            display: flex;
                            justify-content: space-between;
                            width: 100%;
                            align-items: center;

                            dt {
                                font-size: 12pt;
                            }
                        }
                    }

                    &:first-child {
                        border-top: 1px solid #ddd;
                    }
                }
            }
        }

        &.gray-tile {
            background-color: #f5f5f5;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 0;

            content {

                hbox {
                    align-items: center;
                    justify-content: flex-start;

                    i {
                        width: 3rem;
                        height: 3rem;
                        flex-shrink: 0;
                        background-color: white;
                        border-radius: .375rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }
        }

        /* Advisor Identification Panel */
        &.advisor-identification {

            panel {

                &.gray-tile {
                    margin-bottom: 1rem;
                }
            }

            content {

                .edit-btn {
                    padding: 0;

                    i {
                        background-color: transparent;
                    }
                }
            }
        }

        /* Identity Information Panel */
        &.identity-information {
            margin-top: 1rem;

            header {
                .header-buttons {
                    display: flex;
                    gap: 0.5rem;
                    align-items: center;
                }
            }

            .signin-methods {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;

                .signin-method {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    i {
                        width: 1.5rem;
                        text-align: center;
                    }

                    span {
                        font-size: 0.9em;
                    }

                    label-tag {
                        font-size: 0.8em;
                    }
                }
            }
        }

        /* Contact Information Panel */
        &.contact-information {
            margin-top: 1rem;
        }

        /* Administrative Information Panel */
        &.administrative-information {
            margin-top: 1rem;
        }
    }

    /* Edit link styling */
    a.edit-link {
        color: #007bff;
        text-decoration: none;
        font-size: 0.9em;

        &:hover {
            text-decoration: underline;
        }
    }

    /* Muted text styling */
    .text-muted {
        color: #6c757d;
        font-size: 0.9em;
    }
}