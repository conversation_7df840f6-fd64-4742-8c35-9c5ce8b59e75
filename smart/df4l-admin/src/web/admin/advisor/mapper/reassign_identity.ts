import "./reassign_identity.mcss";
import "@bux/component/entity_picker.mts";

import { SE, Uuid, type Option } from "@granite/lib.mts";
import { admin_advisor_reassign_identity } from "@crate/api/admin/advisor/reassign_identityλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
const $identity_uuid: HTMLInputElement = SE($form, "[name=identity_uuid]");

// Get all entity picker items
const $entity_picker = SE(document, "bux-component-entity-picker");
const $entity_items = $entity_picker.querySelectorAll("li") as NodeListOf<HTMLLIElement>;

// Track selected identity
let selectedIdentityUuid: string | null = null;

// Get the current advisor's identity UUID from the form
const currentIdentityUuid = $identity_uuid.dataset["currentIdentityUuid"];

// Pre-select currently assigned identity
$entity_items.forEach(($item) => {
    const $uuid_span = $item.querySelector(".entity-uuid");
    if ($uuid_span && currentIdentityUuid && $uuid_span.textContent?.trim() === currentIdentityUuid) {
        // Pre-select this item
        $item.classList.add("selected");
        selectedIdentityUuid = currentIdentityUuid;
        $identity_uuid.value = currentIdentityUuid;
    }
});

// Add click handlers to entity picker items
$entity_items.forEach(($item) => {
    $item.addEventListener("click", (event) => {
        event.preventDefault(); // Prevent default link behavior
        // Remove selected class from all items
        $entity_items.forEach(($otherItem) => {
            $otherItem.classList.remove("selected");
        });

        // Add selected class to clicked item
        $item.classList.add("selected");

        // Get the UUID from the entity-uuid span
        const $uuid_span = $item.querySelector(".entity-uuid");
        if ($uuid_span) {
            const uuid_text = $uuid_span.textContent?.trim();

            if (uuid_text === "remove-identity") {
                selectedIdentityUuid = null;
                $identity_uuid.value = "";
            } else {
                selectedIdentityUuid = uuid_text || null;
                $identity_uuid.value = selectedIdentityUuid || "";
            }
        }
    });
});

// Form submission handler using proper FormPanel pattern
new FormPanel({
    $form,
    api: admin_advisor_reassign_identity.api,
    on_cancel: go_back,

    err: (errors) => {
        // Handle validation errors if needed
        console.error("Validation errors:", errors);
    },

    get: () => {
        return {
            advisor_uuid: $advisor_uuid.value as Uuid,
            identity_uuid: selectedIdentityUuid ? { Some: selectedIdentityUuid as Uuid } : { None: true } as Option<Uuid>,
        };
    },

    set: (_value) => {
        // Not needed for this form
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
