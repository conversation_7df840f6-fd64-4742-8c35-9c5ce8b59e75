import "./add_agency.mcss";
import "@bux/input/select/nilla.mts";

import { SE, Uuid } from "@granite/lib.mts";
import { admin_agency_advsior_link } from "@crate/api/admin/agency/advisor_linkλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $agency_uuid: BuxInputSelectNilla<Uuid> = SE($form, "[name=agency_uuid]");
// hidden advisor_uuid
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");

new FormPanel({
    $form,
    api: admin_agency_advsior_link.api,
    on_cancel: go_back,

    err: (errors) => {
        $agency_uuid.set_e(errors.agency_uuid);
    },

    get: () => {
        return {
            agency_uuid: $agency_uuid.value,
            advisor_uuid: $advisor_uuid.value,
        };
    },

    set: (_value) => {
    },

    out: (_value) => {
        // Redirect to advisor detail page
        go_next(`/admin/advisor/${$advisor_uuid.value}/`);
    },
});
