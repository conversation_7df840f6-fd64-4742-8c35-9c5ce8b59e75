#[approck::http(GET /admin/?name=String; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Admin Dashboard");

        use crate::api::admin::dashboard::admin_dashboard;
        let dashboard_data = admin_dashboard::call(app).await?;

        // Get user name for welcome message
        let user_name = identity.name().unwrap_or_else(|| "Admin".to_string());

        doc.add_body(html!(
            d2c-admin-dashboard {
                panel {
                    content {
                        header {
                            h1 { "Welcome, " (user_name) "!" }
                            p { "Here's a quick overview of your system metrics." }
                        }
                        grid-2 {
                            admin-metric {
                                i.fas.fa-building aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/agency/" { "Active Agencies" }
                                    }
                                    dd { "(" (dashboard_data.agency_count) ")" }
                                }
                                a href="/admin/agency/" aria-label="View active agencies" {
                                    i.fas.fa-chevron-right.fa-lg aria-hidden="true" {}
                                }
                            }
                            admin-metric {
                                i.fas.fa-users  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/client/" { "Active Clients" }
                                    }
                                    dd { "(" (dashboard_data.client_count) ")" }
                                }
                                a href="/admin/client/" aria-label="View active clients" {
                                    i.fas.fa-chevron-right.fa-lg aria-hidden="true" {}
                                }
                            }
                            admin-metric {
                                i.fas.fa-user-clock  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/client0/" { "Active Legacy Clients" }
                                    }
                                    dd { "(" (dashboard_data.legacy_client_count) ")" }
                                }
                                a href="/admin/client0/" aria-label="View active legacy clients" {
                                    i.fas.fa-chevron-right.fa-lg aria-hidden="true" {}
                                }
                            }
                            admin-metric {
                                i.fas.fa-user-tie  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/advisor/" { "Active Advisors" }
                                    }
                                    dd { "(" (dashboard_data.advisor_count) ")" }
                                }
                                a href="/admin/advisor/" aria-label="View active advisors" {
                                    i.fas.fa-chevron-right.fa-lg aria-hidden="true" {}
                                }
                            }
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
