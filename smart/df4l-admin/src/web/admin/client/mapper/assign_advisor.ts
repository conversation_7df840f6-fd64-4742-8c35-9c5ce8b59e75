import "./assign_advisor.mcss";
import "@bux/input/select/nilla.mts";
import "@bux/input/text/string.mts";

import { SE, Uuid } from "@granite/lib.mts";
import { admin_client_assign_advisor } from "@crate/api/admin/client/assign_advisorλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $client_uuid: HTMLInputElement = SE($form, "[name=client_uuid]");
const $advisor_uuid: BuxInputSelectNilla<Uuid> = SE($form, "[name=advisor_uuid]");

new FormPanel({
    $form,
    api: admin_client_assign_advisor.api,
    on_cancel: go_back,

    err: (errors) => {
        $advisor_uuid.set_e(errors.advisor_uuid as string | undefined);
    },

    get: () => {
        return {
            client_uuid: $client_uuid.value,
            advisor_uuid: $advisor_uuid.value,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
