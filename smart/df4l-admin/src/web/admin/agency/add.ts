import "./add.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";

import { SE } from "@granite/lib.mts";
import { admin_agency_add } from "@crate/api/admin/agency/addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $name: BuxInputTextString = SE($form, "[name=name]");
const $admin_note: BuxInputTextareaString = SE($form, "[name=admin_note]");

new FormPanel({
    $form,
    api: admin_agency_add.api,
    on_cancel: go_back,

    err: (errors) => {
        $name.set_e(errors.name);
        $admin_note.set_e(errors.admin_note);
    },

    get: () => {
        return {
            name: $name.value,
            admin_note: $admin_note.value_option,
        };
    },

    set: (value) => {
        $name.value = value.name;
        $admin_note.value = value.admin_note;
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
