#[approck::http(GET /admin/agency/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.page_nav_add_record("Add New Agency", &crate::ml_agency_add());
        doc.set_title("Agency List");

        use crate::api::admin::agency::list::admin_agency_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = admin_agency_list::call(
            app,
            identity,
            admin_agency_list::Input {
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        // Create a detail table with agencies directly in the constructor
        let mut dt = bux::component::detail_table(output.agency_list);
        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_link_column(
            "Agency Name",
            |a| crate::ml_agency(a.agency_uuid),
            |a| a.name.clone(),
        );
        dt.add_column("Agency ID", |a| html! { (a.agency_esid) });
        dt.add_create_ts(|a| a.create_ts);
        dt.add_active_status_column("Status", |a| a.active);
        dt.add_details_column(|a| crate::ml_agency(a.agency_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}
