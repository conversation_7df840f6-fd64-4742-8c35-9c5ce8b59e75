#[approck::http(GET /admin/agency/{agency_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::agency::detail::admin_agency_detail;
        use maud::html;

        let agency = admin_agency_detail::call(
            app,
            identity,
            admin_agency_detail::Input {
                agency_uuid: path.agency_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record("Edit Agency", &crate::ml_agency_edit(agency.agency_uuid));
        doc.page_nav_delete_record(
            "Delete Agency",
            &crate::ml_agency_delete(agency.agency_uuid),
        );

        doc.set_title("Agency Details");

        let mut table = bux::component::info_table(agency);
        table.set_heading("Agency Information");
        table.add_name_row(|u| &u.name);
        table.add_identifer_row("D2C ID:", |u| &u.agency_esid);
        table.add_create_ts(|u| u.create_ts);
        table.add_note_row(|u| u.admin_note.as_deref().unwrap_or(""));
        table.add_active_status_row("Status:", |u| u.active);

        doc.add_body(html!((table)));

        Ok(Response::HTML(doc.into()))
    }
}
