pub mod add_advisor;
pub mod advisorlist;
pub mod clientlist;
pub mod clientlist0;
pub mod edit;
pub mod index;

#[approck::prefix(/admin/agency/{agency_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(app: App, menu: Menu, agency_uuid: Uuid) {
        menu.set_label_name_uri(
            "Agency Details",
            app.uuid_to_label(agency_uuid),
            &crate::ml_agency(agency_uuid),
        );
        menu.add_link("Advisor List", &crate::ml_agency_advisorlist(agency_uuid));
        menu.add_link("Client List", &crate::ml_agency_clientlist(agency_uuid));
        menu.add_link(
            "Legacy Client List",
            &crate::ml_agency_clientlist0(agency_uuid),
        );
    }
    pub fn auth() {}
}
