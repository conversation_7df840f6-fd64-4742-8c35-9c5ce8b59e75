import "./edit.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/select/nilla.mts";
import "@bux/input/textarea/string.mts";

import { SE } from "@granite/lib.mts";
import { admin_agency_edit } from "@crate/api/admin/agency/editλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $agency_uuid: HTMLInputElement = SE($form, "[name=agency_uuid]");
const $agency_name: BuxInputTextString = SE($form, "[name=agency_name]");
const $agency_esid: HTMLInputElement = SE($form, "[name=agency_esid]");
const $admin_note: BuxInputTextareaString = SE($form, "[name=admin_note]");

new FormPanel({
    $form,
    api: admin_agency_edit.api,
    on_cancel: go_back,

    err: (errors) => {
        $agency_name.set_e(errors.name);
        $admin_note.set_e(errors.admin_note);
    },

    get: () => {
        return {
            agency_uuid: $agency_uuid.value,
            agency_esid: $agency_esid.value,
            name: $agency_name.value_option,
            admin_note: $admin_note.value_option,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
