import "./delete.mcss";
import "@bux/input/checkbox.mjs";
import { SE } from "@granite/lib.mts";
import { advsior_unlink } from "@crate/api/admin/agency/advisor_unlinkλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
//hidden agency_uuid
const $agency_uuid: HTMLInputElement = SE($form, "[name=agency_uuid]");
//hidden advisor_uuid
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
//confirm checkbox
const $confirm: HTMLInputElement = SE($form, "input[type=checkbox]");
//submit button
const $submitButton: HTMLButtonElement = SE($form, "button[type=submit]");

// Disable submit button initially
$submitButton.disabled = true;

// Enable/disable submit button based on checkbox state
$confirm.addEventListener("change", () => {
    $submitButton.disabled = !$confirm.checked;
});

// Add form submission validation
$form.addEventListener("submit", (event) => {
    if (!$confirm.checked) {
        event.preventDefault();
        alert("Please confirm that you understand the implications of this action.");
        return false;
    }
    return true;
});

new FormPanel({
    $form,
    api: advsior_unlink.api,
    on_cancel: go_back,

    err: (_errors) => {
        // No specific field errors to handle
    },

    get: () => {
        return {
            agency_uuid: $agency_uuid.value,
            advisor_uuid: $advisor_uuid.value,
        };
    },

    set: (_value) => {
        // No need to set values
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
