#[approck::http(GET /admin/agency/{agency_uuid:Uuid}/advisorlist/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    use granite::Decimal;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        // Use add_page_nav_button directly with icon
        doc.add_page_nav_button(
            "Add New Advisor",                               // title
            &crate::ml_agency_add_advisor(path.agency_uuid), // href
            "link",                                          // button_type
            Some("primary"),                                 // additional_classes
            Some("fas fa-plus"),                             // icon
        );
        doc.set_title("Advisor List");

        use crate::api::admin::agency::advisor_list::admin_agency_advisor_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = admin_agency_advisor_list::call(
            app,
            identity,
            admin_agency_advisor_list::Input {
                agency_uuid: path.agency_uuid,
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;
        // Calculate totals
        let total_active_clients: i64 = output
            .advisor_list
            .iter()
            .map(|a| a.advisor_client_active_count)
            .sum();
        let total_activated_clients: i64 = output
            .advisor_list
            .iter()
            .map(|a| a.advisor_client_active_count_df4l_activated)
            .sum();
        let total_debt_eliminated: Option<Decimal> = output
            .advisor_list
            .iter()
            .map(|a| a.advisor_client_debt_balance)
            .sum();

        // Create a detail table with agency advisors directly in the constructor
        let mut dt = bux::component::detail_table(output.advisor_list);
        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_link_column(
            "Name",
            |a| crate::ml_advisor(a.advisor_uuid),
            |a| format!("{} {}", a.first_name, a.last_name),
        );
        dt.add_column("Advisor ID", |a| html! { (a.advisor_esid) });
        dt.add_active_status_column("Status", |a| a.active);
        dt.add_column(
            "Active Clients",
            |a| html! { (a.advisor_client_active_count) },
        );
        dt.add_column(
            "Activated Clients",
            |a| html! { (a.advisor_client_active_count_df4l_activated) },
        );
        dt.add_column(
            "Anticipated Debt Eliminated from Activated Clients",
            |a| html! { (a.advisor_client_debt_balance.map(|b| format!("${}", b)).unwrap_or("N/A".to_string())) },
        );
        dt.action_button_column("", "Remove From Agency", "", "warning", |a| {
            format!(
                "/admin/agency/{}/advisorlist/{}/delete",
                path.agency_uuid, a.advisor_uuid
            )
        });

        dt.add_details_column(|a| crate::ml_advisor(a.advisor_uuid));

        doc.add_body(html!(
            (dt)
            grid-3.mt-3 {
                p { b { "Total Number Of Active Clients: " (total_active_clients) } }
                p { b { "Total Number Of Activated Clients: " (total_activated_clients) } }
                p { b { "Total Anticipated Debt Eliminated from Activated Clients: "
                    (total_debt_eliminated.map(|b| format!("${}", b)).unwrap_or("N/A".to_string()))
                } }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
