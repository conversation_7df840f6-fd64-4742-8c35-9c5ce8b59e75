import "./add.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/text/currency.mts";
import "@bux/input/select/nilla.mts";

import { SE, Uuid } from "@granite/lib.mts";
import { admin_client_add } from "@crate/api/admin/client0/addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputCurrency from "@bux/input/text/currency.mts";
import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: BuxInputSelectNilla<Uuid> = SE($form, "[name=advisor_uuid]");
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $email: BuxInputTextString = SE($form, "[name=email]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $phone2: BuxInputTextString = SE($form, "[name=phone2]");
const $monthly_budget: BuxInputCurrency = SE($form, "[name=monthly_budget]");
const $annual_insurance_premium: BuxInputCurrency = SE(
    $form,
    "[name=annual_insurance_premium]",
);
const $annual_insurance_pua: BuxInputCurrency = SE($form, "[name=annual_insurance_pua]");

new FormPanel({
    $form,
    api: admin_client_add.api,
    on_cancel: go_back,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $email.set_e(errors.email);
        $phone.set_e(errors.phone);
        $phone2.set_e(errors.phone2);
        $monthly_budget.set_e(errors.monthly_budget);
        $annual_insurance_premium.set_e(errors.annual_insurance_premium);
        $annual_insurance_pua.set_e(errors.annual_insurance_pua);
    },

    get: () => {
        // Return advisor_uuid as string | undefined
        const advisorUuid = $advisor_uuid.value;

        return {
            advisor_uuid: advisorUuid || undefined,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value_option,
            phone: $phone.value_option,
            phone2: $phone2.value_option,
            monthly_budget: $monthly_budget.value_option,
            annual_insurance_premium: $annual_insurance_premium.value_option,
            annual_insurance_pua: $annual_insurance_pua.value_option,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
