[package]
name = "df4l-advisor"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["df4l-zero", "df4l-crs", "df4l-icover", "approck", "auth-fence", "bux", "granite", "addr-iso"]


[dependencies]
approck = {workspace = true}
approck-postgres = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
num-traits = { workspace = true }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }

df4l-zero = { path = "../df4l-zero" }
df4l-crs = { path = "../df4l-crs" }
df4l-icover = { path = "../df4l-icover" }
addr-iso = { workspace = true }