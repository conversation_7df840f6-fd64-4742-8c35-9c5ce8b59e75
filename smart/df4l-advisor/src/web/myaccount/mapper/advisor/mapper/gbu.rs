#[approck::http(GET /myaccount/{identity_uuid:Uuid}/advisor/{advisor_uuid:Uuid}/gbu; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("Edit GBU Agent ID");

        use crate::api::myaccount::advisor_detail::detail_advisor;

        let output = detail_advisor::call(
            app,
            identity,
            detail_advisor::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        let mut panel = bux::component::save_cancel_form_panel(
            "Edit Your GBU Agent ID",
            &format!("/myaccount/{}/", path.identity_uuid),
        );

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            input type="hidden" name="advisor_uuid" value=(path.advisor_uuid.to_string()) {}
            p {"Please enter your official GBU Agent ID."}
            (bux::input::text::string::name_label_value("gbu_agent_id", "GBU Agent ID: ", output.gbu_agent_id.as_deref()))
        ));

        doc.add_body(html!(
            bux-action-panel {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
