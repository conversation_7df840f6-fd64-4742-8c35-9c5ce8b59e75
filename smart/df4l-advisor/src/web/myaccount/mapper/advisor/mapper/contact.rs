#[approck::http(GET /myaccount/{identity_uuid:Uuid}/advisor/{advisor_uuid:Uuid}/contact; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("Edit Contact Information");

        use crate::api::myaccount::advisor_detail::detail_advisor;

        let output = detail_advisor::call(
            app,
            identity,
            detail_advisor::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        let panel = {
            let mut panel = bux::component::save_cancel_form_panel(
                "Edit Contact Information",
                &format!("/myaccount/{}/", path.identity_uuid),
            );
            panel.set_hidden("advisor_uuid", path.advisor_uuid);
            panel.add_body(html!(
                h6 { "Personal Information" }
                grid-2 {
                    (bux::input::text::string::name_label_value("first_name", "First Name:", Some(&output.first_name)))
                    (bux::input::text::string::name_label_value("last_name", "Last Name:", Some(&output.last_name)))
                }
                h6 { "Contact Information" }
                grid-2 {
                    (bux::input::text::string::name_label_value("email", "Email:", output.email.as_deref()))
                    (bux::input::text::string::name_label_value("phone", "Phone:", output.phone.as_deref()))
                }
                h6 { "Address" }
                (bux::input::text::string::name_label_value("address1", "Address Line 1:", output.address1.as_deref()))
                (bux::input::text::string::name_label_value("address2", "Address Line 2:", output.address2.as_deref()))
                grid-3 {
                    (bux::input::text::string::name_label_value("city", "City:", output.city.as_deref()))
                    (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State:", output.state.as_deref(), "").await?)
                    (bux::input::text::string::name_label_value("zip", "ZIP Code:", output.zip.as_deref()))
                }
                (bux::input::text::string::name_label_value("country", "Country:", output.country.as_deref()))
            ));
            panel
        };

        doc.add_body(html!(
            grid-12 {
                cell-2 {}
                cell-8 { (panel) }
                cell-2 {}
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
