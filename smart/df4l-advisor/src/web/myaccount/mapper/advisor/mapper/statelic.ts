import "./statelic.mcss";
import "@bux/input/checkbox.mts";

import FormPanel from "@bux/component/form_panel.mts";
import BuxInputCheckbox from "@bux/input/checkbox.mts";
import { SE } from "@granite/lib.mts";

import { go_back } from "@bux/singleton/nav_stack.mts";
import { advisor_onboarding_checklist_statelic_set } from "@crate/api/myaccount/statelicλ.mjs";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
const advisor_uuid = $advisor_uuid.value;

const $checkboxes: NodeListOf<BuxInputCheckbox> = $form.querySelectorAll(
    'bux-input-checkbox[name="state_code"]',
);

function get_selected_state_codes(): string[] {
    return Array.from($checkboxes)
        .filter((checkbox) => {
            const result = checkbox.get();
            if ("Ok" in result) {
                return result.Ok;
            } else {
                return false;
            }
        })
        .map((checkbox) => checkbox.get_value() || "");
}

new FormPanel({
    $form,
    api: advisor_onboarding_checklist_statelic_set.api,
    on_cancel: go_back,

    err: (_errors) => {
        // string error will suffice
    },

    get: () => {
        return {
            advisor_uuid: advisor_uuid,
            state_codes: get_selected_state_codes(),
        };
    },

    set: (_value) => {
        // No prefill required for this screen
    },

    out: (_output) => {
        go_back();
    },
});
