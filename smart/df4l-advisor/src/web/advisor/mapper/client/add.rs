#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, path: Path) -> Response {
        use maud::html;

        doc.set_title("Add Client");

        let mut form_panel = bux::component::add_cancel_form_panel(
            "Add New Client",
            &crate::ml_advisor_client_list(path.advisor_uuid),
        );

        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-2 {
                (bux::input::text::string::name_label_value("first_name", "First Name:", None))
                (bux::input::text::string::name_label_value("last_name", "Last Name:", None))
            }
            grid-2 {
                (bux::input::text::string::name_label_value("email", "Personal Email:", None))
                (bux::input::text::string::name_label_value("phone", "Mobile Phone:", None))
            }
        ));
        doc.add_body(html! {
            grid-12 {
                cell-3 {}
                cell-6 { (form_panel) }
                cell-3 {}
            }
        });
        Response::HTML(doc.into())
    }
}
