#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]

pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.page_nav_add_record(
            "Add New Client",
            &crate::ml_advisor_client_add(path.advisor_uuid),
        );
        doc.set_title("Clients");

        use crate::api::client::list::client_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = client_list::call(
            app,
            identity,
            client_list::Input {
                advisor_uuid: path.advisor_uuid,
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        let mut dt = bux::component::detail_table(output.client_list);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_link_column(
            "Client Name",
            |a| crate::ml_advisor_client_details(a.advisor_uuid, a.client_uuid),
            |a| a.name.clone(),
        );
        dt.add_column(
            "Added On",
            |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_active_status_column("Client Status", |a| a.active);
        dt.add_details_column(|a| crate::ml_advisor_client_details(a.advisor_uuid, a.client_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}
