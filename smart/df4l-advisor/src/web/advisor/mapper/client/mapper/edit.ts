//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./edit.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";
import "@addr-iso/input/address_us_select.mts";
import "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SE } from "@granite/lib.mts";
import { client_edit_save } from "@crate/api/client/editλ.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import { AddressUsSelect } from "@addr-iso/input/address_us_select.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SE(document, "form.bux-form-wizard") as HTMLFormElement;
const $client_uuid: HTMLInputElement = SE($form, "[name=client_uuid]");
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $email: BuxInputTextString = SE($form, "[name=email]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $address1: BuxInputTextString = SE($form, "[name=address1]");
const $address2: BuxInputTextString = SE($form, "[name=address2]");
const $city: BuxInputTextString = SE($form, "[name=city]");
const $state: AddressUsSelect = SE($form, "[name=state]");
const $zip: BuxInputTextString = SE($form, "[name=zip]");
const $note: BuxInputTextareaString = SE($form, "[name=note]");

const client_uuid = $client_uuid.value;

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

$first_name.on_change = auto_save;
$last_name.on_change = auto_save;
$email.on_change = auto_save;
$phone.on_change = auto_save;
$address1.on_change = auto_save;
$address2.on_change = auto_save;
$city.on_change = auto_save;
$state.on_change = auto_save;
$zip.on_change = auto_save;
$note.on_change = auto_save;

// -------------------------------------------------------------------------------------------------
// 5. Write Code
// -------------------------------------------------------------------------------------------------

// Auto-save function
async function auto_save() {
    try {
        const response = await client_edit_save.api.call({
            client_uuid: client_uuid,
            first_name: $first_name.value || "",
            last_name: $last_name.value || "",
            email: $email.value_option,
            phone: $phone.value_option,
            phone2: { None: true as true },
            address1: $address1.value_option,
            address2: $address2.value_option,
            city: $city.value_option,
            state: $state.value_option,
            zip: $zip.value_option,
            country: { None: true as true },
            note: $note.value_option,
        });

        if ("ValidationError" in response) {
            const errors = response.ValidationError[0];

            if ("Inner" in errors) {
                const inner_errors = errors.Inner as any;
                $first_name.set_e(inner_errors.first_name);
                $last_name.set_e(inner_errors.last_name);
                $email.set_e(inner_errors.email);
                $phone.set_e(inner_errors.phone);
                $address1.set_e(inner_errors.address1);
                $address2.set_e(inner_errors.address2);
                $city.set_e(inner_errors.city);
                $state.set_e(inner_errors.state);
                $zip.set_e(inner_errors.zip);
                $note.set_e(inner_errors.note);
            }
            if ("Outer" in errors) {
                alert(errors.Outer);
            }
        }
    } catch (error) {
        console.error("Auto-save failed:", error);
    }
}
