#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;
        use bux::format_currency_us_0;

        use crate::api::client::dashboard::client_dashboard;
        use crate::api::client::dashboard::client_dashboard::{
            date_formatted_yy_mon, format_decimal_optional,
        };

        let client_debt_info = client_dashboard::call(
            app,
            identity,
            client_dashboard::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record(
            "Edit Client",
            &crate::ml_advisor_client_edit(path.advisor_uuid, path.client_uuid),
        );
        doc.set_title("Debt Free Program Details");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            grid-12 {
                cell-3 {
                    panel {
                        content {
                            d2c-client-info {
                                h1 { (client_debt_info.client.name) }
                                hr;
                                p.status {
                                    i.fas.fa-bars {}
                                    label-tag.warning { "Has Data But Not Enrolled" }
                                }
                                @if let Some(phone) = &client_debt_info.client.phone {
                                    p.phone {
                                        i.fas.fa-phone-alt {}
                                        (phone)
                                    }
                                }
                                @if let Some(phone2) = &client_debt_info.client.phone2 {
                                    p.phone {
                                        i.fas.fa-phone-alt {}
                                        (phone2)
                                    }
                                }
                                @if let Some(email) = &client_debt_info.client.email {
                                    p.email {
                                        i.far.fa-envelope {}
                                        a href=(format!("mailto:{}", email)) { (email) }
                                    }
                                }
                            }
                            hr;
                            p {
                                small { b{"Minimum Monthly Debt Payments:"} }
                                br;
                                span { (format_currency_us_0(client_debt_info.debt_info.minimum_monthly_payment.unwrap_or_default())) "/mo" }
                            }
                            hr;
                            p {
                                small { b{"Combined Premium And Debt Snowball Budget:"} }
                                br;
                                span { (format_decimal_optional(client_debt_info.debt_info.annual_budget, "-"))  }
                            }
                            hr;
                            p {
                                small { b{"Total Insurance Premium:"} }
                                br;
                                span { (format_decimal_optional(client_debt_info.client.annual_insurance_premium,  "-")) }
                            }
                            hr;
                            p {
                                small { b{"Insurance Premium Base:"} }
                                br;
                                span { (format_decimal_optional(client_debt_info.client.annual_insurance_base,  "-")) }
                            }
                            hr;
                            p style="margin-bottom: 0;" {
                                small { b {"Insurance Premium PUA:"} }
                                br;
                                span { (format_decimal_optional(client_debt_info.client.annual_insurance_pua,  "-")) }
                            }
                        }
                    }

                }
                cell-6 {
                    d2c-program-overview {
                        grid-3 {
                            panel {
                                content {
                                    div {
                                        h5 { "Paid Debts" }
                                        p { (format_currency_us_0(client_debt_info.debt_info.total_paid)) }
                                    }
                                    i.fas.fa-arrow-up style="color: #198754;" {}
                                }
                            }
                            panel {
                                content {
                                    div {
                                        h5 { "Unpaid Debts" }
                                        p { (format_currency_us_0(client_debt_info.debt_info.total_unpaid)) }
                                    }
                                    i.fas.fa-arrow-down style="color: #ffc107;" {}
                                }
                            }
                            panel {
                                content {
                                    div {
                                        h5 { "Est. Cash Value" }
                                        p { (format_currency_us_0(client_debt_info.debt_info.esimated_cash_value))}
                                    }
                                    i.fas.fa-dollar-sign style="color: #0d6efd;" {}
                                }
                            }
                        }
                    }
                    d2c-debt-list {
                        table.striped {
                            thead {
                                tr {
                                    th { "Debt" }
                                    th { "Balance" }
                                    th { "Mo. Payment"}
                                    th { "Interest" }
                                    th { "EIR" }
                                }
                            }
                            tbody {
                                @for debt in &client_debt_info.debt_info.debt_list {
                                    @let class_name = if debt.is_paid_off_as_of_today { "x-paid" } else { "" };
                                    tr class=(class_name) {
                                        td { (debt.full_name) }
                                        td { (format_currency_us_0(debt.balance)) }
                                        td { (format_currency_us_0(debt.monthly_payment)) }
                                        td { (debt.interest_rate) "%" }
                                        td { (debt.effective_interest_cost_percent) "%" }
                                    }
                                }
                            }
                        }
                    }
                }
                cell-3 {
                    d2c-program-timeline {
                        panel.x-complete {
                            content {
                                div.x-date {
                                    p { (date_formatted_yy_mon(client_debt_info.debt_info.start_year, client_debt_info.debt_info.start_month))  }
                                }
                                div.x-icon {
                                    i.fas.fa-circle {}
                                }
                                div.x-content {
                                    h5 {
                                        "Program Started"
                                        br;
                                        small { "Debt Balance" }
                                    }
                                    label-tag.danger { (format_currency_us_0(client_debt_info.debt_info.debt_balance.unwrap_or_default())) }
                                }
                            }
                        }

                        @for debt in &client_debt_info.debt_info.debt_list {
                            @let panel_class = if debt.is_paid_off_as_of_today { "x-complete" } else { "" };
                            @let icon_class = if debt.is_paid_off_as_of_today { "fas fa-circle" } else { "fas fa-spinner" };
                            panel class=(panel_class) {
                                content {
                                    div.x-date {
                                        p {(debt.payoff_date_formatted.clone().unwrap_or_default())}
                                    }
                                    div.x-icon {
                                        i class=(icon_class) {}
                                    }
                                    div.x-content {
                                        h5 {
                                            (debt.full_name)
                                            @if debt.is_paid_off_as_of_today {
                                                br;
                                                small { "Paid Off" }
                                            }
                                        }
                                        p { (format_currency_us_0(debt.balance)) }
                                    }
                                }
                            }
                        }

                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
