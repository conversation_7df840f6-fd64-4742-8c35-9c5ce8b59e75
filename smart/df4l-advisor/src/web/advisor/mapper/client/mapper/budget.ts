import "./budget.mcss";
import "@bux/input/radio/boolean.mts";
import "@bux/input/text/currency.mts";
import "@bux/component/form_wizard.mts";

import FormWizard from "@bux/component/form_wizard.mts";
import BuxInputRadioBoolean from "@bux/input/radio/boolean.mts";
import BuxInputCurrency from "@bux/input/text/currency.mts";
import { SE, unwrap_or } from "@granite/lib.mts";
import { Decimal } from "@granite/lib.mts";

import { go_back } from "@bux/singleton/nav_stack.mts";
import { advisor_client_budget_set } from "@crate/api/client/budgetλ.mjs";

const $form = SE(document, "form.bux-form-wizard") as HTMLFormElement;
const $client_uuid: HTMLInputElement = SE($form, "[name=client_uuid]");
const client_uuid = $client_uuid.value;

// Get all the input elements
const $extra_debt_payments_enabled: BuxInputRadioBoolean = SE(
    $form,
    "[name=extra_debt_payments_enabled]",
);
const $extra_debt_payments_value: BuxInputCurrency = SE(
    $form,
    "[name=extra_debt_payments_value]",
);
const $retirement_contributions_enabled: BuxInputRadioBoolean = SE(
    $form,
    "[name=retirement_contributions_enabled]",
);
const $retirement_contributions_value: BuxInputCurrency = SE(
    $form,
    "[name=retirement_contributions_value]",
);
const $regular_savings_enabled: BuxInputRadioBoolean = SE($form, "[name=regular_savings_enabled]");
const $regular_savings_value: BuxInputCurrency = SE($form, "[name=regular_savings_value]");
const $mortgage_program_enabled: BuxInputRadioBoolean = SE(
    $form,
    "[name=mortgage_program_enabled]",
);
const $mortgage_program_value: BuxInputCurrency = SE($form, "[name=mortgage_program_value]");
const $total_amount = SE(document, ".x-total span") as HTMLSpanElement;

// Centralized refresh function - single source of truth for page state
function refresh() {
    // Get current values from all Yes/No radio buttons
    const extra_debt_payments_enabled = unwrap_or($extra_debt_payments_enabled.get(), false);
    const retirement_contributions_enabled = unwrap_or(
        $retirement_contributions_enabled.get(),
        false,
    );
    const regular_savings_enabled = unwrap_or($regular_savings_enabled.get(), false);
    const mortgage_program_enabled = unwrap_or($mortgage_program_enabled.get(), false);

    // Show/hide currency input fields based on Yes/No selections
    $extra_debt_payments_value.style.display = extra_debt_payments_enabled ? "block" : "none";
    $retirement_contributions_value.style.display = retirement_contributions_enabled
        ? "block"
        : "none";
    $regular_savings_value.style.display = regular_savings_enabled ? "block" : "none";
    $mortgage_program_value.style.display = mortgage_program_enabled ? "block" : "none";

    // Calculate total from all visible and filled currency inputs
    let total = new Decimal(0n, 0);

    if (extra_debt_payments_enabled) {
        const amount_value = unwrap_or($extra_debt_payments_value.get(), undefined);
        if (amount_value !== undefined) {
            total = total.add(amount_value);
        }
    }

    if (retirement_contributions_enabled) {
        const amount_value = unwrap_or($retirement_contributions_value.get(), undefined);
        if (amount_value !== undefined) {
            total = total.add(amount_value);
        }
    }

    if (regular_savings_enabled) {
        const amount_value = unwrap_or($regular_savings_value.get(), undefined);
        if (amount_value !== undefined) {
            total = total.add(amount_value);
        }
    }

    if (mortgage_program_enabled) {
        const amount_value = unwrap_or($mortgage_program_value.get(), undefined);
        if (amount_value !== undefined) {
            total = total.add(amount_value);
        }
    }

    // Update the total display
    $total_amount.textContent = "$" + total.toNumber().toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
}

// Change handlers will be set up below with enhanced_refresh

// Handle selection state visual feedback
function update_selection_states() {
    // Update radio button selection states
    const radio_components = [
        $extra_debt_payments_enabled,
        $retirement_contributions_enabled,
        $regular_savings_enabled,
        $mortgage_program_enabled,
    ];

    radio_components.forEach((component) => {
        const result = component.get();
        if ("Ok" in result && result.Ok) {
            component.classList.add("x-selected");
        } else {
            component.classList.remove("x-selected");
        }
    });

    // Update currency input selection states
    const currency_components = [
        { component: $extra_debt_payments_value, enabled: $extra_debt_payments_enabled },
        { component: $retirement_contributions_value, enabled: $retirement_contributions_enabled },
        { component: $regular_savings_value, enabled: $regular_savings_enabled },
        { component: $mortgage_program_value, enabled: $mortgage_program_enabled },
    ];

    currency_components.forEach(({ component, enabled }) => {
        const enabled_result = enabled.get();
        const value_result = component.get();

        if (
            "Ok" in enabled_result && enabled_result.Ok && "Ok" in value_result && value_result.Ok
        ) {
            component.classList.add("x-selected");
        } else {
            component.classList.remove("x-selected");
        }
    });
}

// Enhanced refresh function that includes selection state updates
function enhanced_refresh() {
    refresh();
    update_selection_states();
}

// Update all change handlers to use enhanced refresh
$extra_debt_payments_enabled.on_change = enhanced_refresh;
$retirement_contributions_enabled.on_change = enhanced_refresh;
$regular_savings_enabled.on_change = enhanced_refresh;
$mortgage_program_enabled.on_change = enhanced_refresh;

$extra_debt_payments_value.on_change = enhanced_refresh;
$retirement_contributions_value.on_change = enhanced_refresh;
$regular_savings_value.on_change = enhanced_refresh;
$mortgage_program_value.on_change = enhanced_refresh;

// Initialize page state on load
enhanced_refresh();

new FormWizard({
    $form,
    api: advisor_client_budget_set.api,
    on_cancel: go_back,

    err: (_errors) => {
        // string error will suffice
    },

    get: () => {
        const extra_debt_payments_enabled = unwrap_or($extra_debt_payments_enabled.get(), false);
        const extra_debt_payments_value = unwrap_or($extra_debt_payments_value.get(), undefined);

        const retirement_contributions_enabled = unwrap_or(
            $retirement_contributions_enabled.get(),
            false,
        );
        const retirement_contributions_value = unwrap_or(
            $retirement_contributions_value.get(),
            undefined,
        );

        const regular_savings_enabled = unwrap_or($regular_savings_enabled.get(), false);
        const regular_savings_value = unwrap_or($regular_savings_value.get(), undefined);

        const mortgage_program_enabled = unwrap_or($mortgage_program_enabled.get(), false);
        const mortgage_program_value = unwrap_or($mortgage_program_value.get(), undefined);

        return {
            client_uuid: client_uuid,
            extra_debt_payments_enabled: extra_debt_payments_enabled,
            extra_debt_payments_value: extra_debt_payments_value
                ? { Some: extra_debt_payments_value }
                : undefined,
            retirement_contributions_enabled: retirement_contributions_enabled,
            retirement_contributions_value: retirement_contributions_value
                ? { Some: retirement_contributions_value }
                : undefined,
            regular_savings_enabled: regular_savings_enabled,
            regular_savings_value: regular_savings_value
                ? { Some: regular_savings_value }
                : undefined,
            mortgage_program_enabled: mortgage_program_enabled,
            mortgage_program_value: mortgage_program_value
                ? { Some: mortgage_program_value }
                : undefined,
        };
    },

    set: (_value) => {
        // No prefill required for this screen - data comes from API call
    },

    out: (_output) => {
    },
});
