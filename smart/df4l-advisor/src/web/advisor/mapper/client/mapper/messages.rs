#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/messages; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        use crate::api::client::messages::client_messages;

        let messages_data = client_messages::call(
            app,
            identity,
            client_messages::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record(
            "Edit Client",
            &crate::ml_advisor_client_edit(path.advisor_uuid, path.client_uuid),
        );
        doc.set_title("Client Messages");

        doc.add_body(html!(
            section {
                h2 { "Text Message History" }

                @if messages_data.messages.is_empty() {
                    panel {
                        content {
                            p { "No messages found for this client." }
                        }
                    }
                } @else {
                    d2c-message-list {
                        @for message in &messages_data.messages {
                            panel.x-outbound {
                                content {
                                    div.x-message-header {
                                        span.x-contact {
                                            "To: " (message.to)
                                        }
                                        span.x-timestamp {
                                            (message.sent_ts.format("%m/%d/%Y %I:%M %p").to_string())
                                        }
                                    }
                                    div.x-message-content {
                                        p { (message.message) }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
