//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./debt.mcss";
import "@bux/component/form_wizard.mts";
import "@bux/input/text/currency.mts";
import "@bux/input/text/percentage.mts";
import "@bux/input/text/string.mts";
import "@bux/input/checkbox.mts";
import "@bux/input/date.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code
import { SE } from "@granite/lib.mts";

import { lapi_add, lapi_tbody } from "./debtλ.mts";
import {
    client_debt_delete,
    client_debt_save,
    client_debt_save_status,
} from "@crate/api/client/debtλ.mts";
import BuxInputCheckbox from "@bux/input/checkbox.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputCurrency from "@bux/input/text/currency.mts";
import BuxInputPercentage from "@bux/input/text/percentage.mts";
import BuxInputDate from "@bux/input/date.mjs";

//-------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = SE(document, "#debt-editor") as HTMLFormElement;
const $table = SE($form, "table") as HTMLTableElement;
const $tbody = SE($table, "tbody") as HTMLTableSectionElement;
const client_uuid = (SE($form, "[name=client_uuid]") as HTMLInputElement).value;

const $x_add_link = SE($form, ".x-add-debt") as HTMLAnchorElement;

//-------------------------------------------------------------------------------------------------
// 4. Bind event handlers
$x_add_link.addEventListener("click", (event) => {
    event.preventDefault();
    add_debt();
});

// iterate over all the tr tags in tbody and wrap them
for (const tr of Array.from($tbody.querySelectorAll<HTMLTableRowElement>("tr"))) {
    wrap_tr(tr);
}

//-------------------------------------------------------------------------------------------------
// 5. All the other funcitonality

function refresh() {
    lapi_tbody.api
        .call({
            client_uuid,
        })
        .then((response) => {
            if ("Output" in response) {
                $tbody.innerHTML = response.Output[0].tbody_inner_html;

                for (const tr of Array.from($tbody.querySelectorAll<HTMLTableRowElement>("tr"))) {
                    wrap_tr(tr);
                }
            } else {
                console.error(response);
            }
        })
        .catch((error) => {
            console.error(error);
        });
}

function wrap_tr(tr: string | HTMLTableRowElement) {
    let $tr: HTMLTableRowElement;
    if (typeof tr === "string") {
        const $tbody = document.createElement("tbody");
        $tbody.innerHTML = tr;
        $tr = $tbody.querySelector("tr") as HTMLTableRowElement;
    } else {
        $tr = tr;
    }

    const client_debt_uuid = $tr.getAttribute("client_debt_uuid") || "";
    const editable = $tr.hasAttribute("editable");
    const $active = SE($tr, "bux-input-checkbox[name=active]") as BuxInputCheckbox;

    $active.on_change = save_status;

    function save_status() {
        client_debt_save_status
            .call({
                client_uuid,
                client_debt_uuid,
                active: $active.value ?? false,
            })
            .then((response) => {
                if ("Output" in response) {
                    // Status saved successfully
                } else {
                    console.error(response);
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    if (editable) {
        const $name: BuxInputTextString = SE($tr, "bux-input-text-string[name=name]");
        const $balance: BuxInputCurrency = SE($tr, "bux-input-text-currency[name=balance]");
        const $balance_date: BuxInputDate = SE($tr, "bux-input-date[name=balance_date]");
        const $interest_rate: BuxInputPercentage = SE(
            $tr,
            "bux-input-text-percentage[name=interest_rate]",
        );
        const $payment: BuxInputCurrency = SE($tr, "bux-input-text-currency[name=payment]");
        const $delete_button = SE($tr, "button.x-delete") as HTMLButtonElement;

        const save = () => {
            client_debt_save
                .call({
                    client_uuid,
                    client_debt_uuid,
                    name: $name.value_option,
                    balance: $balance.value_option,
                    balance_date: $balance_date.value_option,
                    interest: $interest_rate.value_option,
                    payment: $payment.value_option,
                })
                .then((response) => {
                    if ("Output" in response) {
                        // Saved successfully
                    } else {
                        console.error(response);
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        };

        const delete_debt = () => {
            client_debt_delete
                .call({
                    client_uuid,
                    client_debt_uuid,
                })
                .then((response) => {
                    if ("Output" in response) {
                        // Successfully deleted, refresh the table
                        refresh();
                    } else {
                        console.error(response);
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        };

        $name.on_change = save;
        $balance.on_change = save;
        $balance_date.on_change = save;
        $interest_rate.on_change = save;
        $payment.on_change = save;
        $delete_button.addEventListener("click", (event) => {
            event.preventDefault();
            delete_debt();
        });
    }

    return $tr;
}

/// call the new_row api to get the new row html, then inject it into the tbody
function add_debt() {
    lapi_add
        .call({
            client_uuid,
        })
        .then((response) => {
            if ("Output" in response) {
                const $tr = wrap_tr(response.Output[0].tr_html);
                $tbody.appendChild($tr);
                $tr.querySelector<HTMLInputElement>("input[name=name]")?.focus();
            } else {
                console.error(response);
            }
        })
        .catch((error) => {
            console.error(error);
        });
}
