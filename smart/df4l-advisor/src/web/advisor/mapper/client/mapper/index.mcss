
d2c-client-info {
    h1 {
        text-align: center;
        background-color: #cfe2ff;
        padding: 1rem;
        border-radius: .25rem;
        border: 1px solid #b6d4fe;
        color: #084298;
        font-weight: 500;
        font-size: 18pt;
    }

    p {
        &.phone, &.email, &.status {
            display: flex;
            gap: 1rem;
            align-items: center;

            i {
                width: 1.5rem;
                text-align: center;
            }
        }
    }
}

d2c-program-overview {
    panel content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;

        h5 {
            font-size: 11pt;
            margin-bottom: 0;
            color: dimgray;
            font-weight: normal;
        }

        p {
            margin-bottom: 0;
            font-size: 18pt;
            font-weight: 500;
        }

        i {
            font-size: 20pt;
        }
    }
}

d2c-debt-list {
    table {
        th, td {
            text-align: right;

            &:first-child {
                text-align: left;
            }
        }

        tr.x-paid td {
            text-decoration: line-through;
        }

        &.striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, .05);
        }
    }
}

d2c-program-timeline {
    panel {
        content {
            padding: 0;
            display: flex;

            .x-date {
                padding: 1rem;
                width: 25%;
                text-align: right;

                p {
                    margin-bottom: 0;
                    font-size: .875rem;
                    font-weight: 500;
                }
            }

            .x-icon {
                position: relative;
                padding: 1rem 0;

                i {
                    color: #6c757d;
                    position: relative;
                    z-index: 100;
                    background-color: #fff;
                    display: block;
                    padding: 5px 0;
                }

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 50%;
                    width: 1px;
                    background-color: #6c757d;
                    transform: translateX(-50%);
                }
            }

            .x-content {
                padding: 1rem;

                h5 {
                    font-size: 1rem;
                }

                p {
                    margin-bottom: 0;
                    font-size: 1rem;
                    font-weight: 500;
                }
            }
        }

        &.x-complete {
            border-color: #198754;
            background-color: #dff0d8;

            .x-icon {
                i {
                    color: #198754;
                    background-color: #dff0d8;
                }

                &::after {
                    background-color: #198754;
                }
            }

            .x-content p {
                text-decoration: line-through;
            }
        }

        &:first-child .x-icon::after {
            top: 1rem;
        }

        &:last-child .x-icon::after {
            bottom: 1rem;
        }
    }
}