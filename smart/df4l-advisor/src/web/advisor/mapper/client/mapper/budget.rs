#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/budget; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        doc.set_title("Budget Information");

        use crate::api::client::budget::client_budget_get;
        use crate::api::client::wizard::client_wizard;

        let wizard_data = client_wizard::call(
            app,
            identity,
            client_wizard::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let output = client_budget_get::call(
            app,
            identity,
            client_budget_get::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Budget, wizard_data)?;
            wizard.set_id("onboarding");
            wizard.add_heading("Set Your Client's Budget");
            wizard.add_description(
                "Please provide information about your client's current financial situation.",
            );
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_body(html!(

                grid-2 {
                    div {

                        // Question 1: Extra debt payments
                        div.x-question {
                            div { "Are you making extra payments on any of your current debts, including your mortgage?" }
                            div {
                                (bux::input::radio::boolean::radio_yesno("extra_debt_payments_enabled", output.extra_debt_payments_enabled))
                                (bux::input::text::currency::currency_input("extra_debt_payments_value", "How much are you paying extra per month?", output.extra_debt_payments_value))
                            }
                        }

                        // Question 2: Retirement contributions
                        div.x-question {
                            div { "Are you putting money into a retirement plan that is NOT being matched?" }
                            div {
                                (bux::input::radio::boolean::radio_yesno("retirement_contributions_enabled", output.retirement_contributions_enabled))
                                (bux::input::text::currency::currency_input("retirement_contributions_value", "How much are you contributing per month?", output.retirement_contributions_value))
                            }
                        }

                        // Question 3: Regular savings
                        div.x-question {
                            div { "Are you putting money into savings on a regular basis?" }
                            div {
                                (bux::input::radio::boolean::radio_yesno("regular_savings_enabled", output.regular_savings_enabled))
                                (bux::input::text::currency::currency_input("regular_savings_value", "How much are you saving per month?", output.regular_savings_value))
                            }
                        }

                        // Question 4: Mortgage program affordability
                        div.x-question {
                            div { "If we can show you how to get your mortgage paid off faster could you afford to put extra money towards this program without it affecting your lifestyle?" }
                            div {
                                (bux::input::radio::boolean::radio_yesno("mortgage_program_enabled", output.mortgage_program_enabled))
                                (bux::input::text::currency::currency_input("mortgage_program_value", "How much could you afford per month?", output.mortgage_program_value))
                            }
                        }
                    }
                    div {

                        // Total display (calculated client-side)
                        div.x-total {
                            p { "Total: " span { "0.00" } " per Month" }
                            p { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed scelerisque, sem et tempor imperdiet, turpis lectus posuere leo, ac suscipit mauris leo a sapien. Nunc viverra diam et magna cursus ultricies." }
                            p."mb-0" { "Phasellus hendrerit est a ipsum accumsan egestas. Aenean nec mauris id orci pulvinar feugiat quis et purus. Aenean pulvinar nisi eget tortor aliquam, id scelerisque lectus scelerisque. Nunc porta blandit urna nec condimentum. Aliquam eu mi ligula." }
                        }
                    }
                }
            ));
            wizard
        };

        doc.add_body(html!(
            div.constrain-width-md {
                (wizard)
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
