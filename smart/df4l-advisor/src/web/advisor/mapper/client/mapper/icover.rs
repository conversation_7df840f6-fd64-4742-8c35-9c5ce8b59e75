#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/icover; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        doc.set_title("ICover");

        use crate::api::client::wizard::client_wizard;

        let wizard_data = client_wizard::call(
            app,
            identity,
            client_wizard::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let icover_url = app.take_me_to_icover_client_url(path.client_uuid);

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::ICover, wizard_data)?;
            wizard.set_id("icover-wizard");
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_heading("Start The Journey");
            wizard.add_description(
                "Automatically send client information to iCover to start the journey.",
            );
            wizard.add_body(html!(

                a href=(icover_url) target="_blank" rel="noopener noreferrer" {
                    i."fas fa-external-link-alt" {}
                    " Take me to iCover"
                }

            ));
            wizard
        };

        doc.add_body(html!(
            div.constrain-width-md {
                (wizard)
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
