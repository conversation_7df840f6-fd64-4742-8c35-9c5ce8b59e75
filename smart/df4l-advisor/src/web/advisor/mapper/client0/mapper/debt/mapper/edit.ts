import "./edit.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/text/currency.mjs";
import "@bux/input/select/nilla.mts";
import "@bux/input/text/percentage.mjs";
import "@bux/input/date.mjs";
import "@bux/input/textarea/string.mts";

import { SE } from "@granite/lib.mts";
import { edit } from "@crate/api/client0/debt/editλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputCurrency from "@bux/input/text/currency.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import BuxInputPercentage from "@bux/input/text/percentage.mts";
import BuxInputDate from "@bux/input/date.mjs";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $client_uuid = SE($form, "[name=client_uuid]") as HTMLInputElement;
const $advisor_uuid = SE($form, "[name=advisor_uuid]") as HTMLInputElement;
const $client_debt_uuid = SE($form, "[name=client_debt_uuid]") as HTMLInputElement;
const $name: BuxInputTextString = SE($form, "[name=name]");
const $balance: BuxInputCurrency = SE($form, "[name=balance]");
const $balance_date: BuxInputDate = SE($form, "[name=balance_date]");
const $interest_rate: BuxInputPercentage = SE($form, "[name=interest_rate]");
const $monthly_payment: BuxInputCurrency = SE($form, "[name=monthly_payment]");
const $note: BuxInputTextareaString = SE($form, "[name=note]");

new FormPanel({
    $form,
    api: edit.api,
    on_cancel: go_back,

    err: (errors) => {
        $name.set_e(errors.name);
        $balance.set_e(errors.balance);
        $balance_date.set_e(errors.balance_date);
        $interest_rate.set_e(errors.interest_rate);
        $monthly_payment.set_e(errors.monthly_payment);
        $note.set_e(errors.note);
    },

    get: () => {
        return {
            client_debt_uuid: $client_debt_uuid.value,
            client_uuid: $client_uuid.value,
            advisor_uuid: $advisor_uuid.value,
            name: $name.value,
            balance: $balance.value_option,
            balance_date: $balance_date.value
                ? $balance_date.value.toISOString().substring(0, 10)
                : undefined,
            interest_rate: $interest_rate.value_option,
            monthly_payment: $monthly_payment.value_option,
            note: $note.value_option,
        };
    },

    set: (_value) => {
        // console.log(value);
    },
    out: (output) => {
        go_next(output.detail_url);
    },
});
