#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/debt/{client_debt_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;
        use bux::format_currency_us_0;

        use crate::api::client0::debt::detail::detail;

        let debt = detail::call(
            app,
            identity,
            detail::Input {
                client_uuid: path.client_uuid,
                client_debt_uuid: path.client_debt_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record(
            "Edit Debt Account",
            &crate::ml_advisor_client0_debt_edit(
                path.advisor_uuid,
                path.client_uuid,
                path.client_debt_uuid,
            ),
        );
        doc.page_nav_delete_record(
            "Delete Debt Account",
            "/advisor/********-0000-0000-0000-************/client0/********-0000-0000-0000-************/debt/********-0000-0000-0000-************/delete"
        );
        doc.set_title("List of Debts");

        let mut table = bux::component::info_table(debt);

        table.set_heading("Debt Information");
        table.add_row("Debt:", |a| html! { (a.name) });
        table.add_row(
            "Interest Rate:",
            |a| html! { (a.interest_rate.unwrap_or_default()) },
        );
        table.add_row(
            "Monthly Payment:",
            |a| html! { (format_currency_us_0(a.monthly_payment.unwrap_or_default())) },
        );
        table.add_row(
            "Current Balance:",
            |a| html! { (format_currency_us_0(a.balance.unwrap_or_default())) },
        );

        table.add_active_status_row("Advisor Status:", |a| a.active);
        table.add_row(
            "Balance Date",
            |a| html! { (bux::format_date_us_option(a.balance_date)) },
        );
        table.add_row("Note:", |a| html! { (a.note.as_deref().unwrap_or("")) });

        doc.add_body(html!((table)));

        Ok(Response::HTML(doc.into()))
    }
}
