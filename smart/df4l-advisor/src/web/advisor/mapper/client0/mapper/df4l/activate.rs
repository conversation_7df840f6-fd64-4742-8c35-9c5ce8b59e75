#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/df4l/activate; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::client0::detail::detail;
        let client = detail::call(
            app,
            identity,
            detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title(&format!("Activate Debt Free Program for {}", client.name));

        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Activate Debt Free Program for {}", client.name),
            &crate::ml_advisor_client0_details(path.advisor_uuid, path.client_uuid),
        );

        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);
        form_panel.set_hidden("client_uuid", path.client_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::text::string::name_label_readonly("name", "Name:", &client.name))
            (bux::input::text::string::name_label_value_help("phone", "Send Text Messages To Phone Number:", client.phone.as_deref(), "Do not include country code."))
            (bux::input::text::string::name_label_value_help("phone2", "Send Text Messages To Additional Phone Number:", client.phone2.as_deref(), "Do not include country code."))
            (bux::input::date::bux_input_date("debt_free_start_date", "Policy Effective Date", client.debt_free_start_date))
            div style="background-color: #fff3cd;" {
                p { b { "A text message will be sent to the number above welcoming them to the Debt Free program." } }
            }
        ));
        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
