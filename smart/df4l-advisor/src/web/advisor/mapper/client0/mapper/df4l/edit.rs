#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/df4l/edit; AUTH None; return HTML;)]
pub mod page {

    pub async fn request(
        _app: App,
        _identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;
        use std::collections::HashMap;

        // List of 1-360 month in format [("1", "Month 1"), ...]
        let month_list = (1..=360)
            .map(|m| (m.to_string(), format!("Month {}", m)))
            .collect::<Vec<_>>();
        let month_list_select = month_list
            .iter()
            .map(|(k, v)| (k.as_str(), v.as_str()))
            .collect::<Vec<_>>();

        use crate::api::client0::df4l::edit_get::edit_get;
        let df4l_detail = edit_get::call(
            _app,
            _identity,
            edit_get::Input {
                advisor_uuid: path.advisor_uuid,
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let mut highlights = HashMap::new();
        highlights.insert("MONTHLY", "skyblue");
        highlights.insert("ANNUAL", "wheat");

        doc.set_title(&format!("Edit Program - {}", df4l_detail.client.name));

        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Edit Program for {}", df4l_detail.client.name),
            &crate::ml_advisor_client0_df4l_edit(path.advisor_uuid, path.client_uuid),
        );

        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);
        form_panel.set_hidden("client_uuid", path.client_uuid);

        #[rustfmt::skip]
            form_panel.add_body(maud::html!(
                (bux::input::text::string::name_label_readonly("advisor_name", "Advisor Name:", &df4l_detail.advisor.name))
                grid-2 {
                    (bux::input::text::string::name_label_value("first_name", "Client First Name:", Some(&df4l_detail.client.first_name)))
                    (bux::input::text::string::name_label_value("last_name", "Client Last Name:", Some(&df4l_detail.client.last_name)))
                    (bux::input::text::string::name_label_value("email", "Email:", Some( &df4l_detail.client.email.unwrap_or_default())))
                    (bux::input::text::string::name_label_value_help("phone", "Send Text Messages To Phone Number:", 
                        Some(&df4l_detail.client.phone.unwrap_or_default()),  "Do not include country code."))
                    (bux::input::text::string::name_label_value_help("phone2", "Send Text Messages To Additional Phone Number:", 
                        Some(&df4l_detail.client.phone2.unwrap_or_default()), "Do not include country code."))
                }
                grid-3 {
                    (bux::input::text::currency::currency_input_highlighted_help(
                        "monthly_budget",
                        "Monthly Premium And Debt Snowball Budget:",
                        df4l_detail.client.monthly_budget,
                        "The total combined MONTHLY budget for both Insurance Premium and Debt Snowball. The more the better, but keeping it realistic is best.",
                        highlights.clone(),
                    ))
                
                    (bux::input::text::currency::currency_input_highlighted_help(
                        "annual_insurance_premium",
                        "Annual Insurance Premium:",
                        df4l_detail.client.annual_insurance_premium,
                        "The total ANNUAL premium of the Life Insurance Policy (includes PUA and Base).",
                        highlights.clone(),
                    ))
                
                    (bux::input::text::currency::currency_input_highlighted_help(
                        "annual_insurance_pua",
                        "Annual Insurance PUA:",
                        df4l_detail.client.annual_insurance_pua,
                        "The amount of the ANNUAL total premium which is Paid Up Additions (PUA).",
                        highlights.clone(),
                    ))
                }
                            
                h5 { "Extra PUA Additions:" }
                p { "You may specify extra PUA additions here which will \"front load\" the debt payoff plan." }
                grid-2 {
                    @for i in 0..5 {
                        // set value using debt_free_extra_pua_list
                        @let exm = df4l_detail.client.debt_free_extra_pua_list.get(i).map(|e| e.month.to_string());
                        @let exa = df4l_detail.client.debt_free_extra_pua_list.get(i).map(|e| e.amount);
                        (bux::input::select::nilla::nilla_select(&format!("exm_{}", i), "", &month_list_select, Some(exm.as_deref().unwrap_or_default()) ))
                        (bux::input::text::currency::currency_input(&format!("exa_{}", i), "", exa ))
                    }
                }
            ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
