#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/note/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::client0::detail::detail;

        let client_name = detail::call(
            app,
            identity,
            detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?
        .name;

        doc.set_title(&format!("Add Note for {}", client_name));

        let mut form_panel = bux::component::add_cancel_form_panel(
            &format!("Add New Note for {}", client_name),
            &crate::ml_advisor_client0_note_list(path.advisor_uuid, path.client_uuid),
        );
        form_panel.set_hidden("client_uuid", path.client_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::textarea::string::name_label_value("note", "Note:", None))
            /*
            h6 { "Attach files: " }
            panel {
                content style="text-align: center;" {
                    p { "Drop files here to upload" }
                    (bux::button::link::label_icon_class("Browse File", "", "#", "primary"))
                }
            }
            */  
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}
