#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/debt/{client_debt_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::client0::debt::detail::detail;
        use approck::html;

        let client = detail::call(
            app,
            identity,
            detail::Input {
                client_uuid: path.client_uuid,
                client_debt_uuid: path.client_debt_uuid,
            },
        )
        .await?;

        doc.set_title("Add New Debt Account");

        let mut form_panel = bux::component::save_cancel_form_panel(
            "Edit Debt Account",
            &crate::ml_advisor_client0_debt_details(
                path.advisor_uuid,
                path.client_uuid,
                path.client_debt_uuid,
            ),
        );

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            input type="hidden" name="client_uuid" value=(path.client_uuid.to_string()) {}
            input type="hidden" name="advisor_uuid" value=(path.advisor_uuid.to_string()) {}
            input type="hidden" name="client_debt_uuid" value=(path.client_debt_uuid.to_string()) {}

            grid-3 {
                (bux::input::text::string::name_label_value("name", "Debt Type:", Some(&client.name)))
                (bux::input::date::bux_input_date("balance_date", "Balance Date:", client.balance_date))
                (bux::input::textarea::string::name_label_value("note", "Note:", client.note.as_deref()))
            }
            grid-3 {
                (bux::input::text::currency::currency_input("balance", "Current Balance: ", Some(client.balance.unwrap_or_default())))
                (bux::input::text::percentage::percentage_input(
                    "interest_rate",
                    "Interest Rate:",
                    client.interest_rate
                ))
                (bux::input::text::currency::currency_input("monthly_payment", "Monthly Payment: ", Some(client.monthly_payment.unwrap_or_default())))
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}
