#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/note/{client_note_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("View Note");

        // Get the specific note detail
        use crate::api::client0::note::detail::detail;
        let note_detail = detail::call(
            app,
            identity,
            detail::Input {
                client_uuid: path.client_uuid,
                client_note_uuid: path.client_note_uuid,
            },
        )
        .await?;

        // Create a detail table with a single note
        let mut dt = bux::component::detail_table(vec![note_detail]);

        dt.add_column(
            "Date",
            |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_column("Note", |a| html! { (a.note) });
        dt.add_column("Attached Files", |_a| html! { " " });
        doc.add_body(html!(
            section {
                bux-header {
                    h5 { "Note Details" }
                    btn-wrapper {
                        (bux::button::link::label_icon_class("Print Notes ", "fas fa-print", "#print", "success"))
                    }
                }
                (dt)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
