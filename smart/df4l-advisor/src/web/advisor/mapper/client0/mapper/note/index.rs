#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/note/?keyword=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Client Notes");
        doc.page_nav_add_record(
            "Add A Note",
            &crate::ml_advisor_client0_note_add(path.advisor_uuid, path.client_uuid),
        );

        use crate::api::client0::note::list::list;
        let output = list::call(
            app,
            identity,
            list::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        //show print button when list of notes are more then 0
        if !output.note_list.is_empty() {
            // create page_nav_button with print icon
            doc.add_page_nav_button(
                "Print Notes",
                "#print",
                "link",
                Some("success"),
                Some("fas fa-print"),
            );
        }

        let mut dt = bux::component::detail_table(output.note_list);

        dt.add_column(
            "Date",
            |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_column("Note", |a| html! { (a.note) });
        //dt.add_column("Attached Files", |a| html! { (a.attachments) });
        dt.action_button_column("", "View Note", "", "secondary", |a| {
            format!(
                "/advisor/{}/client0/{}/note/{}/",
                path.advisor_uuid, path.client_uuid, a.client_note_uuid
            )
        });

        doc.add_body(html!((dt)));
        Ok(Response::HTML(doc.into()))
    }
}
