import "./add.mcss";
import "@bux/input/textarea/string.mts";

import { SE } from "@granite/lib.mts";
import { add } from "@crate/api/client0/note/addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $client_uuid: HTMLInputElement = SE($form, "[name=client_uuid]");
const $note: BuxInputTextareaString = SE($form, "[name=note]");

new FormPanel({
    $form,
    api: add.api,
    on_cancel: go_back,

    err: (errors) => {
        $note.set_e(errors.note);
    },

    get: () => {
        return {
            client_uuid: $client_uuid.value,
            note: $note.value,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
