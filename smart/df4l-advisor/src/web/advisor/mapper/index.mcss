/* ===== ADVISOR DASHBOARD STYLES ===== */

/* Main Dashboard Container */
.advisor-dashboard {
    margin: 0 auto;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
}

/* Dashboard Header */
.dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
    border-radius: 1rem;
    color: white;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 300;
}

/* Section Titles */
.section-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #0d6efd, #6f42c1);
    border-radius: 2px;
}

/* Dashboard Overview Section */
.dashboard-overview {
    margin-bottom: 2rem;
}

/* Enhanced Quick Info Box */
bux-ui-quick-info-box {
    display: flex;
    gap: 1rem !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
}

bux-ui-quick-info-box x-item {
    flex: 1;
    background: white;
    border: none;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

bux-ui-quick-info-box x-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0d6efd, #6f42c1);
}

bux-ui-quick-info-box x-item .icon-text {
    font-size: 1.1rem;
    font-weight: 600;
    display: block;
    margin-bottom: 0.5rem;
}

bux-ui-quick-info-box x-item .icon {
    color: #2c3e50;
}

bux-ui-quick-info-box x-item a {
    color: #0d6efd;
    text-decoration: underline;
    font-weight: 600;
}

bux-ui-quick-info-box x-item a:hover {
    color: #0a58ca;
}

bux-ui-quick-info-box x-item h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0d6efd;
    margin: 0;
}

/* Dashboard Content */
.dashboard-content {
    display: grid;
    gap: 2rem;

    @media (min-width: 992px) {
        grid-template-columns: 1fr 1fr;
    }
}

/* Setup Section Styles */
.setup-section {
    grid-column: 1 / -1;
}

.setup-incomplete {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border-left: 4px solid #ffc107;
}

.setup-description {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.setup-checklist {
    display: grid;
    gap: 1rem;
}

.setup-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 0.75rem;
    border: 1px solid #e9ecef;
}

.setup-item-content {
    flex: 1;
}

.setup-item-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 0.25rem 0;
}

.setup-item-status {
    font-size: 0.9rem;
    color: #dc3545;
    margin: 0;
    font-weight: 500;
}

.setup-item-action {
    background: #0d6efd;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    border: none;
    cursor: pointer;
}

/* Setup Complete Styles */
.setup-complete {
    grid-column: 1 / -1;
}

.success-card {
    background: linear-gradient(135deg, #d1e7dd 0%, #a3d9a4 100%);
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(25, 135, 84, 0.2);
    border: 1px solid #badbcc;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.success-title {
    font-size: 2rem;
    font-weight: 700;
    color: #0f5132;
    margin-bottom: 1rem;
}

.success-message {
    font-size: 1.1rem;
    color: #0f5132;
    margin: 0;
    line-height: 1.6;
    opacity: 0.9;
}

/* Profile Section Styles */
.profile-section {
    grid-column: 1 / -1;
}

.profile-cards {
    display: grid;
    gap: 1.5rem;

    @media (min-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
    }
}

.profile-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.edit-link {
    background: #6c757d;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.card-content {
    padding: 2rem 1.5rem;
}

.profile-value {
    font-size: 1.1rem;
    font-weight: 500;
    color: #2c3e50;
    display: block;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.profile-value.not-set {
    color: #6c757d;
    font-style: italic;
    background: #fff3cd;
    border-color: #ffeaa7;
}

/* Responsive Design */
@media (max-width: 767px) {
    .advisor-dashboard {
        padding: 1rem 0.5rem;
    }

    .dashboard-header {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
    }

    .profile-cards {
        grid-template-columns: 1fr;
    }

    .setup-item {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .setup-item-action {
        text-align: center;
    }
}