#[approck::api]
pub mod advisor_client_debt_add {
    use chrono::NaiveDate;
    use granite::{Error, ErrorType, ResultExt, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_debt_uuid: Option<Uuid>,
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub name: String,
        pub balance: Option<Decimal>,
        pub balance_date: String,
        pub interest_rate: Option<Decimal>,
        pub monthly_payment: Option<Decimal>,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        let balance_date = NaiveDate::parse_from_str(&input.balance_date, "%Y-%m-%d")
            .map_err(|e| Error::new(ErrorType::Validation).add_context(e))?;

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $client_uuid: &input.client_uuid,
                    $name: &input.name,
                    $balance: &input.balance,
                    $balance_date: &balance_date,
                    $interest_rate: &input.interest_rate,
                    $monthly_payment: &input.monthly_payment,
                    $note: &input.note,
                };
                row = {
                    client_debt_uuid: Uuid,
                };
                INSERT INTO df4l.client0_debt (
                    client_uuid,
                    name,
                    balance,
                    balance_date,
                    interest_rate,
                    monthly_payment,
                    note
                )
                VALUES (
                    $client_uuid,
                    $name,
                    $balance,
                    $balance_date,
                    $interest_rate,
                    $monthly_payment,
                    $note
                )
                RETURNING
                    client_debt_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            detail_url: crate::ml_advisor_client0_debt_details(
                input.advisor_uuid,
                input.client_uuid,
                row.client_debt_uuid,
            ),
            message: "Client debt updated".into(),
        }))
    }
}
