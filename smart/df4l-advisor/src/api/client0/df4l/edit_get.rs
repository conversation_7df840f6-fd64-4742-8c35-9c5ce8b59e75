#[approck::api]
pub mod edit_get {
    use granite::return_authorization_error;
    use serde_json;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client: Client,
        pub advisor: Advisor,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub name: String,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub monthly_budget: Option<Decimal>,
        pub debt_free_extra_pua_list: Vec<ExmExa>,
    }
    #[granite::gtype(ApiOutput)]
    pub struct Advisor {
        pub advisor_uuid: Uuid,
        pub name: String,
    }

    //  {"Month": 48, "Amount": "21739"}
    #[granite::gtype(ApiOutput)]
    #[derive(serde::Deserialize)]
    pub struct ExmExa {
        pub month: i32,
        pub amount: Decimal,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Uuid,
                name: String,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                phone2: Option<String>,
                annual_insurance_premium: Option<Decimal>,
                annual_insurance_pua: Option<Decimal>,
                monthly_budget: Option<Decimal>,
                debt_free_extra_pua_list: String,
            };
            SELECT
                client_uuid,
                advisor_uuid,
                first_name || " "  || last_name AS name,
                first_name,
                last_name,
                email,
                phone,
                phone2,
                annual_insurance_premium,
                annual_insurance_pua,
                monthly_budget,
                debt_free_extra_pua_list::text AS debt_free_extra_pua_list

            FROM
                df4l.client0
            WHERE true
            AND advisor_uuid = $advisor_uuid::uuid
            AND client_uuid = $client_uuid::uuid
        )
        .await?;

        let advisor_row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                advisor_uuid: Uuid,
                name: String,
            };
            SELECT
                advisor_uuid,
                first_name || " "  || last_name AS name
            FROM
                df4l.advisor
            WHERE true
            AND advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        // Parse debt_free_extra_pua_list from JSON string to Vec<ExmExa>
        let debt_free_extra_pua_list: Vec<ExmExa> =
            match serde_json::from_str(&row.debt_free_extra_pua_list) {
                Ok(debt_free_extra_pua_list) => debt_free_extra_pua_list,
                Err(e) => {
                    return Err(granite::Error::new(granite::ErrorType::Unexpected)
                        .set_external_message(format!(
                            "Could not parse debt_free_extra_pua_list: {}",
                            e
                        )));
                }
            };

        Ok(Output {
            client: Client {
                client_uuid: row.client_uuid,
                name: row.name,
                first_name: row.first_name,
                last_name: row.last_name,
                email: row.email,
                phone: row.phone,
                phone2: row.phone2,
                annual_insurance_premium: row.annual_insurance_premium,
                annual_insurance_pua: row.annual_insurance_pua,
                monthly_budget: row.monthly_budget,
                debt_free_extra_pua_list,
            },
            advisor: Advisor {
                advisor_uuid: advisor_row.advisor_uuid,
                name: advisor_row.name,
            },
        })
    }
}
