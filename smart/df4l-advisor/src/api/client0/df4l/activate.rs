#[approck::api]
pub mod activate {

    use granite::return_authorization_error;
    use serde::{Deserialize, Serialize};

    //  {"Month": 48, "Amount": "21739"}
    #[granite::gtype(ApiInput)]
    #[derive(Serialize, Deserialize)]
    pub struct ExmExa {
        pub month: i32,
        pub amount: Decimal,
    }

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub client_uuid: Uuid,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub debt_free_start_date: DateUtc,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $phone: &input.phone,
                $phone2: &input.phone2,
                $debt_free_start_date: &input.debt_free_start_date,
            };
            UPDATE
                df4l.client0
            SET
                phone = $phone,
                phone2 = $phone2,
                debt_free_start_date = $debt_free_start_date
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_advisor_client0_details(input.advisor_uuid, input.client_uuid),
            message: "Aadvisor updated".into(),
        }))
    }
}
