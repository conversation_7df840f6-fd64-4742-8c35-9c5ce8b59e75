#[approck::api]
pub mod add {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub address1: Option<String>,
        pub monthly_budget: Option<Decimal>,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.client_add(input.advisor_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to client add".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $advisor_uuid: &input.advisor_uuid,
                    $first_name: &input.first_name,
                    $last_name: &input.last_name,
                    $email: &input.email,
                    $phone: &input.phone,
                    $phone2: &input.phone2,
                    $monthly_budget: &input.monthly_budget,
                    $annual_insurance_premium: &input.annual_insurance_premium,
                    $annual_insurance_pua: &input.annual_insurance_pua,
                };
                row = {
                    client_uuid: Uuid,
                };

                INSERT INTO
                    df4l.client0
                    (
                        advisor_uuid,
                        first_name,
                        last_name,
                        email,
                        phone,
                        phone2,
                        monthly_budget,
                        annual_insurance_premium,
                        annual_insurance_pua
                    )
                VALUES
                    (
                        $advisor_uuid,
                        $first_name,
                        $last_name,
                        $email,
                        $phone,
                        $phone2,
                        $monthly_budget,
                        $annual_insurance_premium,
                        $annual_insurance_pua
                    )
                RETURNING
                    client_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            client_uuid: row.client_uuid,
            detail_url: crate::ml_advisor_client0_details(input.advisor_uuid, row.client_uuid),
        }))
    }
}
