#[approck::api]
pub mod advisor_detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub advisor_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
    }

    impl Output {
        pub fn name(&self) -> String {
            format!("{} {}", self.first_name, self.last_name)
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!("insufficient permissions to advisor read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                advisor_uuid: Uuid,
                first_name: String,
                last_name: String,
            };

            SELECT
                advisor_uuid,
                first_name,
                last_name
            FROM
                df4l.advisor
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            advisor_uuid: row.advisor_uuid,
            first_name: row.first_name.clone(),
            last_name: row.last_name.clone(),
        }))
    }
}
