#[approck::api]
pub mod advisor_setup_checklist {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct SetupItem {
        pub name: String,
        pub status: String,
        pub action_url: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub setup_items: Vec<SetupItem>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!("Insufficient permissions to advisor read");
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                gbu_advisor_esid: Option<String>,
                identity_uuid: Uuid,
            };
            SELECT
                gbu_advisor_esid,
                identity_uuid
            FROM
                df4l.advisor
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        let gbu_complete = row
            .gbu_advisor_esid
            .as_ref()
            .map(|s| !s.trim().is_empty())
            .unwrap_or(false);

        let identity_uuid = row.identity_uuid;

        let states_row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                count: i64,
            };
            SELECT
                COUNT(*) AS count
            FROM
                df4l.advisor_statelic
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        let states_complete = states_row.count > 0;

        let mut setup_items = vec![];

        // Only add incomplete items to the setup list
        if !gbu_complete {
            setup_items.push(SetupItem {
                name: "GBU Agent ID".to_string(),
                status: "Incomplete".to_string(),
                action_url: crate::ml_myaccount_advisor_gbu(identity_uuid, input.advisor_uuid),
            });
        }

        if !states_complete {
            setup_items.push(SetupItem {
                name: "State Licenses".to_string(),
                status: "Incomplete".to_string(),
                action_url: crate::ml_myaccount_advisor_statelic(identity_uuid, input.advisor_uuid),
            });
        }

        Ok(Output { setup_items })
    }
}
