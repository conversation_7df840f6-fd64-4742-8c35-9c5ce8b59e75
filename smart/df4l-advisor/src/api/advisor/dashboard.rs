#[approck::api]
pub mod dashboard {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub active_legacy_clients: i64,
        pub active_clients: i64,
        pub name: String,
        pub gbu_agent_id: Option<String>,
        pub statelic: Vec<String>,
        pub identity_uuid: Uuid,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!(
                "insufficient permissions to advisor {}",
                input.advisor_uuid
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                first_name: String,
                last_name: String,
                gbu_advisor_esid: Option<String>,
                active_legacy_clients: i64,
                active_clients: i64,
                statelic: Vec<String>,
                identity_uuid: Uuid,
            };
            SELECT
                a.first_name,
                a.last_name,
                a.gbu_advisor_esid,
                (SELECT count(*) FROM df4l.client0 WHERE advisor_uuid = a.advisor_uuid AND active = true) AS active_legacy_clients,
                (SELECT count(*) FROM df4l.client WHERE advisor_uuid = a.advisor_uuid AND active = true) AS active_clients,
                COALESCE(
                    (
                        SELECT array_agg(sl.label ORDER BY als.state_code)
                        FROM df4l.advisor_statelic als
                        INNER JOIN df4l.statelic sl ON als.state_code = sl.state_code
                        WHERE als.advisor_uuid = a.advisor_uuid
                    ),
                    ARRAY[]::text[]
                ) AS statelic,
                a.identity_uuid
            FROM
                df4l.advisor a
            WHERE
                a.advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        let full_name = format!("{} {}", row.first_name, row.last_name);

        Ok(Output {
            active_legacy_clients: row.active_legacy_clients,
            active_clients: row.active_clients,
            name: full_name,
            gbu_agent_id: row.gbu_advisor_esid.filter(|s| !s.trim().is_empty()),
            statelic: row.statelic,
            identity_uuid: row.identity_uuid,
        })
    }
}
