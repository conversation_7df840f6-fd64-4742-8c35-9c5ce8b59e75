#[approck::api]
pub mod client_messages {
    use chrono::{Duration, Utc};
    use granite::Uuid;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct MessageItem {
        pub message_uuid: Uuid,
        pub sent_ts: DateTimeUtc,
        pub to: String,
        pub message: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub messages: Vec<MessageItem>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Create fake message data showing outbound messages sent by advisor
        let now = Utc::now();
        let messages = vec![
            MessageItem {
                message_uuid: Uuid::new_v4(),
                sent_ts: now - Duration::days(3),
                to: "(555) 123-4567".to_string(),
                message: "Hi <PERSON>, this is <PERSON> from Debt Free 4 Life. I wanted to follow up on your debt consolidation plan. Do you have any questions about the next steps?".to_string(),
            },
            MessageItem {
                message_uuid: Uuid::new_v4(),
                sent_ts: now - Duration::days(2),
                to: "(555) 123-4567".to_string(),
                message: "Just wanted to check in - did you receive the payment schedule I sent via email? Please let me know if you have any questions about the timeline.".to_string(),
            },
            MessageItem {
                message_uuid: Uuid::new_v4(),
                sent_ts: now - Duration::days(1),
                to: "(555) 123-4567".to_string(),
                message: "Great news! I've finalized your debt consolidation plan. Your first payment will be due on the 15th of next month. I'll call you tomorrow to go over the details.".to_string(),
            },
            MessageItem {
                message_uuid: Uuid::new_v4(),
                sent_ts: now - Duration::hours(6),
                to: "(555) 123-4567".to_string(),
                message: "Reminder: We have a call scheduled for 2 PM today to review your debt consolidation plan. Looking forward to speaking with you!".to_string(),
            },
            MessageItem {
                message_uuid: Uuid::new_v4(),
                sent_ts: now - Duration::hours(1),
                to: "(555) 123-4567".to_string(),
                message: "Thank you for our call today! As discussed, I've sent the updated payment schedule to your email. We're here to help you succeed on your debt-free journey!".to_string(),
            },
        ];

        Ok(Output { messages })
    }
}
