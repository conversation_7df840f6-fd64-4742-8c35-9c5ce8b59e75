#[granite::gtype(ApiOutput)]
pub struct Debt {
    pub client_debt_uuid: Uuid,
    pub is_external: bool,
    pub name: Option<String>,
    pub balance: Option<Decimal>,
    pub balance_date: Option<DateUtc>,
    pub interest: Option<Decimal>,
    pub payment: Option<Decimal>,
    pub active: bool,
}

/// Returns a merged view of CRS debts plus manually entered debts
#[approck::api]
pub mod client_debt_merged {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub debt_list: Vec<Debt>,
    }

    #[granite::gtype]
    pub type Debt = super::Debt;

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let debts = df4l_crs::core::get_client_debts_merged(app, input.client_uuid).await?;

        let debt_list = debts
            .debts
            .into_iter()
            .map(|debt| Debt {
                client_debt_uuid: debt.client_debt_uuid,
                is_external: debt.client_debt_esid.is_some(),
                name: debt.name,
                balance: debt.balance,
                balance_date: debt.balance_date,
                interest: debt.interest,
                payment: debt.payment,
                active: debt.active,
            })
            .collect();

        Ok(Output { debt_list })
    }
}

#[approck::api]
pub mod client_debt_add {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub debt: Debt,
    }

    #[granite::gtype]
    pub type Debt = super::Debt;

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_debt_uuid: Uuid,
                active: bool,
                balance_date: DateUtc,
            };
            INSERT INTO df4l.client_debt (
                client_uuid,
                active,
                balance_date
            )
            VALUES (
                $client_uuid,
                true,
                CURRENT_DATE
            )
            RETURNING
                client_debt_uuid,
                active,
                balance_date
        )
        .await?;

        let debt = Debt {
            client_debt_uuid: row.client_debt_uuid,
            is_external: false,
            name: None,
            balance: None,
            balance_date: Some(row.balance_date),
            interest: None,
            payment: None,
            active: row.active,
        };

        Ok(Output { debt })
    }
}

#[approck::api]
pub mod client_debt_delete {
    use granite::{return_authorization_error, return_invalid_operation};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // validate that the debt is inactive
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
            };
            row = {
                active: bool,
                is_external: bool,
            };
            SELECT
                active,
                client_debt_esid IS NOT NULL AS is_external
            FROM
                df4l.client_debt
            WHERE
                // SECON: restrict to client_uuid
                client_uuid = $client_uuid
                AND client_debt_uuid = $client_debt_uuid

        )
        .await?;

        if row.active {
            return_invalid_operation!("Cannot delete an active debt");
        }

        if row.is_external {
            return_invalid_operation!("Cannot delete an external debt");
        }

        // Delete the debt
        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
            };
            DELETE FROM
                df4l.client_debt
            WHERE
                // SECON: restrict to client_uuid
                client_uuid = $client_uuid

                AND client_debt_uuid = $client_debt_uuid
        )
        .await?;

        Ok(Output {})
    }
}

#[approck::api]
pub mod client_debt_save_status {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
        pub active: bool,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Update the debt's active status
        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
                $active: &input.active,
            };
            UPDATE
                df4l.client_debt
            SET
                active = $active

            WHERE
                // SECON: restrict to client_uuid
                client_uuid = $client_uuid

                AND client_debt_uuid = $client_debt_uuid
        )
        .await?;

        Ok(Output {})
    }
}

#[approck::api]
pub mod client_debt_save {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
        pub name: Option<String>,
        pub balance: Option<Decimal>,
        pub balance_date: Option<DateUtc>,
        pub interest: Option<Decimal>,
        pub payment: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Update the debt
        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
                $name: &input.name,
                $balance: &input.balance,
                $balance_date: &input.balance_date,
                $interest: &input.interest,
                $payment: &input.payment,
            };
            UPDATE
                df4l.client_debt
            SET
                name = $name,
                balance = $balance,
                balance_date = $balance_date,
                interest = $interest,
                payment = $payment
            WHERE
                // SECON: restrict to client_uuid
                client_uuid = $client_uuid

                AND client_debt_uuid = $client_debt_uuid
        )
        .await?;

        Ok(Output {})
    }
}
