#[approck::api]
pub mod client_budget_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub extra_debt_payments_enabled: bool,
        pub extra_debt_payments_value: Option<Decimal>,
        pub retirement_contributions_enabled: bool,
        pub retirement_contributions_value: Option<Decimal>,
        pub regular_savings_enabled: bool,
        pub regular_savings_value: Option<Decimal>,
        pub mortgage_program_enabled: bool,
        pub mortgage_program_value: Option<Decimal>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        // Mock data for now - no database queries
        Ok(Output {
            extra_debt_payments_enabled: false,
            extra_debt_payments_value: None,
            retirement_contributions_enabled: false,
            retirement_contributions_value: None,
            regular_savings_enabled: false,
            regular_savings_value: None,
            mortgage_program_enabled: false,
            mortgage_program_value: None,
        })
    }
}

#[approck::api]
pub mod advisor_client_budget_set {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub extra_debt_payments_enabled: bool,
        pub extra_debt_payments_value: Option<Decimal>,
        pub retirement_contributions_enabled: bool,
        pub retirement_contributions_value: Option<Decimal>,
        pub regular_savings_enabled: bool,
        pub regular_savings_value: Option<Decimal>,
        pub mortgage_program_enabled: bool,
        pub mortgage_program_value: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Mock implementation - no database operations for now
        // In the future, this would save the budget data to the database

        Ok(Output {
            message: "Budget information saved successfully".to_string(),
        })
    }
}
