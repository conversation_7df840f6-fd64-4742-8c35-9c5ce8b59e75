#[approck::api]
pub mod client_wizard {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub edit_complete: bool,
        pub crs_complete: bool,
        pub debt_complete: bool,
        pub budget_complete: bool,
        pub icover_complete: bool,
        pub report_complete: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                advisor_uuid: Uuid,
            };
            SELECT
                advisor_uuid
            FROM
                df4l.client
            WHERE true
                AND client_uuid = $client_uuid::uuid
        )
        .await?;

        Ok(Output {
            client_uuid: input.client_uuid,
            advisor_uuid: row.advisor_uuid,
            edit_complete: true,
            crs_complete: true,
            debt_complete: false,
            budget_complete: false,
            icover_complete: false,
            report_complete: false,
        })
    }
}
