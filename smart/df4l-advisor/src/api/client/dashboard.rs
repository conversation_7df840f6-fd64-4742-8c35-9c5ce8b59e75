#[approck::api]
pub mod client_dashboard {
    use granite::return_authorization_error;
    use granite::{Decimal, Uuid};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_base: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client: Client,
        pub debt_info: DebtInfo,
        // Legacy fields for compatibility with edit page
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub name: String,
        pub create_ts: DateTimeUtc,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub country: Option<String>,
        pub note: Option<String>,
        pub active: bool,
    }

    impl Output {
        pub fn address(&self) -> String {
            let mut parts = Vec::new();

            if let Some(addr1) = &self.address1 {
                parts.push(addr1.clone());
            }
            if let Some(addr2) = &self.address2 {
                parts.push(addr2.clone());
            }
            if let Some(city) = &self.city {
                parts.push(city.clone());
            }
            if let Some(state) = &self.state {
                parts.push(state.clone());
            }
            if let Some(zip) = &self.zip {
                parts.push(zip.clone());
            }
            if let Some(country) = &self.country {
                parts.push(country.clone());
            }

            parts.join(" ")
        }
    }

    #[granite::gtype(ApiOutput)]
    pub struct DebtInfoDebtItem {
        pub client_debt_uuid: Uuid,
        pub debt_name: String,
        pub full_name: String,
        pub balance: Decimal,
        pub interest_rate: Decimal,
        pub monthly_payment: Decimal,
        pub effective_interest_cost_percent: Decimal,
        pub is_paid_off_as_of_today: bool,
        pub payoff_date_formatted: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct DebtInfo {
        pub start_year: u32,
        pub start_month: u32,
        pub debt_balance: Option<Decimal>,
        pub annual_budget: Option<Decimal>,
        pub minimum_monthly_payment: Option<Decimal>,
        pub total_paid: Decimal,
        pub total_unpaid: Decimal,
        pub esimated_cash_value: Decimal,
        pub debt_list: Vec<DebtInfoDebtItem>,
    }

    // Helper function to format date as "YY, Mon"
    pub fn date_formatted_yy_mon(year: u32, month: u32) -> String {
        let month_names = [
            "", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec",
        ];
        let month_name = month_names.get(month as usize).unwrap_or(&"");
        format!("'{:02}, {}", year % 100, month_name)
    }

    // Helper function to format decimal with fallback
    pub fn format_decimal_optional(value: Option<Decimal>, fallback: &str) -> String {
        match value {
            Some(v) => v.to_string(),
            None => fallback.to_string(),
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        // Get basic client info from database
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Uuid,
                first_name: String,
                last_name: String,
                name: String,
                create_ts: DateTimeUtc,
                email: Option<String>,
                phone: Option<String>,
                phone2: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                country: Option<String>,
                note: Option<String>,
                active: bool,
            };
            SELECT
                client_uuid,
                advisor_uuid,
                first_name,
                last_name,
                first_name || " " || last_name AS name,
                create_ts,
                email,
                phone,
                phone2,
                address1,
                address2,
                city,
                state,
                zip,
                country,
                note,
                active
            FROM
                df4l.client
            WHERE true
                AND client_uuid = $client_uuid::uuid
        )
        .await?;

        // Create fake client data
        let client = Client {
            client_uuid: row.client_uuid,
            name: row.name.clone(),
            email: row.email.clone(),
            phone: row.phone.clone(),
            phone2: row.phone2.clone(),
            annual_insurance_premium: Some(Decimal::new(6000, 0)),
            annual_insurance_base: Some(Decimal::new(4000, 0)),
            annual_insurance_pua: Some(Decimal::new(2000, 0)),
        };

        // Create fake debt items
        let debt_list = vec![
            DebtInfoDebtItem {
                client_debt_uuid: Uuid::new_v4(),
                debt_name: "Credit Card 1".to_string(),
                full_name: "Chase Sapphire Credit Card".to_string(),
                balance: Decimal::new(5000, 0),
                interest_rate: Decimal::new(1899, 2), // 18.99%
                monthly_payment: Decimal::new(150, 0),
                effective_interest_cost_percent: Decimal::new(1899, 2),
                is_paid_off_as_of_today: false,
                payoff_date_formatted: Some("'25, Mar".to_string()),
            },
            DebtInfoDebtItem {
                client_debt_uuid: Uuid::new_v4(),
                debt_name: "Auto Loan".to_string(),
                full_name: "Honda Civic Auto Loan".to_string(),
                balance: Decimal::new(15000, 0),
                interest_rate: Decimal::new(549, 2), // 5.49%
                monthly_payment: Decimal::new(350, 0),
                effective_interest_cost_percent: Decimal::new(549, 2),
                is_paid_off_as_of_today: false,
                payoff_date_formatted: Some("'26, Aug".to_string()),
            },
            DebtInfoDebtItem {
                client_debt_uuid: Uuid::new_v4(),
                debt_name: "Personal Loan".to_string(),
                full_name: "Personal Consolidation Loan".to_string(),
                balance: Decimal::new(8000, 0),
                interest_rate: Decimal::new(1249, 2), // 12.49%
                monthly_payment: Decimal::new(200, 0),
                effective_interest_cost_percent: Decimal::new(1249, 2),
                is_paid_off_as_of_today: true,
                payoff_date_formatted: Some("'24, Dec".to_string()),
            },
        ];

        // Calculate totals
        let total_balance = debt_list.iter().map(|d| d.balance).sum::<Decimal>();
        let minimum_monthly_payment = debt_list.iter().map(|d| d.monthly_payment).sum::<Decimal>();
        let total_paid = Decimal::new(12000, 0);
        let total_unpaid = total_balance - total_paid;
        let estimated_cash_value = Decimal::new(8500, 0);

        // Create debt info
        let debt_info = DebtInfo {
            start_year: 2024,
            start_month: 1,
            debt_balance: Some(total_balance),
            annual_budget: Some(Decimal::new(24000, 0)), // $2000/month
            minimum_monthly_payment: Some(minimum_monthly_payment),
            total_paid,
            total_unpaid,
            esimated_cash_value: estimated_cash_value,
            debt_list,
        };

        Ok(Output {
            client,
            debt_info,
            // Legacy fields for compatibility
            client_uuid: row.client_uuid,
            advisor_uuid: row.advisor_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            name: row.name,
            create_ts: row.create_ts,
            email: row.email,
            phone: row.phone,
            phone2: row.phone2,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,
            country: row.country,
            note: row.note,
            active: row.active,
        })
    }
}
