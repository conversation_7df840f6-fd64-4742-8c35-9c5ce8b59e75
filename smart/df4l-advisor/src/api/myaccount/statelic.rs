#[approck::api]
pub mod advisor_onboarding_checklist_statelic_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub states: Vec<StateInfo>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct StateInfo {
        pub state_code: String,
        pub label: String,
        pub has_license: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!("identity.advisor_read({})", input.advisor_uuid);
        }

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                state_code: String,
                label: String,
                has_license: bool,
            };
            SELECT
                state_code,
                label,
                EXISTS (
                    SELECT
                    FROM df4l.advisor_statelic
                    WHERE advisor_uuid = $advisor_uuid::uuid
                    AND state_code = statelic.state_code
                ) AS has_license
            FROM
                df4l.statelic
            ORDER BY
                label
        )
        .await?;

        let states = rows
            .into_iter()
            .map(|r| StateInfo {
                state_code: r.state_code,
                label: r.label,
                has_license: r.has_license,
            })
            .collect();

        Ok(Output { states })
    }
}

#[approck::api]
pub mod advisor_onboarding_checklist_statelic_set {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub state_codes: Vec<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let mut dbcx = app.postgres_dbcx().await?;

        if !identity.advisor_write(input.advisor_uuid) {
            return_authorization_error!("identity.advisor_write({})", input.advisor_uuid);
        }

        // Transaction block
        {
            let dbtx = dbcx.transaction().await?;

            // First, delete all existing licenses that are not in the input list
            let uppercase_state_codes: Vec<String> = input
                .state_codes
                .iter()
                .map(|code| code.to_uppercase())
                .collect();

            granite::pg_execute!(
                db = dbtx;
                args = {
                    $advisor_uuid: &input.advisor_uuid,
                    $state_codes: &uppercase_state_codes,
                };
                DELETE FROM df4l.advisor_statelic
                WHERE advisor_uuid = $advisor_uuid::uuid
                AND state_code NOT IN (SELECT unnest($state_codes::text[]))
            )
            .await?;

            // Then, insert the selected licenses if they don't exist, by selecting from statelic table
            granite::pg_execute!(
                db = dbtx;
                args = {
                    $advisor_uuid: &input.advisor_uuid,
                    $state_codes: &uppercase_state_codes,
                };
                INSERT INTO df4l.advisor_statelic
                    (advisor_uuid, state_code)
                SELECT
                    $advisor_uuid::uuid, state_code
                FROM
                    df4l.statelic
                WHERE true
                    AND state_code = ANY($state_codes::text[])
                    AND NOT EXISTS (
                        SELECT
                        FROM df4l.advisor_statelic
                        WHERE advisor_uuid = $advisor_uuid::uuid
                        AND state_code = df4l.statelic.state_code
                    )
            )
            .await?;

            dbtx.commit().await?;
        }

        // Get the identity_uuid for the advisor to construct the correct redirect URL
        let identity_row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                identity_uuid: Uuid,
            };
            SELECT
                identity_uuid
            FROM
                df4l.advisor
            WHERE
                advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        Ok(Output {
            detail_url: format!("/myaccount/{}/", identity_row.identity_uuid),
            message: "State licenses updated successfully".to_string(),
        })
    }
}
