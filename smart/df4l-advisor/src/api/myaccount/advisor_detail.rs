#[approck::api]
pub mod detail_advisor {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub country: Option<String>,
        pub gbu_agent_id: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!("identity.advisor_read({})", input.advisor_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid
            };
            row = {
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                country: Option<String>,
                gbu_advisor_esid: Option<String>,
            };
            SELECT
                first_name,
                last_name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                country,
                gbu_advisor_esid
            FROM
                df4l.advisor
            WHERE
                advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        let gbu_agent_id = match row.gbu_advisor_esid {
            Some(id) if !id.is_empty() => Some(id),
            _ => None,
        };

        Ok(Output {
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,
            country: row.country,
            gbu_agent_id,
        })
    }
}
