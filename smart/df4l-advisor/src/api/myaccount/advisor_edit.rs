#[approck::api]
pub mod edit_advisor {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub first_name: Option<String>,
        pub last_name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub country: Option<String>,
        pub gbu_agent_id: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.advisor_write(input.advisor_uuid) {
            return_authorization_error!("identity.advisor_write({})", input.advisor_uuid);
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $first_name: &input.first_name,
                $last_name: &input.last_name,
                $email: &input.email,
                $phone: &input.phone,
                $address1: &input.address1,
                $address2: &input.address2,
                $city: &input.city,
                $state: &input.state,
                $zip: &input.zip,
                $country: &input.country,
                $gbu_agent_id: &input.gbu_agent_id,
            };
            UPDATE
                df4l.advisor
            SET
                first_name = COALESCE($first_name, first_name),
                last_name = COALESCE($last_name, last_name),
                email = COALESCE($email, email),
                phone = COALESCE($phone, phone),
                address1 = COALESCE($address1, address1),
                address2 = COALESCE($address2, address2),
                city = COALESCE($city, city),
                state = COALESCE($state, state),
                zip = COALESCE($zip, zip),
                country = COALESCE($country, country),
                gbu_advisor_esid = COALESCE($gbu_agent_id, gbu_advisor_esid)
            WHERE
                advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        let identity_row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                identity_uuid: Uuid,
            };
            SELECT
                identity_uuid
            FROM
                df4l.advisor
            WHERE
                advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        let message = if input.gbu_agent_id.is_some()
            && (input.first_name.is_some() || input.email.is_some())
        {
            "Advisor profile updated successfully".to_string()
        } else if input.gbu_agent_id.is_some() {
            "GBU Agent ID updated".to_string()
        } else {
            "Contact information updated successfully".to_string()
        };

        Ok(Output {
            detail_url: format!("/myaccount/{}/", identity_row.identity_uuid),
            message,
        })
    }
}
