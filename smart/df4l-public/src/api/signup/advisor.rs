#[approck::api]
pub mod signup_advisor_create {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_advisor_uuid: Uuid,
        pub signup_url: String,
    }

    pub async fn call(app: App, identity: Identity) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.signup_advisor_create() {
            return_authorization_error!("signup_advisor_create");
        }

        let session_token = identity.session_token();
        let remote_addr = identity.get_address();

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $session_token: &session_token,
                $remote_addr: &remote_addr,
            };
            row = {
                signup_advisor_uuid: Uuid,
            };
            INSERT INTO df4l.signup_advisor (session_token, create_addr)
            VALUES ($session_token, $remote_addr::text::cidr)
            RETURNING signup_advisor_uuid
        )
        .await?;

        let signup_url = crate::ml_signup_advisor(row.signup_advisor_uuid);

        Ok(Output {
            signup_advisor_uuid: row.signup_advisor_uuid,
            signup_url,
        })
    }
}

#[approck::api]
pub mod signup_advisor_wizard_data {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_advisor_uuid: Uuid,
        pub step_contact_enabled: bool,
        pub step_contact_complete: bool,
        pub step_contact_error: Option<String>,
        pub step_verify_enabled: bool,
        pub step_verify_complete: bool,
        pub step_verify_error: Option<String>,
        pub step_terms_enabled: bool,
        pub step_terms_complete: bool,
        pub step_terms_error: Option<String>,
        pub step_billing_enabled: bool,
        pub step_billing_complete: bool,
        pub step_billing_error: Option<String>,
        pub done: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_read(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_read");
        }

        let wizard_data =
            crate::core::signup::advisor::Signup::load(&dbcx, input.signup_advisor_uuid).await?;

        Ok(Output {
            signup_advisor_uuid: input.signup_advisor_uuid,
            step_contact_enabled: true,
            step_contact_complete: wizard_data.contact.is_ok(),
            step_contact_error: wizard_data
                .contact
                .err()
                .map(|_| "Contact Information Missing".to_string()),
            step_verify_enabled: true,
            step_verify_complete: wizard_data.verify.is_ok(),
            step_verify_error: wizard_data
                .verify
                .err()
                .map(|_| "Verification Incomplete".to_string()),
            step_terms_enabled: true,
            step_terms_complete: wizard_data.terms.is_ok(),
            step_terms_error: wizard_data
                .terms
                .err()
                .map(|_| "Terms & Conditions Not Accepted".to_string()),
            step_billing_enabled: true,
            step_billing_complete: false,
            step_billing_error: None,
            done: wizard_data.done.is_some(),
        })
    }
}

#[approck::api]
pub mod signup_advisor_contact_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_advisor_uuid: Uuid,
        pub first_name: Option<String>,
        pub last_name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_read(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                signup_advisor_uuid: Uuid,
                first_name: Option<String>,
                last_name: Option<String>,
                email: Option<String>,
                phone: Option<String>,
            };
            SELECT
                signup_advisor_uuid,
                first_name,
                last_name,
                email,
                phone
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        Ok(Output {
            signup_advisor_uuid: row.signup_advisor_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
        })
    }
}

#[approck::api]
pub mod signup_advisor_contact_set {
    use granite::{NestedError, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        let contact_result = crate::core::signup::advisor::Contact_Partial {
            first_name: Some(input.first_name),
            last_name: Some(input.last_name),
            email: Some(input.email),
            phone: Some(input.phone),
        }
        .validate();

        println!("contact_result: {:#?}", contact_result);

        let contact_validated = match contact_result {
            Ok(contact) => contact,
            Err(errors) => {
                return Ok(Response::ValidationError(NestedError {
                    outer: "Validation Error".to_string(),
                    inner: Some(Input_Error {
                        signup_advisor_uuid: None,
                        first_name: errors.first_name,
                        last_name: errors.last_name,
                        email: errors.email,
                        phone: errors.phone,
                    }),
                }));
            }
        };

        let previous_data = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                previous_email: Option<String>,
                previous_phone: Option<String>,
            };
            SELECT
                email as previous_email,
                phone as previous_phone
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let email_changed =
            previous_data.previous_email.as_deref() != Some(&contact_validated.email);
        let phone_changed =
            previous_data.previous_phone.as_deref() != Some(&contact_validated.phone);

        if email_changed || phone_changed {
            // Clear verification fields when email or phone changes
            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $first_name: &contact_validated.first_name,
                    $last_name: &contact_validated.last_name,
                    $email: &contact_validated.email,
                    $phone: &contact_validated.phone,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    first_name = $first_name,
                    last_name = $last_name,
                    email = $email,
                    phone = $phone,
                    email_code = NULL,
                    email_code_sent_ts = NULL,
                    email_code_expire_ts = NULL,
                    email_code_attempts = 0,
                    email_verified_ts = NULL,
                    phone_code = NULL,
                    phone_code_sent_ts = NULL,
                    phone_code_expire_ts = NULL,
                    phone_code_attempts = 0,
                    phone_verified_ts = NULL
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;
        } else {
            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $first_name: &contact_validated.first_name,
                    $last_name: &contact_validated.last_name,
                    $email: &contact_validated.email,
                    $phone: &contact_validated.phone,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    first_name = $first_name,
                    last_name = $last_name,
                    email = $email,
                    phone = $phone
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;
        }

        Ok(Response::Output(Output {}))
    }
}

#[approck::api]
pub mod signup_advisor_verify_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_advisor_uuid: Uuid,
        pub email: String,
        pub phone: String,
        pub email_code_sent: bool,
        pub phone_code_sent: bool,
        pub email_masked: String,
        pub phone_masked: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                email: Option<String>,
                phone: Option<String>,
                email_code: Option<String>,
                email_code_expire_ts: Option<DateTimeUtc>,
                phone_code: Option<String>,
                phone_code_expire_ts: Option<DateTimeUtc>,
            };
            SELECT
                email,
                phone,
                email_code,
                email_code_expire_ts,
                phone_code,
                phone_code_expire_ts
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let email = row.email.unwrap_or_default();
        let phone = row.phone.unwrap_or_default();
        let now = granite::Utc::now();

        // Check if codes are valid (not expired)
        let email_code_valid =
            row.email_code.is_some() && row.email_code_expire_ts.is_some_and(|exp| exp > now);
        let phone_code_valid =
            row.phone_code.is_some() && row.phone_code_expire_ts.is_some_and(|exp| exp > now);

        let mut email_code_sent = email_code_valid;
        let mut phone_code_sent = phone_code_valid;

        // Generate and send email code if needed
        if !email.is_empty() && !email_code_valid {
            // Generate 4-digit numeric code from hex
            let hex_code = granite::random_hex(4);
            let numeric_value = u32::from_str_radix(&hex_code, 16).unwrap_or(0) % 10000;
            let email_code = format!("{:04}", numeric_value);
            let expire_ts = now + granite::Duration::minutes(60);

            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $email_code: &email_code,
                    $email_code_sent_ts: &now,
                    $email_code_expire_ts: &expire_ts,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    email_code = $email_code,
                    email_code_sent_ts = $email_code_sent_ts,
                    email_code_expire_ts = $email_code_expire_ts
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;

            // Send email with verification code using SendGrid
            let email_subject = "Your D2C Verification Code";
            let email_message = format!(
                "Your verification code is: {}\n\nThis code will expire in 60 minutes.\n\nIf you did not request this code, please ignore this email.",
                email_code
            );

            match app
                .sendgrid()
                .send_email(app, "default", &email, email_subject, &email_message)
                .await
            {
                Ok(()) => {
                    approck::info!("Email verification code sent to {}", email);
                    email_code_sent = true;
                }
                Err(e) => {
                    approck::error!("Failed to send email verification code to {}: {}", email, e);
                    // Still mark as sent since code is generated and stored
                    // User can contact support if they don't receive the email
                    email_code_sent = true;
                }
            }
        }

        // Generate and send phone code if needed
        if !phone.is_empty() && !phone_code_valid {
            // Generate 4-digit numeric code from hex
            let hex_code = granite::random_hex(4);
            let numeric_value = u32::from_str_radix(&hex_code, 16).unwrap_or(0) % 10000;
            let phone_code = format!("{:04}", numeric_value);
            let expire_ts = now + granite::Duration::minutes(60);

            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $phone_code: &phone_code,
                    $phone_code_sent_ts: &now,
                    $phone_code_expire_ts: &expire_ts,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    phone_code = $phone_code,
                    phone_code_sent_ts = $phone_code_sent_ts,
                    phone_code_expire_ts = $phone_code_expire_ts
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;

            // Send SMS with verification code using Twilio
            let sms_message = format!(
                "Your D2C verification code is: {}\n\nThis code will expire in 60 minutes.\n\nIf you did not request this code, please ignore this message.",
                phone_code
            );

            // Debug logging for Twilio configuration
            let sender_map = &app.twilio().sender_map;
            approck::info!(
                "Available Twilio senders: {:?}",
                sender_map.keys().collect::<Vec<_>>()
            );

            if !sender_map.contains_key("default") {
                approck::error!(
                    "Twilio sender key 'default' not found in configuration. Available keys: {:?}",
                    sender_map.keys().collect::<Vec<_>>()
                );
                phone_code_sent = false; // Don't mark as sent if configuration is wrong
            } else {
                approck::info!("Attempting to send SMS to {} using sender 'default'", phone);

                match app
                    .twilio()
                    .send_sms(app, "default", &phone, &sms_message)
                    .await
                {
                    Ok(()) => {
                        approck::info!("SMS verification code sent successfully to {}", phone);
                        phone_code_sent = true;
                    }
                    Err(e) => {
                        approck::error!("Failed to send SMS verification code to {}: {}", phone, e);
                        // Still mark as sent since code is generated and stored
                        // User can contact support if they don't receive the SMS
                        phone_code_sent = true;
                    }
                }
            }
        }

        // Create masked versions for display
        let email_masked = if email.len() > 3 {
            let at_pos = email.find('@').unwrap_or(email.len());
            if at_pos > 3 {
                format!("{}***{}", &email[..2], &email[at_pos..])
            } else {
                email.clone()
            }
        } else {
            email.clone()
        };

        let phone_masked = if phone.len() > 4 {
            format!("***{}", &phone[phone.len() - 4..])
        } else {
            phone.clone()
        };

        Ok(Output {
            signup_advisor_uuid: input.signup_advisor_uuid,
            email,
            phone,
            email_code_sent,
            phone_code_sent,
            email_masked,
            phone_masked,
        })
    }
}

#[approck::api]
pub mod signup_advisor_verify_set {
    use granite::{NestedError, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
        pub email_code: Option<String>,
        pub phone_code: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                email: Option<String>,
                phone: Option<String>,
                email_code: Option<String>,
                email_code_expire_ts: Option<DateTimeUtc>,
                email_verified_ts: Option<DateTimeUtc>,
                phone_code: Option<String>,
                phone_code_expire_ts: Option<DateTimeUtc>,
                phone_verified_ts: Option<DateTimeUtc>,
            };
            SELECT
                email,
                phone,
                email_code,
                email_code_expire_ts,
                email_verified_ts,
                phone_code,
                phone_code_expire_ts,
                phone_verified_ts
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let now = granite::Utc::now();
        let mut errors = Input_Error {
            signup_advisor_uuid: None,
            email_code: None,
            phone_code: None,
        };

        let mut email_verified = row.email_verified_ts.is_some();
        let mut phone_verified = row.phone_verified_ts.is_some();

        // Validate email code if provided
        if let Some(provided_email_code) = &input.email_code {
            if provided_email_code.trim().is_empty() {
                errors.email_code = Some("Email verification code is required.".to_string());
            } else if row.email_code.is_none() {
                errors.email_code = Some("No email verification code was sent.".to_string());
            } else if row.email_code_expire_ts.is_none() || row.email_code_expire_ts.unwrap() < now
            {
                errors.email_code = Some("Email verification code has expired.".to_string());
            } else if row.email_code.as_ref() != Some(provided_email_code) {
                errors.email_code = Some("Invalid email verification code.".to_string());
            } else {
                email_verified = true;
            }
        }

        // Validate phone code if provided
        if let Some(provided_phone_code) = &input.phone_code {
            if provided_phone_code.trim().is_empty() {
                errors.phone_code = Some("Phone verification code is required.".to_string());
            } else if row.phone_code.is_none() {
                errors.phone_code = Some("No phone verification code was sent.".to_string());
            } else if row.phone_code_expire_ts.is_none() || row.phone_code_expire_ts.unwrap() < now
            {
                errors.phone_code = Some("Phone verification code has expired.".to_string());
            } else if row.phone_code.as_ref() != Some(provided_phone_code) {
                errors.phone_code = Some("Invalid phone verification code.".to_string());
            } else {
                phone_verified = true;
            }
        }

        // Return validation errors if any
        if errors.email_code.is_some() || errors.phone_code.is_some() {
            return Ok(Response::ValidationError(NestedError {
                outer: "Verification Error".to_string(),
                inner: Some(errors),
            }));
        }

        // Update verification timestamps if codes were validated
        if input.email_code.is_some() || input.phone_code.is_some() {
            let email_verified_ts = if email_verified {
                Some(now)
            } else {
                row.email_verified_ts
            };
            let phone_verified_ts = if phone_verified {
                Some(now)
            } else {
                row.phone_verified_ts
            };

            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $email_verified_ts: &email_verified_ts,
                    $phone_verified_ts: &phone_verified_ts,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    email_verified_ts = $email_verified_ts,
                    phone_verified_ts = $phone_verified_ts
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;
        }

        Ok(Response::Output(Output {}))
    }
}

#[approck::api]
pub mod signup_advisor_terms_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub document_uuid: Uuid,
        pub revision: String,
        pub terms_html: String,
        pub terms_accepted_name: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_read(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                terms_accepted_name: Option<String>,
            };
            SELECT
                terms_accepted_name
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let legal_plane_document =
            match legal_plane::core::load_active_by_psid(&dbcx, "AdvisorAgreement").await? {
                Some(document) => document,
                None => {
                    return Err(granite::Error::process_error(
                        "Unable to find current terms document. Please contact support."
                            .to_string(),
                    ));
                }
            };

        Ok(Output {
            document_uuid: legal_plane_document.document_uuid,
            revision: legal_plane_document.revision,
            terms_html: legal_plane_document.body_html,
            terms_accepted_name: row.terms_accepted_name,
        })
    }
}

#[approck::api]
pub mod signup_advisor_terms_set {
    use granite::{NestedError, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,

        // for confirmation that nothing changed during the user's review
        pub document_uuid: Uuid,

        // for confirmation that nothing changed during the user's review
        pub revision: String,

        /// Users first and last name
        pub terms_accepted_name: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        // get the current terms document from the legal plane module
        let legal_plane_document =
            match legal_plane::core::load_active_by_psid(&dbcx, "AdvisorAgreement").await? {
                Some(document) => document,
                None => {
                    return Err(granite::Error::process_error(
                        "Unable to find current terms document. Please contact support."
                            .to_string(),
                    ));
                }
            };

        if legal_plane_document.document_uuid != input.document_uuid {
            return Err(granite::Error::process_error(
                "Document content has changed. Please refresh.".to_string(),
            ));
        }

        if legal_plane_document.revision != input.revision {
            return Err(granite::Error::process_error(
                "Document content has changed. Please refresh.".to_string(),
            ));
        }

        // pull the first and last name from the signup table
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                first_name: Option<String>,
                last_name: Option<String>,
            };
            SELECT
                first_name,
                last_name
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let remote_addr = identity.remote_addr();

        let terms = match (crate::core::signup::advisor::Terms_Partial {
            first_name: row.first_name,
            last_name: row.last_name,
            terms_accepted_name: Some(input.terms_accepted_name),
            document_uuid: Some(legal_plane_document.document_uuid),
            revision: Some(legal_plane_document.revision),
            remote_addr: Some(remote_addr),
        })
        .validate()
        {
            Ok(terms) => terms,
            Err(errors) => {
                return Ok(Response::ValidationError(NestedError {
                    outer: "Validation Error".to_string(),
                    inner: Some(Input_Error {
                        signup_advisor_uuid: None,
                        document_uuid: None,
                        revision: None,
                        terms_accepted_name: errors.terms_accepted_name,
                    }),
                }));
            }
        };

        let remote_addr = identity.get_address();

        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
                $terms_document_uuid: &terms.document_uuid,
                $terms_revision: &terms.revision,
                $terms_accepted_name: &terms.terms_accepted_name,
                $terms_accepted_addr: &remote_addr,
            };
            UPDATE
                df4l.signup_advisor
            SET
                terms_document_uuid = $terms_document_uuid,
                terms_revision = $terms_revision,
                terms_accepted_name = $terms_accepted_name,
                terms_accepted_addr = $terms_accepted_addr::text::cidr
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        Ok(Response::Output(Output {}))
    }
}

#[approck::api]
pub mod signup_advisor_billing_save {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_advisor_write(&dbcx, input.signup_advisor_uuid)
            .await
        {
            return_authorization_error!("signup_advisor_write");
        }

        //Find first and last name, email from signup_advisor_uuid
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_uuid: &input.signup_advisor_uuid,
            };
            row = {
                first_name: String,
                last_name: String,
                email: Option<String>,
                stripe_customer_id: Option<String>,
            };
            SELECT
                first_name,
                last_name,
                email,
                stripe_customer_id
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid
        )
        .await?;

        let customer_name = format!("{} {}", row.first_name, row.last_name);
        let customer_email = row.email.unwrap_or_default();
        let mut stripe_customer_id = row.stripe_customer_id.unwrap_or_default();

        // Check if stripe_customer_id exists and is not empty
        let has_stripe_customer = !stripe_customer_id.trim().is_empty();

        println!("has_stripe_customer: {:?}", has_stripe_customer);
        println!("customer_name: {:?}", customer_name);
        println!("customer_email: {:?}", customer_email);

        // Create customer if no valid stripe_customer_id exists
        if !has_stripe_customer {
            let stripe_customer = app
                .stripe()
                .create_customer(&customer_name, &customer_email)
                .await?;
            println!("Created Stripe customer: {:?}", stripe_customer);

            granite::pg_execute!(
                db = dbcx;
                args = {
                    $signup_advisor_uuid: &input.signup_advisor_uuid,
                    $stripe_customer_id: &stripe_customer.id,
                };
                UPDATE
                    df4l.signup_advisor
                SET
                    stripe_customer_id = $stripe_customer_id
                WHERE
                    signup_advisor_uuid = $signup_advisor_uuid
            )
            .await?;
            stripe_customer_id = stripe_customer.id;
        }

        // Create billing portal link
        let return_url = format!(
            "{}/signup/advisor/{}/billing",
            "https://local.acp7.net:3014", // TODO
            input.signup_advisor_uuid
        );
        // Got it after updating Business name at https://dashboard.stripe.com/test/settings/billing/portal
        // saving it
        // and looking at the events
        // https://dashboard.stripe.com/events/evt_1RWhfbHoa1E2rj0LiNdO0tdf
        let configuration_id = "bpc_1RS0KEHoa1E2rj0Lt6u84gb2";
        //let configuration_id = "bpc_1RWkWRHoa1E2rj0Lc74DQsvm";
        //let configuration_id = "bpc_1RRzrdHoa1E2rj0Ll12lyy2r";

        /*
        [df4l]         "Stripe API returned error status: 400 Bad Request with message: {\n  \"error\": {\n    \"code\": \"resource_missing\",\n    \"doc_url\": \"https://stripe.com/docs/error-codes/resource-missing\",\n    \"message\": \"No such configuration: 'bpc_1RRzrdHoa1E2rj0Ll12lyy2r'; a similar object exists in live mode, but a test mode key was used to make this request.\",\n    \"param\": \"configuration\",\n    \"request_log_url\": \"https://dashboard.stripe.com/test/logs/req_2rPK6v8ZzfI5AF?t=1749158291\",\n    \"type\": \"invalid_request_error\"\n  }\n}\n",
        */
        let portal_session = app
            .stripe()
            .create_billing_portal_link(&stripe_customer_id, &return_url, Some(configuration_id))
            .await?;
        println!(
            "Created Stripe billing portal session: {:?}",
            portal_session
        );

        Ok(Output {
            url: portal_session.url,
        })
    }
}
