#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/verify; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::advisor::{signup_advisor_verify_get, signup_advisor_wizard_data};
        use approck::html;

        let wizard_data = signup_advisor_wizard_data::call(
            app,
            identity,
            signup_advisor_wizard_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let verify_data = signup_advisor_verify_get::call(
            app,
            identity,
            signup_advisor_verify_get::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        #[rustfmt::skip]
        let mut phone_verification = bux::component::verification_code();
        phone_verification.set_title("Verify your Phone");
        phone_verification.set_description("Enter the 4-digit code sent to your phone ending in:");
        phone_verification.set_phone_ending(&verify_data.phone_masked);
        phone_verification.set_code_length(4);
        phone_verification.set_resend_link("#");

        let mut email_verification = bux::component::verification_code();
        email_verification.set_title("Verify your Email");
        email_verification
            .set_description("Please enter the 4-digit code sent to your email address:");
        email_verification.set_phone_ending(&verify_data.email_masked);
        email_verification.set_code_length(4);
        email_verification.set_email_link("#");

        #[rustfmt::skip]
        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Verify, wizard_data)?;
            wizard.set_id("verification-form");
            wizard.set_hidden("action", "verify_codes");
            wizard.set_hidden("signup_advisor_uuid", path.signup_advisor_uuid);
            wizard.add_body(html! {
                grid-2 {
                    (phone_verification)
                    (email_verification)
                }
            });
            wizard
        };

        doc.set_title("Verification");
        doc.add_body(html!((wizard)));
        Ok(Response::HTML(doc.into()))
    }
}
