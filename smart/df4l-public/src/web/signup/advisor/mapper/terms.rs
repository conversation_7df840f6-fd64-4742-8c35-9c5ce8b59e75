#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/terms; AUTH None; return HTML|Redirect;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        use crate::api::signup::advisor::signup_advisor_terms_get;
        use crate::api::signup::advisor::signup_advisor_wizard_data;

        let terms_data = signup_advisor_terms_get::call(
            app,
            identity,
            signup_advisor_terms_get::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let wizard_data = signup_advisor_wizard_data::call(
            app,
            identity,
            signup_advisor_wizard_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Terms, wizard_data)?;

        wizard.set_id("terms-form");
        wizard.set_hidden("action", "accept_terms");
        wizard.set_hidden("signup_advisor_uuid", path.signup_advisor_uuid);
        wizard.set_hidden("document_uuid", terms_data.document_uuid.to_string());
        wizard.set_hidden("revision", terms_data.revision);
        wizard.add_heading("📄 Terms and Conditions");
        wizard.add_description("Please review and accept the terms and conditions to continue.");
        wizard.add_body(html! {
            div.terms {
                (maud::PreEscaped(terms_data.terms_html))
            }
            div.x-sign {
                div.x-error {}
                (bux::input::text::string::name_label_value("terms_accepted_name", "Enter First & Last Name To Indicate Agreement", terms_data.terms_accepted_name.as_deref()))

            }
        });

        doc.set_title("Terms and Conditions");
        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}
