// -------------------------------------------------------------------------------------------------
// 1. Import Components
import "@bux/component/verification_code.mts";
import "@bux/component/form_wizard.mts";
import "./verify.mcss";
import "@bux/input/text/string.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SE, SEC } from "@granite/lib.mts";
import { signup_advisor_verify_set } from "@crate/api/signup/advisorλ.mts";
import FormWizard from "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SE(document, "form.bux-form-wizard") as HTMLFormElement;
const $signup_advisor_uuid: HTMLInputElement = SE($form, "[name=signup_advisor_uuid]");

// Get all verification code inputs - they are in a grid-2 layout
const $verification_components = Array.from(
    $form.querySelectorAll("bux-component-verification-code"),
) as HTMLElement[];
const $phone_code_inputs = Array.from(
    $verification_components[0]?.querySelectorAll("input.code-digit") || [],
) as HTMLInputElement[];
const $email_code_inputs = Array.from(
    $verification_components[1]?.querySelectorAll("input.code-digit") || [],
) as HTMLInputElement[];

const signup_advisor_uuid = $signup_advisor_uuid.value;

const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

$next_button.addEventListener("click", (event) => {
    event.preventDefault();
    $form.dispatchEvent(new Event("submit"));
});

// -------------------------------------------------------------------------------------------------
// 5. Write Code

new FormWizard({
    $form,
    api: signup_advisor_verify_set.api,

    err: (errors) => {
        // Handle validation errors for verification codes
        console.log("Verification errors:", errors);
    },

    get: () => {
        // Collect email verification code
        const email_code = $email_code_inputs.map((input) => input.value).join("");
        // Collect phone verification code
        const phone_code = $phone_code_inputs.map((input) => input.value).join("");

        return {
            signup_advisor_uuid: signup_advisor_uuid,
            email_code: email_code.length === 4 ? { Some: email_code } : undefined,
            phone_code: phone_code.length === 4 ? { Some: phone_code } : undefined,
        };
    },

    set: (_value) => {
    },

    out: (_output) => {
        window.location.href = $next_button.href;
    },
});
