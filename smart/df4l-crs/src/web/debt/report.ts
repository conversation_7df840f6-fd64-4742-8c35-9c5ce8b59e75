import "./report.mcss";
import "@bux/input/text/string.mts";

import { SE } from "@granite/lib.mts";
import { FormPanel } from "@bux/component/form_panel.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import { report } from "@crate/api/debtλ.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $middle_name: BuxInputTextString = SE($form, "[name=middle_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $address: BuxInputTextString = SE($form, "[name=address]");
const $city: BuxInputTextString = SE($form, "[name=city]");
const $state: BuxInputTextString = SE($form, "[name=state]");
const $zip: BuxInputTextString = SE($form, "[name=zip]");
const $ssn: BuxInputTextString = SE($form, "[name=ssn]");
const $dob: BuxInputTextString = SE($form, "[name=dob]");

new FormPanel({
    $form,
    api: report.api,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $middle_name.set_e(errors.middle_name);
        $last_name.set_e(errors.last_name);
        $address.set_e(errors.address);
        $city.set_e(errors.city);
        $state.set_e(errors.state);
        $zip.set_e(errors.zip);
        $ssn.set_e(errors.ssn);
        $dob.set_e(errors.dob);
    },

    get: () => {
        return {
            first_name: $first_name.value,
            middle_name: $middle_name.value,
            last_name: $last_name.value,
            address: $address.value,
            city: $city.value,
            state: $state.value,
            zip: $zip.value,
            ssn: $ssn.value,
            dob: $dob.value,
        };
    },

    set: (value) => {
        $first_name.value = value.first_name;
        $middle_name.value = value.middle_name;
        $last_name.value = value.last_name;
        $address.value = value.address;
        $city.value = value.city;
        $state.value = value.state;
        $zip.value = value.zip;
        $ssn.value = value.ssn;
        $dob.value = value.dob;
    },

    out: (output) => {
        // Handle the debt report output - could redirect to a results page
        // or display the results on the same page
        console.log("Debt report received:", output);

        // For now, we'll just log the results
        // In a real implementation, you might want to:
        // 1. Redirect to a results page
        // 2. Display results in a modal
        // 3. Update the page content with the results

        if (output.debts && output.debts.length > 0) {
            alert(`Found ${output.debts.length} debt accounts across all credit bureaus.`);
        } else {
            alert("No debt accounts found.");
        }
    },
});
