#[approck::http(GET /debt/report; AUTH None; return HTML;)]
pub mod page {
    use maud::html;

    pub async fn request(_app: App, _identity: Identity, doc: Document) -> Result<Response> {
        doc.set_title("Unified Debt Report");

        let mut form_panel = bux::component::add_cancel_form_panel("Unified Debt Report", "/");

        // Change the submit button label to "Get Debt Report"
        form_panel.submit_label = "Get Debt Report".to_string();

        form_panel.add_body(html!(
            p { "View your consolidated debt information from all three credit bureaus." }
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("first_name", "First Name", Some("<PERSON>")))
                    (bux::input::text::string::name_label_value("last_name", "Last Name", Some("Doe")))
                    (bux::input::text::string::name_label_value("address", "Address", Some("123 Main St")))
                    (bux::input::text::string::name_label_value("city", "City", Some("Anytown")))
                    (bux::input::text::string::name_label_value("state", "State", Some("CA")))
                }
                cell-6 {
                    (bux::input::text::string::name_label_value("middle_name", "Middle Name", Some("Middle")))
                    (bux::input::text::string::name_label_value("zip", "ZIP Code", Some("12345")))
                    (bux::input::text::string::name_label_value("ssn", "SSN", Some("***********")))
                    (bux::input::text::string::name_label_value("dob", "Date of Birth (YYYY-MM-DD)", Some("1990-01-01")))
                }
            }
        ));

        doc.add_body(html!(
            section {
                grid-row {
                    col-offset-eight {
                        (form_panel)
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
