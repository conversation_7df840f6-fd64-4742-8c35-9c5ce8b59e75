#[approck::api]
pub mod credit_report {
    use crate::api::debt::report::UnifiedDebt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub first_name: String,
        pub last_name: String,
        pub ssn: String,
        pub dob: String, // Format: YYYY-MM-DD
        pub house_number: String,
        pub street_name: String,
        pub city: String,
        pub state: String,
        pub zip: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub data: TransUnionData,
    }

    #[granite::gtype(ApiOutput)]
    pub struct TransUnionData {
        pub custom: TransUnionCustom,
    }

    #[granite::gtype(ApiOutput)]
    pub struct TransUnionCustom {
        pub trades: Vec<TransUnionTradeItem>,
        pub collections: Vec<TransUnionCollectionAccount>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct TransUnionCustomCreditTrade {
        pub subscriber: TransUnionSubscriber,
        #[serde(rename = "")]
        pub account_number: String,
        pub current_balance: String,
        pub past_due: String,
        pub terms: Option<TransUnionTerms>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct TransUnionTradeItem {
        #[serde(rename = "subscriber")]
        pub subscriber: TransUnionSubscriber,
        #[serde(rename = "accountNumber")]
        pub account_number: String,
        #[serde(rename = "currentBalance")]
        pub current_balance: String,
        #[serde(rename = "pastDue")]
        pub past_due: String,
        #[serde(rename = "terms")]
        pub terms: Option<TransUnionTerms>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct TransUnionTerms {
        #[serde(rename = "scheduledMonthlyPayment")]
        pub scheduled_monthly_payment: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct TransUnionSubscriber {
        #[serde(rename = "memberCode")]
        pub member_code: String,
        #[serde(rename = "name")]
        pub name: TransUnionSubscriberName,
    }

    #[granite::gtype(ApiOutput)]
    pub struct TransUnionSubscriberName {
        #[serde(rename = "unparsed")]
        pub unparsed: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct TransUnionCollectionAccount {
        pub subscriber: TransUnionSubscriber,
        pub account_number: String,
        pub current_balance: String,
    }

    impl From<TransUnionTradeItem> for UnifiedDebt {
        fn from(tradeline: TransUnionTradeItem) -> Self {
            UnifiedDebt {
                consumer_id: tradeline.subscriber.member_code.clone(),
                debt_id: tradeline.account_number.clone(),
                name: tradeline.subscriber.name.unparsed.clone(),
                balance: tradeline.current_balance.parse::<i64>().ok(),
                interest_rate: None,
                minimum_payment: tradeline
                    .terms
                    .and_then(|t| t.scheduled_monthly_payment)
                    .and_then(|s| s.parse::<granite::Decimal>().ok()),
                source: "TransUnion".to_string(),
            }
        }
    }

    impl From<TransUnionCollectionAccount> for UnifiedDebt {
        fn from(account: TransUnionCollectionAccount) -> Self {
            UnifiedDebt {
                consumer_id: account.subscriber.member_code.clone(),
                debt_id: account.account_number.clone(),
                name: account.subscriber.name.unparsed.clone(),
                balance: account.current_balance.parse::<i64>().ok(),
                interest_rate: None,
                minimum_payment: None,
                source: "TransUnion".to_string(),
            }
        }
    }

    pub async fn call(_app: App, _identity: Identity, _input: Input) -> Result<Output> {
        // TODO: Check permissions
        // TODO: Implement actual TransUnion API call
        let _method = "POST";
        let _uri = "/transunion/credit-report/basic/{config}";
        let _auth_type = "Bearer Token"; // Auth: Bearer Token

        // Load the sample JSON response
        let json_data = include_str!("docs/transunion-prequal-vantage4.json");

        // Parse the JSON data
        let parsed_data: serde_json::Value = serde_json::from_str(json_data)?;

        // Extract trades and collections from the JSON
        let mut trades = Vec::new();
        let mut collections = Vec::new();

        if let Some(custom_data) = parsed_data.get("custom").and_then(|c| c.as_object()) {
            if let Some(trades_data) = custom_data
                .get("credit")
                .and_then(|c| c.get("trades").and_then(|t| t.as_array()))
            {
                for trade in trades_data {
                    if let (
                        Some(account_number),
                        Some(subscriber),
                        Some(current_balance),
                        Some(past_due),
                    ) = (
                        trade.get("accountNumber").and_then(|a| a.as_str()),
                        trade.get("subscriber").and_then(|s| s.as_object()),
                        trade.get("currentBalance").and_then(|b| b.as_str()),
                        trade.get("pastDue").and_then(|p| p.as_str()),
                    ) {
                        if let (Some(member_code), Some(name)) = (
                            subscriber.get("memberCode").and_then(|m| m.as_str()),
                            subscriber
                                .get("name")
                                .and_then(|n| n.as_object())
                                .and_then(|n| n.get("unparsed").and_then(|u| u.as_str())),
                        ) {
                            trades.push(TransUnionTradeItem {
                                subscriber: TransUnionSubscriber {
                                    member_code: member_code.to_string(),
                                    name: TransUnionSubscriberName {
                                        unparsed: name.to_string(),
                                    },
                                },
                                account_number: account_number.to_string(),
                                current_balance: current_balance.to_string(),
                                past_due: past_due.to_string(),
                                terms: None,
                            });
                        }
                    }
                }
            }

            if let Some(collections_data) =
                custom_data.get("collections").and_then(|c| c.as_array())
            {
                for collection in collections_data {
                    if let (Some(account_number), Some(subscriber), Some(current_balance)) = (
                        collection.get("accountNumber").and_then(|a| a.as_str()),
                        collection.get("subscriber").and_then(|s| s.as_object()),
                        collection.get("currentBalance").and_then(|b| b.as_str()),
                    ) {
                        if let (Some(member_code), Some(name)) = (
                            subscriber.get("memberCode").and_then(|m| m.as_str()),
                            subscriber
                                .get("name")
                                .and_then(|n| n.as_object())
                                .and_then(|n| n.get("unparsed").and_then(|u| u.as_str())),
                        ) {
                            collections.push(TransUnionCollectionAccount {
                                subscriber: TransUnionSubscriber {
                                    member_code: member_code.to_string(),
                                    name: TransUnionSubscriberName {
                                        unparsed: name.to_string(),
                                    },
                                },
                                account_number: account_number.to_string(),
                                current_balance: current_balance.to_string(),
                            });
                        }
                    }
                }
            }
        }

        let response = Output {
            data: TransUnionData {
                custom: TransUnionCustom {
                    trades,
                    collections,
                },
            },
        };

        Ok(response)
    }
}
