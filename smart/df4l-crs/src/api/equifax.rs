#[approck::api]
pub mod credit_report {
    use crate::api::debt::report::UnifiedDebt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub first_name: String,
        pub last_name: String,
        pub ssn: String,
        pub dob: String, // Format: YYYY-MM-DD
        pub house_number: String,
        pub street_name: String,
        pub city: String,
        pub state: String,
        pub zip: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub data: EquifaxCreditData,
    }

    #[granite::gtype(ApiOutput)]
    pub struct EquifaxCreditData {
        #[serde(rename = "trades")]
        pub trades: Vec<EquifaxTradeItem>,
        #[serde(rename = "collections")]
        pub collections: Vec<EquifaxCollectionItem>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct EquifaxTradeItem {
        #[serde(rename = "accountNumber")]
        pub account_number: String,
        #[serde(rename = "customerName")]
        pub customer_name: String,
        #[serde(rename = "customerNumber")]
        pub customer_number: String,
        #[serde(rename = "balance")]
        pub balance: String,
        #[serde(rename = "actualPaymentAmount")]
        pub actual_payment_amount: Option<String>,
        #[serde(rename = "scheduledMonthlyPayment")]
        pub scheduled_monthly_payment: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct EquifaxCollectionItem {
        #[serde(rename = "accountNumber")]
        pub account_number: String,
        #[serde(rename = "customerName")]
        pub customer_name: String,
        #[serde(rename = "customerNumber")]
        pub customer_number: String,
        #[serde(rename = "balanceAmount")]
        pub balance_amount: String,
    }

    impl From<EquifaxTradeItem> for UnifiedDebt {
        fn from(trade: EquifaxTradeItem) -> Self {
            UnifiedDebt {
                consumer_id: trade.customer_number.clone(),
                debt_id: trade.account_number.clone(),
                name: trade.customer_name.clone(),
                balance: trade.balance.parse::<i64>().ok(),
                interest_rate: None,
                minimum_payment: trade
                    .scheduled_monthly_payment
                    .and_then(|s| s.parse::<granite::Decimal>().ok()),
                source: "Equifax".to_string(),
            }
        }
    }

    impl From<EquifaxCollectionItem> for UnifiedDebt {
        fn from(collection: EquifaxCollectionItem) -> Self {
            UnifiedDebt {
                consumer_id: collection.customer_number.clone(),
                debt_id: collection.account_number.clone(),
                name: collection.customer_name.clone(),
                balance: collection.balance_amount.parse::<i64>().ok(),
                interest_rate: None,
                minimum_payment: None,
                source: "Equifax".to_string(),
            }
        }
    }

    pub async fn call(_app: App, _identity: Identity, _input: Input) -> Result<Output> {
        // TODO: Check permissions
        // TODO: Implement actual Equifax API call
        let _method = "POST";
        let _uri = "/equifax/credit-report/{option}";
        let _auth_type = "Bearer Token"; // Auth: Bearer Token

        // Load the sample JSON response
        let json_data = include_str!("docs/equifax-prequal-fico9.json");

        // Parse the JSON data
        let parsed_data: serde_json::Value = serde_json::from_str(json_data)?;

        // Extract trades and collections from the JSON
        let mut trades = Vec::new();
        let mut collections = Vec::new();

        // Extract trade data if available
        if let Some(data) = parsed_data.get("data") {
            if let Some(trades_data) = data.get("trades").and_then(|t| t.as_array()) {
                for trade in trades_data {
                    if let (
                        Some(account_number),
                        Some(customer_name),
                        Some(customer_number),
                        Some(balance),
                    ) = (
                        trade.get("accountNumber").and_then(|a| a.as_str()),
                        trade.get("customerName").and_then(|c| c.as_str()),
                        trade.get("customerNumber").and_then(|c| c.as_str()),
                        trade.get("balanceAmount").and_then(|b| b.as_str()),
                    ) {
                        let scheduled_payment = trade
                            .get("scheduledMonthlyPayment")
                            .and_then(|p| p.as_str())
                            .map(|s| s.to_string());

                        let actual_payment = trade
                            .get("actualPaymentAmount")
                            .and_then(|p| p.as_str())
                            .map(|s| s.to_string());

                        trades.push(EquifaxTradeItem {
                            account_number: account_number.to_string(),
                            customer_name: customer_name.to_string(),
                            customer_number: customer_number.to_string(),
                            balance: balance.to_string(),
                            scheduled_monthly_payment: scheduled_payment,
                            actual_payment_amount: actual_payment,
                        });
                    }
                }
            }

            // Extract collection data if available
            if let Some(collections_data) = data.get("collections").and_then(|c| c.as_array()) {
                for collection in collections_data {
                    if let (
                        Some(account_number),
                        Some(customer_name),
                        Some(customer_number),
                        Some(balance),
                    ) = (
                        collection.get("accountNumber").and_then(|a| a.as_str()),
                        collection.get("customerName").and_then(|c| c.as_str()),
                        collection.get("customerNumber").and_then(|c| c.as_str()),
                        collection.get("balanceAmount").and_then(|b| b.as_str()),
                    ) {
                        collections.push(EquifaxCollectionItem {
                            account_number: account_number.to_string(),
                            customer_name: customer_name.to_string(),
                            customer_number: customer_number.to_string(),
                            balance_amount: balance.to_string(),
                        });
                    }
                }
            }
        }

        let response = Output {
            data: EquifaxCreditData {
                trades,
                collections,
            },
        };

        Ok(response)
    }
}
