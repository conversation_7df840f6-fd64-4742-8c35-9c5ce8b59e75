// create a function that takes customer information (name, address, dob) and returns a unified debt report from the three credit bureau functions
#[approck::api]
pub mod report {
    use crate::api::{equifax, experian, transunion};

    #[derive(Clone)]
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub first_name: String,
        pub middle_name: String,
        pub last_name: String,
        pub address: String,
        pub city: String,
        pub state: String,
        pub zip: String,
        pub ssn: String,
        pub dob: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub debts: Vec<UnifiedDebt>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct UnifiedDebt {
        pub consumer_id: String,
        pub debt_id: String,
        pub name: String,
        pub balance: Option<i64>,
        pub interest_rate: Option<Decimal>,
        pub minimum_payment: Option<Decimal>,
        pub source: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut debts = Vec::new();

        // TODO: check permissions
        match equifax::credit_report::call(
            app,
            identity,
            equifax::credit_report::Input {
                first_name: input.first_name.clone(),
                last_name: input.last_name.clone(),
                ssn: input.ssn.clone(),
                dob: input.dob.clone(),
                house_number: input.address.clone(),
                street_name: input.address.clone(),
                city: input.city.clone(),
                state: input.state.clone(),
                zip: input.zip.clone(),
            },
        )
        .await
        {
            Ok(r) => {
                let mut data: Vec<UnifiedDebt> =
                    r.data.trades.into_iter().map(|t| t.into()).collect();
                data.extend(r.data.collections.into_iter().map(|c| c.into()));
                debts.extend(data);
            }
            Err(e) => {
                approck::error!("Failed to get Equifax data: {e}");
                return Err(e);
            }
        };

        match experian::credit_report::call(
            app,
            identity,
            experian::credit_report::Input {
                first_name: input.first_name.clone(),
                last_name: input.last_name.clone(),
                ssn: input.ssn.clone(),
                dob: input.dob.clone(),
                house_number: input.address.clone(),
                street_name: input.address.clone(),
                city: input.city.clone(),
                state: input.state.clone(),
                zip: input.zip.clone(),
            },
        )
        .await
        {
            Ok(r) => {
                let data: Vec<UnifiedDebt> =
                    r.data.tradeline.into_iter().map(|t| t.into()).collect();
                debts.extend(data);
            }
            Err(e) => {
                approck::error!("Failed to get Experian data: {e}");
                return Err(e);
            }
        };

        match transunion::credit_report::call(
            app,
            identity,
            transunion::credit_report::Input {
                first_name: input.first_name.clone(),
                last_name: input.last_name.clone(),
                ssn: input.ssn.clone(),
                dob: input.dob.clone(),
                house_number: input.address.clone(),
                street_name: input.address.clone(),
                city: input.city.clone(),
                state: input.state.clone(),
                zip: input.zip.clone(),
            },
        )
        .await
        {
            Ok(r) => {
                let mut data: Vec<UnifiedDebt> =
                    r.data.custom.trades.into_iter().map(|a| a.into()).collect();
                data.extend(r.data.custom.collections.into_iter().map(|c| c.into()));
                debts.extend(data);
            }
            Err(e) => {
                approck::error!("Failed to get TransUnion data: {e}");
                return Err(e);
            }
        };

        println!("Debts: {:#?}", debts);

        Ok(Response::Output(Output { debts }))
    }
}
