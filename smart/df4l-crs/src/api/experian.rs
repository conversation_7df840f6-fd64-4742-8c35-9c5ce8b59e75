#[approck::api]
pub mod credit_report {
    use crate::api::debt::report::UnifiedDebt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub first_name: String,
        pub last_name: String,
        pub ssn: String,
        pub dob: String, // Format: YYYY-MM-DD
        pub house_number: String,
        pub street_name: String,
        pub city: String,
        pub state: String,
        pub zip: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub data: ExperianCreditData,
    }

    #[granite::gtype(ApiOutput)]
    pub struct ExperianCreditData {
        pub tradeline: Vec<ExperianTradeItem>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct ExperianTradeItem {
        #[serde(rename = "accountNumber")]
        pub account_number: String,
        #[serde(rename = "subscriberCode")]
        pub subscriber_code: String,
        #[serde(rename = "subscriberName")]
        pub subscriber_name: String,
        #[serde(rename = "balanceAmount")]
        pub balance_amount: String,
        #[serde(rename = "balanceDate")]
        pub balance_date: Option<String>,
        #[serde(rename = "monthlyPaymentAmount")]
        pub monthly_payment: String,
    }

    impl From<ExperianTradeItem> for UnifiedDebt {
        fn from(tradeline: ExperianTradeItem) -> Self {
            UnifiedDebt {
                consumer_id: tradeline.subscriber_code.clone(),
                debt_id: tradeline.account_number.clone(),
                name: tradeline.subscriber_name.clone(),
                balance: tradeline.balance_amount.parse::<i64>().ok(),
                interest_rate: None,
                minimum_payment: tradeline.monthly_payment.parse::<granite::Decimal>().ok(),
                source: "Experian".to_string(),
            }
        }
    }

    pub async fn call(_app: App, _identity: Identity, _input: Input) -> Result<Output> {
        // TODO: Check permissions
        // TODO: Implement actual Experian API call
        let _method = "POST";
        let _uri = "/experian/credit-profile/credit-report/basic/{config}";
        let _auth_type = "Bearer Token"; // Auth: Bearer Token

        // Load the sample JSON response
        let json_data = include_str!("docs/experian-prequal-fico9.json");

        // Parse the JSON data
        let parsed_data: serde_json::Value = serde_json::from_str(json_data)?;

        // Extract tradelines from the JSON
        let mut tradelines = Vec::new();

        // Extract tradeline data if available
        if let Some(tradeline_data) = parsed_data.get("tradeline").and_then(|t| t.as_array()) {
            for tradeline in tradeline_data {
                if let (Some(account_number), Some(subscriber_code), Some(subscriber_name)) = (
                    tradeline.get("accountNumber").and_then(|a| a.as_str()),
                    tradeline.get("subscriberCode").and_then(|s| s.as_str()),
                    tradeline.get("subscriberName").and_then(|s| s.as_str()),
                ) {
                    let balance_amount = tradeline
                        .get("balanceAmount")
                        .and_then(|b| b.as_str())
                        .or_else(|| tradeline.get("amount1").and_then(|a| a.as_str()))
                        .unwrap_or("0")
                        .to_string();

                    let balance_date = tradeline
                        .get("balanceDate")
                        .and_then(|d| d.as_str())
                        .map(|s| s.to_string());

                    let monthly_payment = tradeline
                        .get("monthlyPaymentAmount")
                        .and_then(|p| p.as_str())
                        .unwrap_or("0")
                        .to_string();

                    tradelines.push(ExperianTradeItem {
                        account_number: account_number.to_string(),
                        subscriber_code: subscriber_code.to_string(),
                        subscriber_name: subscriber_name.to_string(),
                        balance_amount,
                        balance_date,
                        monthly_payment,
                    });
                }
            }
        }

        let response = Output {
            data: ExperianCreditData {
                tradeline: tradelines,
            },
        };

        Ok(response)
    }
}
