{"pdfReportId": "58ea7f4e-1cc1-2d57-668e-85ee9c2b67e9", "data": {"identifier": "Individual Report 1", "customerReferenceNumber": "1688067456761", "customerNumber": "180ZB10171", "consumerReferralCode": "062", "multipleReportIndicator": "1", "ECOAInquiryType": "I", "hitCode": {"code": "1", "description": "Hit"}, "fileSinceDate": "12131999", "lastActivityDate": "********", "reportDate": "06292023", "subjectName": {"firstName": "PATRICIA", "lastName": "EERAT", "middleName": "K"}, "subjectSocialNum": "*********", "birthDate": "********", "doNotCombineIndicator": "C", "addressDiscrepancyIndicator": "N", "addresses": [{"addressType": "current", "houseNumber": "9226", "streetName": "NTRQVBZT", "streetType": "DR", "cityName": "DURHAM", "stateAbbreviation": "NC", "zipCode": "27712", "sourceOfAddress": {"code": "T", "description": "AUT**"}, "addressLine1": "9226 NTRQVBZT DR", "dateFirstReported": "********", "dateLastReported": "********"}], "trades": [{"monthsReviewed": "48", "accountDesignator": {"code": "I", "description": "Individual Account"}, "accountNumber": "3212TEST45000", "customerName": "CHOICE COMMUNITY CRE", "customerNumber": "815FC06814", "dateReported": "********", "dateOpened": "********", "highCredit": 8167, "balance": 0, "portfolioTypeCode": {"code": "I", "description": "Installment (fixed number of payments)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "042014", "narrativeCodes": [{"code": "FA", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}], "rawNarrativeCodes": ["FA"], "activityDesignatorCode": {"code": "B", "description": "Paid and Closed"}}, {"monthsReviewed": "52", "accountDesignator": {"code": "J", "description": "Joint Account"}, "accountNumber": "1744TEST7000", "customerName": "CHASE", "customerNumber": "416FM02092", "dateReported": "********", "dateOpened": "********", "highCredit": 84400, "balance": 0, "portfolioTypeCode": {"code": "I", "description": "Installment (fixed number of payments)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "042017", "narrativeCodes": [{"code": "IF", "description": "FREDDIE MAC ACCOUNT"}], "rawNarrativeCodes": ["IF"], "activityDesignatorCode": {"code": "B", "description": "Paid and Closed"}}, {"monthsReviewed": "29", "accountDesignator": {"code": "I", "description": "Individual Account"}, "accountNumber": "416TEST15000", "customerName": "MACYS/CITIBANK, N.A.", "customerNumber": "636DC26977", "dateReported": "********", "dateOpened": "********", "balance": 0, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "102020", "narrativeCodes": [{"code": "AV", "description": "CHARGE"}], "rawNarrativeCodes": ["AV"], "accountTypeCode": {"code": "07", "description": "Charge Account"}, "lastPaymentDate": "********"}, {"monthsReviewed": "01", "accountDesignator": {"code": "I", "description": "Individual Account"}, "accountNumber": "000099TEST2053000", "customerName": "AMERICAN EXPRESS", "customerNumber": "402BB48257", "dateReported": "********", "dateOpened": "********", "highCredit": 0, "creditLimit": 15700, "balance": 0, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "narrativeCodes": [{"code": "FE", "description": "CREDIT CARD"}, {"code": "AZ", "description": "AMOUNT IN H/C COLUMN IS CREDIT LIMIT"}], "rawNarrativeCodes": ["FE", "AZ"], "accountTypeCode": {"code": "18", "description": "Credit Card"}}, {"automatedUpdateIndicator": "*", "monthsReviewed": "01", "accountDesignator": {"code": "I", "description": "Individual Account"}, "accountNumber": "*************", "customerName": "FIA CSNA", "customerNumber": "458ON14382", "dateReported": "********", "dateOpened": "********", "highCredit": 1813, "balance": 1813, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "052023", "narrativeCodes": [{"code": "JX", "description": "FLEXIBLE SPENDING CREDIT CARD"}], "rawNarrativeCodes": ["JX"], "accountTypeCode": {"code": "0G", "description": "Flexible Spending Credit Card"}, "scheduledPaymentAmount": 18, "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}}, {"automatedUpdateIndicator": "*", "monthsReviewed": "39", "accountDesignator": {"code": "J", "description": "Joint Account"}, "accountNumber": "*************", "customerName": "WELLS FARGO BANK, NA", "customerNumber": "805BB87407", "dateReported": "********", "dateOpened": "********", "highCredit": 24025, "creditLimit": 109200, "balance": 0, "portfolioTypeCode": {"code": "C", "description": "Line of Credit (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "012021", "narrativeCodes": [{"code": "EC", "description": "HOME EQUITY"}, {"code": "CV", "description": "LINE OF CREDIT"}], "rawNarrativeCodes": ["EC", "CV"], "accountTypeCode": {"code": "89", "description": "Home Equity Line of Credit"}, "lastPaymentDate": "********", "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}, "paymentHistory1to24": [{"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "/"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}]}, {"automatedUpdateIndicator": "*", "monthsReviewed": "36", "accountDesignator": {"code": "A", "description": "Authorized User"}, "accountNumber": "*************", "customerName": "BANK OF AMERICA", "customerNumber": "416BB01228", "dateReported": "********", "dateOpened": "********", "highCredit": 8423, "creditLimit": 9900, "balance": 586, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "052023", "narrativeCodes": [{"code": "FE", "description": "CREDIT CARD"}, {"code": "AZ", "description": "AMOUNT IN H/C COLUMN IS CREDIT LIMIT"}], "rawNarrativeCodes": ["FE", "AZ"], "accountTypeCode": {"code": "18", "description": "Credit Card"}, "lastPaymentDate": "********", "actualPaymentAmount": 1315, "scheduledPaymentAmount": 15, "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}, "paymentHistory1to24": [{"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "/"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}]}, {"automatedUpdateIndicator": "*", "monthsReviewed": "99", "accountDesignator": {"code": "I", "description": "Individual Account"}, "accountNumber": "1887000", "customerName": "KOHLS/CHASE", "customerNumber": "668DC04557", "dateReported": "********", "dateOpened": "********", "highCredit": 316, "creditLimit": 3000, "balance": 9, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "052023", "narrativeCodes": [{"code": "FE", "description": "CREDIT CARD"}, {"code": "AZ", "description": "AMOUNT IN H/C COLUMN IS CREDIT LIMIT"}], "rawNarrativeCodes": ["FE", "AZ"], "accountTypeCode": {"code": "18", "description": "Credit Card"}, "lastPaymentDate": "********", "actualPaymentAmount": 58, "scheduledPaymentAmount": 5, "paymentHistory1to24": [{"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "/"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}]}, {"automatedUpdateIndicator": "*", "monthsReviewed": "84", "accountDesignator": {"code": "I", "description": "Individual Account"}, "accountNumber": "*************", "customerName": "BANK OF AMERICA", "customerNumber": "416BB01228", "dateReported": "********", "dateOpened": "********", "highCredit": 12484, "creditLimit": 19200, "balance": 1176, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "052023", "narrativeCodes": [{"code": "FE", "description": "CREDIT CARD"}, {"code": "AZ", "description": "AMOUNT IN H/C COLUMN IS CREDIT LIMIT"}], "rawNarrativeCodes": ["FE", "AZ"], "accountTypeCode": {"code": "18", "description": "Credit Card"}, "lastPaymentDate": "********", "actualPaymentAmount": 1236, "scheduledPaymentAmount": 15, "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}, "paymentHistory1to24": [{"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "/"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}]}, {"automatedUpdateIndicator": "*", "monthsReviewed": "28", "accountDesignator": {"code": "A", "description": "Authorized User"}, "accountNumber": "*********", "customerName": "SEARS/CBNA", "customerNumber": "645DC09286", "dateReported": "********", "dateOpened": "********", "highCredit": 1885, "creditLimit": 7200, "balance": 0, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "042023", "narrativeCodes": [{"code": "AV", "description": "CHARGE"}, {"code": "AZ", "description": "AMOUNT IN H/C COLUMN IS CREDIT LIMIT"}], "rawNarrativeCodes": ["AV", "AZ"], "accountTypeCode": {"code": "07", "description": "Charge Account"}, "lastPaymentDate": "********", "actualPaymentAmount": 1842, "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}, "paymentHistory1to24": [{"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "/"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}]}, {"automatedUpdateIndicator": "*", "monthsReviewed": "47", "accountDesignator": {"code": "I", "description": "Individual Account"}, "accountNumber": "*********", "customerName": "SYNCB/SAMS CLUB PLCC", "customerNumber": "404FZ01044", "dateReported": "********", "dateOpened": "********", "highCredit": 485, "creditLimit": 1250, "balance": 0, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "012023", "narrativeCodes": [{"code": "AV", "description": "CHARGE"}, {"code": "AZ", "description": "AMOUNT IN H/C COLUMN IS CREDIT LIMIT"}], "rawNarrativeCodes": ["AV", "AZ"], "accountTypeCode": {"code": "07", "description": "Charge Account"}, "lastPaymentDate": "********", "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}, "paymentHistory1to24": [{"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "/"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}]}, {"automatedUpdateIndicator": "*", "monthsReviewed": "69", "accountDesignator": {"code": "I", "description": "Individual Account"}, "accountNumber": "*************", "customerName": "BANK OF AMERICA", "customerNumber": "801ON00119", "dateReported": "********", "dateOpened": "********", "highCredit": 0, "creditLimit": 13000, "balance": 0, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "042023", "narrativeCodes": [{"code": "FE", "description": "CREDIT CARD"}, {"code": "AZ", "description": "AMOUNT IN H/C COLUMN IS CREDIT LIMIT"}], "rawNarrativeCodes": ["FE", "AZ"], "accountTypeCode": {"code": "18", "description": "Credit Card"}, "lastPaymentDate": "********", "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}}, {"automatedUpdateIndicator": "*", "monthsReviewed": "99", "accountDesignator": {"code": "A", "description": "Authorized User"}, "accountNumber": "************", "customerName": "BANK OF AMERICA", "customerNumber": "801ON00119", "dateReported": "********", "dateOpened": "********", "highCredit": 152, "creditLimit": 30000, "balance": 0, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "032023", "narrativeCodes": [{"code": "FE", "description": "CREDIT CARD"}, {"code": "AZ", "description": "AMOUNT IN H/C COLUMN IS CREDIT LIMIT"}], "rawNarrativeCodes": ["FE", "AZ"], "accountTypeCode": {"code": "18", "description": "Credit Card"}, "lastPaymentDate": "********", "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}}, {"automatedUpdateIndicator": "*", "monthsReviewed": "16", "accountDesignator": {"code": "J", "description": "Joint Account"}, "accountNumber": "***********", "customerName": "SUN TRUST FSB", "customerNumber": "484BB00920", "dateReported": "********", "dateOpened": "********", "highCredit": 15, "creditLimit": 15000, "balance": 0, "portfolioTypeCode": {"code": "C", "description": "Line of Credit (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "102021", "narrativeCodes": [{"code": "FA", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}, {"code": "CV", "description": "LINE OF CREDIT"}], "rawNarrativeCodes": ["FA", "CV"], "accountTypeCode": {"code": "15", "description": "Line of Credit"}, "lastPaymentDate": "********", "closedDate": "********", "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}, "activityDesignatorCode": {"code": "B", "description": "Paid and Closed"}, "paymentHistory1to24": [{"code": "1", "description": "Pays account as agreed"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "/"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "E", "description": "Zero balance and current account"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}]}, {"automatedUpdateIndicator": "*", "monthsReviewed": "23", "accountDesignator": {"code": "J", "description": "Joint Account"}, "accountNumber": "*********", "customerName": "CAPITAL ONE", "customerNumber": "850BB01498", "dateReported": "********", "dateOpened": "********", "highCredit": 7763, "balance": 0, "portfolioTypeCode": {"code": "O", "description": "Open Account (entire balance is due upon demand)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "102020", "narrativeCodes": [{"code": "FE", "description": "CREDIT CARD"}], "rawNarrativeCodes": ["FE"], "accountTypeCode": {"code": "18", "description": "Credit Card"}, "lastPaymentDate": "********", "actualPaymentAmount": 173, "termsFrequencyCode": {"code": "M", "description": "Monthly (due every month)"}, "paymentHistory1to24": [{"code": "E", "description": "Zero balance and current account"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "/"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "1", "description": "Pays account as agreed"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": "*", "description": "Rate/Status was not available for that month"}, {"code": " ", "description": "Not populated based on the Date Open(payment history will only be populated for each of the months that the account has been open)"}]}, {"automatedUpdateIndicator": "*", "monthsReviewed": "89", "accountDesignator": {"code": "A", "description": "Authorized User"}, "accountNumber": "*********", "customerName": "CBUSASEARS", "customerNumber": "906BB05601", "dateReported": "********", "dateOpened": "********", "highCredit": 10600, "creditLimit": 10600, "balance": 0, "portfolioTypeCode": {"code": "R", "description": "Revolving (payment amounts based on the outstanding balance)"}, "rate": {"code": 1, "description": "Pays account as agreed"}, "lastActivityDate": "082017", "narrativeCodes": [{"code": "FA", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}, {"code": "CF", "description": "CLOSED ACCOUNT"}], "rawNarrativeCodes": ["FA", "CF"], "accountTypeCode": {"code": "18", "description": "Credit Card"}, "lastPaymentDate": "********", "closedDate": "********", "activityDesignatorCode": {"code": "B", "description": "Paid and Closed"}}], "inquiries": [{"type": "fileInquiry", "industryCode": "FP", "inquiryDate": "********", "customerNumber": "999FP02123", "customerName": "GENESIS FS - MILESTO"}, {"type": "fileInquiry", "industryCode": "FP", "inquiryDate": "********", "customerNumber": "999FP02123", "customerName": "GENESIS FS - MILESTO"}, {"type": "fileInquiry", "industryCode": "FP", "inquiryDate": "********", "customerNumber": "999FP02123", "customerName": "GENESIS FS - MILESTO"}, {"type": "fileInquiry", "industryCode": "FP", "inquiryDate": "********", "customerNumber": "999FP05183", "customerName": "LENDMARK FINANCIAL S"}, {"type": "fileInquiry", "industryCode": "FP", "inquiryDate": "********", "customerNumber": "999FP02123", "customerName": "GENESIS FS - MILESTO"}, {"type": "fileInquiry", "industryCode": "FC", "inquiryDate": "********", "customerNumber": "845FC00043", "customerName": "TYNDALL FEDERAL CR U"}, {"type": "inquiry", "industryCode": "BB", "inquiryDate": "********", "customerNumber": "942BB01200", "customerName": "COLONY BANK"}, {"type": "inquiry", "industryCode": "ZZ", "inquiryDate": "********", "customerNumber": "999ZZ55712", "customerName": "GEMB/AMAZON BRC"}, {"type": "inquiry", "industryCode": "FP", "inquiryDate": "********", "customerNumber": "999FP02420", "customerName": "ONEMAIN FINANCIAL"}, {"type": "inquiry", "industryCode": "FF", "inquiryDate": "********", "customerNumber": "999FF04310", "customerName": "RFFC FINANCIAL LLC"}, {"type": "inquiry", "industryCode": "FF", "inquiryDate": "********", "customerNumber": "999FF04310", "customerName": "RFFC FINANCIAL LLC"}, {"type": "inquiry", "industryCode": "FA", "inquiryDate": "********", "customerNumber": "682FA10459", "customerName": "GMFNANCIAL"}, {"type": "inquiry", "industryCode": "BB", "inquiryDate": "********", "customerNumber": "999BB30563", "customerName": "CITIZENS BANK"}, {"type": "inquiry", "industryCode": "FA", "inquiryDate": "********", "customerNumber": "999FA00354", "customerName": "GMAC"}, {"type": "inquiry", "industryCode": "BB", "inquiryDate": "********", "customerNumber": "999BB30992", "customerName": "RADIUS BANK"}, {"type": "inquiry", "industryCode": "BB", "inquiryDate": "********", "customerNumber": "615BB14165", "customerName": "CHILLICOTHE STATE BA"}, {"type": "inquiry", "industryCode": "BB", "inquiryDate": "********", "customerNumber": "615BB14165", "customerName": "CHILLICOTHE STATE BA"}], "models": [{"type": "MODEL", "modelNumber": "05206", "score": 798, "reasons": [{"code": "00010"}, {"code": "00032"}, {"code": "00030"}, {"code": "00012"}], "inquiryKeyFactor": {"code": "Y", "description": "NUMBER OF INQUIRIES ADVERSELY AFFECTED THE SCORE BUT NOT SIGNIFICANTLY"}, "scoreNumberOrMarketMaxIndustryCode": {"code": "1"}}], "identification": {"subjectSocialNum": "*********", "inquirySocialNum": "*********"}, "consumerReferralLocation": {"bureauCode": "062", "bureauName": "EQUIFAX INFORMATION SERVICES LLC", "address": {"primaryAddress": "P O BOX 740241", "cityName": "ATLANTA", "stateAbbreviation": "GA", "zipCode": "*********"}, "telephoneNumber": {}}}}