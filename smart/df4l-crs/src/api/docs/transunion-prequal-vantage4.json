{"fileNumber": "01", "fileSummary": {"fileHitIndicator": "REGULAR_HIT", "ssnMatchIndicator": "EXACT", "consumerStatementIndicator": false, "market": "03", "submarket": "PE", "creditDataStatus": {"suppressed": false, "doNotPromote": {"indicator": false}, "freeze": {"indicator": false}, "minor": false, "disputed": false}, "inFileSinceDate": {"value": "1994-06-23T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}}, "indicative": {"names": [{"person": {"first": "ZELNINO", "middle": "XX", "last": "WINTER"}, "source": "FILE"}], "addresses": [{"status": "CURRENT", "qualifier": "PERSONAL", "street": {"unparseds": [], "number": "760", "name": "SPROUL", "preDirectional": "W", "type": "RD"}, "location": {"city": "FANTASY ISLAND", "state": "IL", "zipCode": "60750"}, "dateReported": {"value": "2019-09-16T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "source": "FILE"}, {"status": "PREVIOUS", "qualifier": "PERSONAL", "street": {"unparseds": [], "number": "555", "name": "ADAMS", "preDirectional": "W", "type": "ST"}, "location": {"city": "CHICAGO", "state": "IL", "zipCode": "60661"}, "dateReported": {"value": "2014-08-24T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "source": "FILE"}], "socialSecurities": [{"number": "666125812", "source": "FILE"}], "dateOfBirths": [{"value": "1977-10-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false, "source": "FILE"}], "ages": [], "phones": [], "employments": [{"employer": {"unparsed": "UAT INC"}, "occupation": "UAT PRIMARY", "dateOnFileSince": {"value": "2023-05-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2023-05-04T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "source": "FILE"}, {"employer": {"unparsed": "INC"}, "occupation": "TESTER1", "dateOnFileSince": {"value": "2023-05-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2023-05-04T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "source": "FILE"}], "creditCards": [], "genders": [], "vehicles": []}, "originalInputs": [], "custom": {"credit": {"trades": [{"subscriber": {"industryCode": "R", "memberCode": "060LA001", "name": {"unparsed": "MT W VLY MHP"}}, "portfolioType": "OPEN", "accountNumber": "88", "dateOpened": {"value": "2017-12-31T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2023-05-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "09", "remarks": [{"code": "PRL", "type": "AFFILIATE"}], "pastDue": "*********", "paymentHistory": {"maxDelinquencies": []}, "accountHistories": [], "updateMethod": "LOCKED", "ecoadesignator": "INDIVIDUAL"}, {"subscriber": {"industryCode": "B", "memberCode": "********", "name": {"unparsed": "PNC BANK"}}, "portfolioType": "INSTALLMENT", "accountNumber": "********", "dateOpened": {"value": "2020-11-24T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2023-05-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "02", "remarks": [], "terms": {"paymentFrequency": "MONTHLY", "paymentScheduleMonthCount": "060", "scheduledMonthlyPayment": "*********"}, "paymentHistory": {"maxDelinquencies": [], "paymentPattern": {"startDate": {"value": "2023-04-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "text": "21X11X111X11111X11111"}, "historicalCounters": {"monthsReviewedCount": "21", "late30DaysTotal": "01", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "accountHistories": [], "updateMethod": "MANUAL", "ecoadesignator": "INDIVIDUAL"}, {"subscriber": {"industryCode": "B", "memberCode": "0860V001", "name": {"unparsed": "BK OF AMER"}}, "portfolioType": "MORTGAGE", "accountNumber": "009985", "dateOpened": {"value": "2017-01-08T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2023-05-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remarks": [], "terms": {"paymentFrequency": "MONTHLY", "paymentScheduleMonthCount": "168", "scheduledMonthlyPayment": "*********"}, "account": {"type": "CV"}, "pastDue": "*********", "paymentHistory": {"maxDelinquencies": [], "paymentPattern": {"startDate": {"value": "2023-04-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "text": "***********************************************"}, "historicalCounters": {"monthsReviewedCount": "47", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "accountHistories": [], "updateMethod": "MANUAL", "ecoadesignator": "JOINT_CONTRACT_LIABILITY"}, {"subscriber": {"industryCode": "B", "memberCode": "0728P546", "name": {"unparsed": "MELLON BK-E"}}, "portfolioType": "REVOLVING", "accountNumber": "69", "dateOpened": {"value": "2014-10-04T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2023-05-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "remarks": [], "pastDue": "*********", "paymentHistory": {"maxDelinquencies": [], "paymentPattern": {"startDate": {"value": "2023-04-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "text": "************************************************"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "accountHistories": [], "updateMethod": "MANUAL", "ecoadesignator": "INDIVIDUAL"}, {"subscriber": {"industryCode": "B", "memberCode": "********", "name": {"unparsed": "BK OF AMER"}}, "portfolioType": "REVOLVING", "accountNumber": "567444", "dateOpened": {"value": "2020-06-22T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2023-04-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "currentBalance": "*********", "highCredit": "*********", "creditLimit": "*********", "accountRating": "01", "remarks": [], "paymentHistory": {"maxDelinquencies": [], "paymentPattern": {"startDate": {"value": "2023-03-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "text": "********************************"}, "historicalCounters": {"monthsReviewedCount": "32", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "accountHistories": [], "updateMethod": "MANUAL", "ecoadesignator": "INDIVIDUAL"}, {"subscriber": {"industryCode": "V", "memberCode": "********", "name": {"unparsed": "CRESTAR BANK"}}, "portfolioType": "INSTALLMENT", "accountNumber": "976771", "dateOpened": {"value": "2014-12-31T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2022-02-18T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateClosed": {"value": "2022-02-18T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "closedIndicator": "NORMAL", "currentBalance": "*********", "highCredit": "*********", "accountRating": "01", "remarks": [{"code": "CLO", "type": "AFFILIATE"}], "terms": {"paymentFrequency": "MONTHLY", "paymentScheduleMonthCount": "064", "scheduledMonthlyPayment": "*********"}, "account": {"type": "ST"}, "pastDue": "*********", "paymentHistory": {"maxDelinquencies": [], "paymentPattern": {"startDate": {"value": "2022-01-18T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "text": "XXXXXXXXXXXXXX111111111111111111"}, "historicalCounters": {"monthsReviewedCount": "48", "late30DaysTotal": "00", "late60DaysTotal": "00", "late90DaysTotal": "00"}}, "accountHistories": [], "updateMethod": "MANUAL", "ecoadesignator": "INDIVIDUAL"}], "collections": [{"subscriber": {"industryCode": "Y", "memberCode": "098MC001", "name": {"unparsed": "RECOVERY SYS"}}, "portfolioType": "OPEN", "accountNumber": "19899", "dateOpened": {"value": "2021-07-07T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "dateEffective": {"value": "2023-05-01T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "currentBalance": "*********", "original": {"creditGrantor": {"unparsed": "MEDICAL"}, "creditorClassification": "MEDICAL_OR_HEALTH_CARE", "balance": "*********"}, "pastDue": "*********", "accountRating": "9B", "remarks": [{"code": "CLA", "type": "AFFILIATE"}], "updateMethod": "LOCKED", "ecoadesignator": "INDIVIDUAL"}], "publicRecords": [], "inquiries": [{"subscriber": {"industryCode": "B", "memberCode": "********", "inquirySubscriberPrefixCode": "40LO", "name": {"unparsed": "HSBC"}}, "date": {"value": "2023-05-14T00:00:00.000+00:00", "estimatedCentury": false, "estimatedYear": false, "estimatedMonth": false, "estimatedDay": false}, "ecoadesignator": "INDIVIDUAL"}]}}, "addOnProducts": [{"code": "06400", "status": "DELIVERED", "idMismatchAlerts": [{"type": "ADDRESS", "condition": "MISMATCH", "inquiriesInLast60Count": "00", "addressStatus": "CURRENT"}], "addressAnalysises": [], "creditorContacts": []}, {"code": "07500", "status": "DELIVERED", "idMismatchAlerts": [], "addressAnalysises": [], "creditorContacts": [{"decodeData": "TRADE", "subscriber": {"industryCode": "VZ", "memberCode": "********", "inquirySubscriberPrefixCode": "0000", "name": {"unparsed": "CRESTAR BANK"}, "address": {"street": {"unparseds": ["757 N 57TH ST"]}, "location": {"city": "RICHMOND", "state": "VA", "zipCode": "23219"}}, "phone": {"number": {"qualifier": "REPORTING_CREDIT", "type": "UNDEFINED", "areaCode": "800", "exchange": "552", "suffix": "3006"}, "source": "FILE"}}}, {"decodeData": "TRADE", "subscriber": {"industryCode": "BC", "memberCode": "********", "inquirySubscriberPrefixCode": "0000", "name": {"unparsed": "BK OF AMER"}, "address": {"street": {"unparseds": ["PO BOX 982238"]}, "location": {"city": "EL PASO", "state": "TX", "zipCode": "79998"}}, "phone": {"number": {"qualifier": "REPORTING_CREDIT", "type": "UNDEFINED", "areaCode": "800", "exchange": "421", "suffix": "2110"}, "source": "FILE"}}}, {"decodeData": "TRADE", "subscriber": {"industryCode": "R", "memberCode": "060LA001", "inquirySubscriberPrefixCode": "0000", "name": {"unparsed": "MT W VLY MHP"}, "address": {"street": {"unparseds": ["25 PLEASANT ST"]}, "location": {"city": "BERLIN", "state": "NH", "zipCode": "03570"}}, "phone": {"number": {"qualifier": "REPORTING_CREDIT", "type": "UNDEFINED", "areaCode": "603", "exchange": "356", "suffix": "7376"}, "source": "FILE"}}}, {"decodeData": "TRADE", "subscriber": {"industryCode": "BZ", "memberCode": "********", "inquirySubscriberPrefixCode": "0000", "name": {"unparsed": "PNC BANK"}, "address": {"street": {"unparseds": ["PO BOX 3180"]}, "location": {"city": "PITTSBURGH", "state": "PA", "zipCode": "15230"}}}}, {"decodeData": "TRADE", "subscriber": {"industryCode": "BI", "memberCode": "0728P546", "inquirySubscriberPrefixCode": "0000", "name": {"unparsed": "MELLON BK-E"}, "address": {"street": {"unparseds": ["PO BOX 149", "ATTN MELLON EAST #611"]}, "location": {"city": "PITTSBURGH", "state": "PA", "zipCode": "15230"}}}}, {"decodeData": "TRADE", "subscriber": {"industryCode": "BM", "memberCode": "0860V001", "inquirySubscriberPrefixCode": "0000", "name": {"unparsed": "BK OF AMER"}, "address": {"street": {"unparseds": ["9000 SOUTHSIDE BLVD  BLDG 400", "FL9-400-05-41"]}, "location": {"city": "JACKSONVILLE", "state": "FL", "zipCode": "32256"}}, "phone": {"number": {"qualifier": "REPORTING_CREDIT", "type": "UNDEFINED", "areaCode": "877", "exchange": "240", "suffix": "5563"}, "source": "FILE"}}}, {"decodeData": "COLLECTION", "subscriber": {"industryCode": "Y", "memberCode": "098MC001", "inquirySubscriberPrefixCode": "0000", "name": {"unparsed": "RECOVERY SYS"}, "address": {"street": {"unparseds": ["2008 PENNSYLVANIA", "AVE"]}, "location": {"city": "WILMINGTON", "state": "DE", "zipCode": "19806"}}, "phone": {"number": {"qualifier": "REPORTING_CREDIT", "type": "UNDEFINED", "areaCode": "302", "exchange": "429", "suffix": "0654"}, "source": "FILE"}}}, {"decodeData": "INQUIRY", "subscriber": {"industryCode": "B", "memberCode": "********", "inquirySubscriberPrefixCode": "4004", "name": {"unparsed": "HSBC"}, "address": {"street": {"unparseds": ["2700 SANDERS ROAD"]}, "location": {"city": "PROSPECT HEIGH", "state": "IL", "zipCode": "60070"}}, "phone": {"number": {"qualifier": "INQUIRY_CREDIT", "type": "UNDEFINED", "areaCode": "847", "exchange": "291", "suffix": "3538"}, "source": "FILE"}}}]}, {"code": "001NN", "status": "DELIVERED", "idMismatchAlerts": [], "addressAnalysises": [], "scoreModel": {"score": {"results": "+589", "derogatoryAlert": false, "fileInquiriesImpactedScore": false, "factors": {"factors": [{"rank": "1", "code": "64"}, {"rank": "2", "code": "38"}, {"rank": "3", "code": "05"}, {"rank": "4", "code": "07"}]}, "scoreCard": "05"}, "characteristics": [], "messages": [], "statements": [], "consumerStatements": [], "complianceConditions": [], "idMismatchAlerts": []}, "creditorContacts": []}]}