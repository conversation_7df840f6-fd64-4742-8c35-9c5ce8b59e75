{"headerRecord": [{"reportDate": "062923", "reportTime": "143911", "preamble": "TCA7", "versionNo": "07", "mKeywordLength": "07", "mKeywordText": "SBMYSQL", "y2kReportedDate": "********"}], "addressInformation": [{"city": "CALIFORNIA CITY", "dwellingType": "A", "firstReportedDate": "********", "lastReportingSubscriberCode": "1350240", "lastUpdatedDate": "********", "source": "2", "state": "CA", "streetName": "LOOP", "streetPrefix": "9817", "streetSuffix": "BLVD", "timesReported": "00", "unitId": "G", "unitType": "APT", "zipCode": "*********"}, {"city": "BURBANK", "dwellingType": "A", "firstReportedDate": "********", "lastUpdatedDate": "********", "source": "1", "state": "CA", "streetName": "OLIVE", "streetPrefix": "450 E", "streetSuffix": "AVE", "timesReported": "00", "unitId": "339", "unitType": "APT", "zipCode": "*********"}, {"city": "LOS ANGELES", "dwellingType": "S", "firstReportedDate": "********", "lastUpdatedDate": "********", "source": "1", "state": "CA", "streetName": "CIMARRON", "streetPrefix": "4427", "streetSuffix": "ST", "timesReported": "17", "zipCode": "*********"}], "consumerIdentity": {"name": [{"firstName": "LAURIE", "surname": "ANDERSON", "type": "N"}, {"firstName": "LAURIE", "surname": "ANDERSON", "type": "S"}, {"firstName": "LAURIE", "middleName": "ANNE", "surname": "BERTHA", "type": "A"}, {"firstName": "LAURIE", "surname": "FULLER", "type": "A"}]}, "employmentInformation": [{"firstReportedDate": "********", "lastUpdatedDate": "********", "name": "PANARAM INTERNATIONAL", "source": "2"}, {"firstReportedDate": "07052015", "lastUpdatedDate": "07052015", "name": "RETIRED", "source": "2"}], "informationalMessage": [{"messageNumber": "84", "messageNumberDetailed": "0084", "messageText": "SSN MATCHES"}, {"messageNumber": "57", "messageNumberDetailed": "0335", "messageText": "F908TOO MANY INQUIRIES LAST 12 MONTHS"}], "inquiry": [{"amount": "UNKNOWN", "date": "01282023", "subscriberCode": "3970658", "subscriberName": "SERVICE & PROF", "terms": "UNK", "type": "08"}, {"amount": "UNKNOWN", "date": "05132021", "kob": "YC", "subscriberCode": "1984208", "subscriberName": "OTHER COLLECTION AGENC", "terms": "UNK", "type": "48"}, {"amount": "UNKNOWN", "date": "05132021", "kob": "YC", "subscriberCode": "1984518", "subscriberName": "OTHER COLLECTION AGENC", "terms": "UNK", "type": "48"}], "ofac": {"messageNumber": "1199", "messageText": "OFAC SEARCH NOT PERFORMED DUE TO MISMATCHED YOB/DOB"}, "publicRecord": [{"courtCode": "2001065", "courtName": "US BKPT CT MS GULFPORT", "ecoa": "1", "evaluation": "N", "filingDate": "********", "referenceNumber": "0352321ERG", "status": "15", "statusDate": "********"}], "riskModel": [{"evaluation": "P", "modelIndicator": "F9", "score": "0650", "scoreFactors": [{"importance": "1", "code": "40"}, {"importance": "2", "code": "33"}, {"importance": "3", "code": "24"}, {"importance": "4", "code": "15"}, {"importance": "5", "code": "08"}]}], "tradeline": [{"accountNumber": "**************", "accountType": "12", "amount1": "********", "amount1Qualifier": "O", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"enhancedAccountCondition": "05", "enhancedAccountType": "12", "enhancedPaymentHistory84": "BCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "51", "enhancedTerms": "117", "enhancedTermsFrequency": "M", "originalLoanAmount": "**********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "EL", "monthsHistory": "09", "openDate": "********", "openOrClosed": "C", "paymentHistory": "BCCCCCCCC", "revolvingOrInstallment": "I", "specialComment": "28", "status": "05", "statusDate": "********", "subscriberCode": "6908265", "subscriberName": "MOHELA", "terms": "117"}, {"accountNumber": "**************", "accountType": "12", "amount1": "********", "amount1Qualifier": "O", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"enhancedAccountCondition": "05", "enhancedAccountType": "12", "enhancedPaymentHistory84": "BCCCCCCCCCCCCCCCCCCCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "51", "enhancedTerms": "117", "enhancedTermsFrequency": "M", "originalLoanAmount": "**********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "EL", "monthsHistory": "27", "openDate": "********", "openOrClosed": "C", "paymentHistory": "BCCCCCCCCCCCCCCCCCCCCCCCC", "revolvingOrInstallment": "I", "specialComment": "28", "status": "05", "statusDate": "********", "subscriberCode": "6908265", "subscriberName": "MOHELA", "terms": "117"}, {"accountNumber": "**************", "accountType": "12", "amount1": "********", "amount1Qualifier": "O", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"enhancedAccountCondition": "05", "enhancedAccountType": "12", "enhancedPaymentHistory84": "BCCCCCCCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "51", "enhancedTerms": "117", "enhancedTermsFrequency": "M", "originalLoanAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "EL", "monthsHistory": "15", "openDate": "********", "openOrClosed": "C", "paymentHistory": "BCCCCCCCCCCCCCC", "revolvingOrInstallment": "I", "specialComment": "28", "status": "05", "statusDate": "********", "subscriberCode": "6908265", "subscriberName": "MOHELA", "terms": "117"}, {"accountNumber": "**************", "accountType": "12", "amount1": "********", "amount1Qualifier": "O", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"enhancedAccountCondition": "05", "enhancedAccountType": "12", "enhancedPaymentHistory84": "BCCCCCCCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "51", "enhancedTerms": "117", "enhancedTermsFrequency": "M", "originalLoanAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "EL", "monthsHistory": "15", "openDate": "********", "openOrClosed": "C", "paymentHistory": "BCCCCCCCCCCCCCC", "revolvingOrInstallment": "I", "specialComment": "28", "status": "05", "statusDate": "********", "subscriberCode": "6908265", "subscriberName": "MOHELA", "terms": "117"}, {"accountNumber": "**************", "accountType": "12", "amount1": "********", "amount1Qualifier": "O", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"enhancedAccountCondition": "05", "enhancedAccountType": "12", "enhancedPaymentHistory84": "BCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "51", "enhancedTerms": "117", "enhancedTermsFrequency": "M", "originalLoanAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "EL", "monthsHistory": "09", "openDate": "********", "openOrClosed": "C", "paymentHistory": "BCCCCCCCC", "revolvingOrInstallment": "I", "specialComment": "28", "status": "05", "statusDate": "********", "subscriberCode": "6908265", "subscriberName": "MOHELA", "terms": "117"}, {"accountNumber": "*************", "accountType": "07", "amount1": "********", "amount1Qualifier": "L", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"creditLimitAmount": "00********", "enhancedAccountCondition": "A3", "enhancedAccountType": "07", "enhancedPaymentHistory84": "B0-********************************0000000", "enhancedPaymentStatus": "11", "enhancedTerms": "REV", "highBalanceAmount": "********00", "paymentLevelDate": "********"}, "evaluation": "P", "kob": "ZR", "monthsHistory": "81", "openDate": "********", "openOrClosed": "C", "paymentHistory": "B0-****************000000", "revolvingOrInstallment": "R", "status": "11", "statusDate": "********", "subscriberCode": "1392176", "subscriberName": "MACYS/FDSB", "terms": "REV"}, {"accountNumber": "**********", "accountType": "18", "amount1": "********", "amount1Qualifier": "L", "amount2": "********", "amount2Qualifier": "H", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "3", "enhancedPaymentData": {"complianceCondition": "XA", "creditLimitAmount": "00********", "enhancedAccountCondition": "A2", "enhancedAccountType": "18", "enhancedPaymentHistory84": "B0CCCCCCCCCCCCCCCCCCCCCCCC0", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "19", "enhancedTerms": "REV", "enhancedTermsFrequency": "M", "highBalanceAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "BC", "lastPaymentDate": "********", "monthsHistory": "27", "openDate": "********", "openOrClosed": "C", "paymentHistory": "B0CCCCCCCCCCCCCCCCCCCCCCC", "revolvingOrInstallment": "R", "specialComment": "19", "status": "12", "statusDate": "********", "subscriberCode": "3200714", "subscriberName": "CAPITAL ONE", "terms": "REV"}, {"accountNumber": "*********", "accountType": "18", "amount1": "********", "amount1Qualifier": "L", "amount2": "********", "amount2Qualifier": "H", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "3", "enhancedPaymentData": {"complianceCondition": "XA", "creditLimitAmount": "**********", "enhancedAccountCondition": "A2", "enhancedAccountType": "18", "enhancedPaymentHistory84": "B0CCCCCCCCCCCCCCCCCCCCCCCC00CC00CCCCCCCC0C0CCC000", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "19", "enhancedTerms": "REV", "enhancedTermsFrequency": "M", "highBalanceAmount": "**********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "BC", "lastPaymentDate": "********", "monthsHistory": "49", "openDate": "********", "openOrClosed": "C", "paymentHistory": "B0CCCCCCCCCCCCCCCCCCCCCCC", "revolvingOrInstallment": "R", "specialComment": "19", "status": "12", "statusDate": "********", "subscriberCode": "1232900", "subscriberName": "CITI/CBNA", "terms": "REV"}, {"accountNumber": "************", "accountType": "18", "amount1": "********", "amount1Qualifier": "H", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "3", "enhancedPaymentData": {"complianceCondition": "XA", "creditLimitAmount": "UNKNOWN", "enhancedAccountCondition": "A2", "enhancedAccountType": "18", "enhancedPaymentHistory84": "BCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "19", "enhancedTerms": "REV", "enhancedTermsFrequency": "M", "highBalanceAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "BC", "monthsHistory": "36", "openDate": "********", "openOrClosed": "C", "paymentHistory": "BCCCCCCCCCCCCCCCCCCCCCCCC", "revolvingOrInstallment": "R", "specialComment": "19", "status": "12", "statusDate": "********", "subscriberCode": "1270246", "subscriberName": "CAPITAL ONE", "terms": "REV"}, {"accountNumber": "****************", "accountType": "18", "amount1": "********", "amount1Qualifier": "L", "amount2": "********", "amount2Qualifier": "H", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "3", "enhancedPaymentData": {"creditLimitAmount": "00********", "enhancedAccountCondition": "A2", "enhancedAccountType": "18", "enhancedPaymentHistory84": "BB0B---------------B-************************00000-0---------------000", "enhancedPaymentStatus": "11", "enhancedSpecialComment": "18", "enhancedTerms": "REV", "enhancedTermsFrequency": "M", "highBalanceAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "N", "kob": "BC", "monthsHistory": "70", "openDate": "********", "openOrClosed": "C", "paymentHistory": "BB0B---------------B-0000", "revolvingOrInstallment": "R", "specialComment": "18", "status": "12", "statusDate": "********", "subscriberCode": "1310331", "subscriberName": "JPMCB CARD", "terms": "REV"}, {"accountNumber": "**********", "accountType": "13", "amount1": "********", "amount1Qualifier": "O", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"enhancedAccountCondition": "A2", "enhancedAccountType": "13", "enhancedPaymentHistory84": "BCCCCC", "enhancedPaymentStatus": "11", "enhancedTerms": "048", "originalLoanAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "P", "kob": "BB", "monthsHistory": "06", "openDate": "********", "openOrClosed": "C", "paymentHistory": "BCCCCC", "revolvingOrInstallment": "I", "status": "12", "statusDate": "********", "subscriberCode": "1102013", "subscriberName": "JPMCB AUTO", "terms": "048"}, {"accountNumber": "****************", "accountType": "18", "amount1": "********", "amount1Qualifier": "L", "amount2": "********", "amount2Qualifier": "H", "balanceAmount": "********", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "3", "enhancedPaymentData": {"actualPaymentAmount": "********", "creditLimitAmount": "00********", "enhancedAccountCondition": "A1", "enhancedAccountType": "18", "enhancedPaymentHistory84": "CCCC", "enhancedPaymentStatus": "11", "enhancedTerms": "REV", "enhancedTermsFrequency": "M", "highBalanceAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "P", "kob": "BC", "lastPaymentDate": "********", "monthlyPaymentAmount": "********", "monthlyPaymentType": "S", "monthsHistory": "04", "openDate": "********", "openOrClosed": "O", "paymentHistory": "CCCC", "revolvingOrInstallment": "R", "status": "11", "statusDate": "********", "subscriberCode": "3182310", "subscriberName": "JPMCB CARD", "terms": "REV"}, {"accountNumber": "**********", "accountType": "18", "amount1": "********", "amount1Qualifier": "L", "amount2": "********", "amount2Qualifier": "H", "balanceAmount": "********", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "3", "enhancedPaymentData": {"creditLimitAmount": "00********", "enhancedAccountCondition": "A1", "enhancedAccountType": "18", "enhancedPaymentHistory84": "CCCCC", "enhancedPaymentStatus": "11", "enhancedTerms": "REV", "highBalanceAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "P", "kob": "BC", "lastPaymentDate": "********", "monthlyPaymentAmount": "********", "monthlyPaymentType": "S", "monthsHistory": "05", "openDate": "********", "openOrClosed": "O", "paymentHistory": "CCCCC", "revolvingOrInstallment": "R", "status": "11", "statusDate": "********", "subscriberCode": "3208430", "subscriberName": "BANK CREDIT CARD", "terms": "REV"}, {"accountNumber": "****************", "accountType": "07", "amount1": "********", "amount1Qualifier": "L", "amount2": "********", "amount2Qualifier": "H", "balanceAmount": "********", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "0", "enhancedPaymentData": {"creditLimitAmount": "00********", "enhancedAccountCondition": "A1", "enhancedAccountType": "07", "enhancedPaymentHistory84": "00CCCCCCCCCCCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedTerms": "REV", "enhancedTermsFrequency": "M", "highBalanceAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "P", "kob": "ZR", "lastPaymentDate": "********", "monthsHistory": "20", "openDate": "********", "openOrClosed": "O", "paymentHistory": "00CCCCCCCCCCCCCCCCCC", "revolvingOrInstallment": "R", "status": "11", "statusDate": "********", "subscriberCode": "3178962", "subscriberName": "THD/CBNA", "terms": "REV"}, {"accountNumber": "**************", "accountType": "12", "amount1": "********", "amount1Qualifier": "O", "balanceAmount": "********", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"enhancedAccountCondition": "A1", "enhancedAccountType": "12", "enhancedPaymentHistory84": "CCCCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedTerms": "179", "enhancedTermsFrequency": "M", "originalLoanAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "P", "kob": "EL", "lastPaymentDate": "********", "monthlyPaymentAmount": "********", "monthlyPaymentType": "S", "monthsHistory": "11", "openDate": "********", "openOrClosed": "O", "paymentHistory": "CCCCCCCCCCC", "revolvingOrInstallment": "I", "status": "11", "statusDate": "********", "subscriberCode": "8997779", "subscriberName": "MOHELA", "terms": "179"}, {"accountNumber": "**************", "accountType": "12", "amount1": "********", "amount1Qualifier": "O", "balanceAmount": "********", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"enhancedAccountCondition": "A1", "enhancedAccountType": "12", "enhancedPaymentHistory84": "CCCCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedTerms": "179", "enhancedTermsFrequency": "M", "originalLoanAmount": "00********", "paymentLevelDate": "********"}, "evaluation": "P", "kob": "EL", "lastPaymentDate": "********", "monthlyPaymentAmount": "********", "monthlyPaymentType": "S", "monthsHistory": "11", "openDate": "********", "openOrClosed": "O", "paymentHistory": "CCCCCCCCCCC", "revolvingOrInstallment": "I", "status": "11", "statusDate": "********", "subscriberCode": "8997779", "subscriberName": "MOHELA", "terms": "179"}, {"accountNumber": "**********", "accountType": "07", "amount1": "********", "amount1Qualifier": "L", "amount2": "********", "amount2Qualifier": "C", "balanceAmount": "********", "balanceDate": "********", "delinquencies30Days": "00", "delinquencies60Days": "00", "delinquencies90to180Days": "00", "derogCounter": "00", "ecoa": "1", "enhancedPaymentData": {"chargeoffAmount": "00********", "creditLimitAmount": "00********", "enhancedAccountCondition": "A4", "enhancedAccountType": "07", "enhancedPaymentHistory84": "0-------CCCCCCCCCCCCCCCCCC", "enhancedPaymentStatus": "11", "enhancedTerms": "REV", "highBalanceAmount": "**********", "paymentLevelDate": "********"}, "evaluation": "P", "kob": "BC", "lastPaymentDate": "********", "monthsHistory": "26", "openDate": "********", "openOrClosed": "O", "paymentHistory": "0-------CCCCCCCCCCCCCCCCC", "revolvingOrInstallment": "R", "status": "11", "statusDate": "********", "subscriberCode": "2225419", "subscriberName": "BANK CREDIT CARD", "terms": "REV"}], "endTotals": [{"totalSegments": "036", "totalLength": "07160"}]}