use granite::{Decimal, Utc, Uuid};
use itertools::Itertools;

pub struct ClientDebts {
    pub debts: Vec<ClientDebt>,
}

#[granite::gtype]
pub struct ClientDebt {
    pub client_debt_uuid: Uuid,
    pub client_uuid: Uuid,
    pub client_debt_esid: Option<String>,
    pub name: Option<String>,
    pub balance: Option<Decimal>,
    pub balance_date: Option<DateUtc>,
    pub interest: Option<Decimal>,
    pub payment: Option<Decimal>,
    pub active: bool,
}

pub async fn get_client_debts_merged(
    app: &impl crate::App,
    client_uuid: Uuid,
) -> granite::Result<ClientDebts> {
    let dbcx = app.postgres_dbcx().await?;

    let rows = granite::pg_row_vec!(
        db = dbcx;
        args = {
            $client_uuid: &client_uuid,
        };
        row = {
            client_debt_uuid: Uuid,
            client_uuid: Uuid,
            client_debt_esid: Option<String>,
            name: Option<String>,
            balance: Option<Decimal>,
            balance_date: Option<DateUtc>,
            interest: Option<Decimal>,
            payment: Option<Decimal>,
            active: bool,
        };
        SELECT
            client_debt_uuid,
            client_uuid,
            client_debt_esid,
            name,
            balance,
            balance_date,
            interest,
            payment,
            active
        FROM
            df4l.client_debt
        WHERE
            client_uuid = $client_uuid::uuid
    )
    .await?;
    // Fill in plausible fake data for rows with client_debt_esid
    let rows = rows
        .into_iter()
        .map(|mut row| {
            if row.client_debt_esid.is_some() {
                use rand::Rng;
                let mut rng = rand::thread_rng();

                // create a list of names to use like chase, honda, etc.
                let names = [
                    "Chase",
                    "Honda",
                    "Toyota",
                    "GM",
                    "Ford",
                    "Wells Fargo",
                    "Citi",
                    "Bank of America",
                    "USAA",
                    "Capital One",
                ];
                row.name = Some(names[rng.gen_range(0..names.len())].to_string());

                // Random balance between $500 and $50,000
                let balance_cents = rng.gen_range(50000..5000000);
                row.balance = Some(Decimal::new(balance_cents, 2));

                // Random interest between 2% and 30%
                let interest_basis_points = rng.gen_range(200..3000);
                row.interest = Some(Decimal::new(interest_basis_points, 2));

                // Calculate monthly payment (rough estimate: balance * (1 + interest_rate) / 12)
                let monthly_payment = (balance_cents as f64
                    * (1.0 + interest_basis_points as f64 / 10000.0)
                    / 12.0) as i64;
                row.payment = Some(Decimal::new(monthly_payment, 2));

                // Random date within last 3 months
                let days_ago = rng.gen_range(0..90);
                row.balance_date = Some(Utc::now().date_naive() - chrono::Duration::days(days_ago));
            }

            row
        })
        .collect::<Vec<_>>();

    let debts: Vec<ClientDebt> = rows
        .into_iter()
        .map(|row| ClientDebt {
            client_debt_uuid: row.client_debt_uuid,
            client_uuid: row.client_uuid,
            client_debt_esid: row.client_debt_esid,
            name: row.name,
            balance: row.balance,
            balance_date: row.balance_date,
            interest: row.interest,
            payment: row.payment,
            active: row.active,
        })
        .sorted_by_key(|debt| match (&debt.client_debt_esid, &debt.name) {
            (Some(_), Some(name)) => (1, name.clone()),
            (Some(_), None) => (2, "".to_string()),
            (None, Some(name)) => (3, name.clone()),
            (None, None) => (4, "".to_string()),
        })
        .sorted_by_key(|debt| debt.client_debt_uuid) // TODO:DF4L - Remove this when we have real data
        .collect();

    Ok(ClientDebts { debts })
}
