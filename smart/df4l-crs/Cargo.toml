[package]
name = "df4l-crs"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["approck", "auth-fence", "bux", "df4l-zero", "granite"]


[dependencies]
approck = { workspace = true }
approck-redis = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
df4l-zero = { path = "../df4l-zero" }
granite = { workspace = true }
approck-postgres = { workspace = true }
itertools = { workspace = true }
rand = { workspace = true }
chrono = { workspace = true }

maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
