#[approck::http(GET /api/icover/client/{client_uuid:Uuid}/; AUTH None; return JSON|Empty;)]
pub mod get {
    use crate::api::client::detail as client_api;

    pub async fn request(app: App, identity: Identity, path: Path) -> Result<Response> {
        // check permissions
        if !identity.scope_d2c_read() {
            let response = Empty {
                status: approck::StatusCode::UNAUTHORIZED,
                ..Default::default()
            };
            return Ok(Response::Empty(response));
        }

        let input = client_api::get::Input {
            client_uuid: path.client_uuid,
        };

        match client_api::get::call(app, identity.as_ref(), input).await {
            Ok(output) => {
                approck::debug!(
                    "/api/icover/client/{}/ : Agent and Client data loaded successfully",
                    path.client_uuid
                );

                // Client will not expect Some(value), etc.. so we're using serde instead
                Ok(Response::JSON(serde_json::json!(output).into()))
            }
            Err(e) => {
                approck::error!(
                    "/api/icover/client/{}/ : Error getting data for client: {}",
                    path.client_uuid,
                    e
                );

                let response = Empty {
                    status: approck::StatusCode::BAD_REQUEST,
                    ..Default::default()
                };
                return Ok(Response::Empty(response));
            }
        }
    }
}
