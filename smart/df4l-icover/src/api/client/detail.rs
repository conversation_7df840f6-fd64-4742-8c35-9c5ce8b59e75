// Client and Policy Data Endpoint:
// Host: df4l-1.us-east-2.acp7.link
// GET /api/icover/client-policy/{client_uuid}
pub mod get {
    pub struct Input {
        pub client_uuid: granite::Uuid,
    }

    #[derive(serde::Serialize, Debug)]
    pub struct Output {
        pub agent: Agent,
        pub client: Client,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct Agent {
        pub agent_email: String,
        pub agent_first_name: String,
        pub agent_last_name: String,
        pub agent_contact_number: String,
        pub agent_access_level: String,
        pub agent_states_approved: Vec<AgentStateApproval>,
        // TODO: Make GBU agent id required in schema (??? iCover insists this must be non-nullable)
        pub client_agent_id: String, // GBU Agent ID
        pub agent_address: AgentAddress,
    }

    #[derive(serde::Serialize, serde::Deserialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct AgentStateApproval {
        pub product_name: String, // Debt2Capital
        pub states: Vec<String>,
    }

    // Internal struct for parsing database JSON (snake_case)
    #[derive(serde::Deserialize, Debug)]
    struct DbAgentStateApproval {
        pub product_name: String,
        pub states: Vec<String>,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct AgentAddress {
        pub street_one: String,
        pub street_two: String,
        pub city: String,
        pub state: String,
        pub zipcode: String,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct Client {
        pub client_uuid: granite::Uuid,
        pub first_name: String,
        pub last_name: String,
        pub street_one: String,
        pub street_two: String,
        pub city: String,
        pub state: String,
        pub zipcode: String,
        pub phone: String,
        pub gender: String,
        pub birth_date: String,
        pub target_premium: granite::Decimal,
        pub pua_split: String,
        pub pua_type: PuaType,
        pub pua_amount: granite::Decimal,
        pub pua_duration: i32,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "snake_case")]
    pub enum PuaType {
        LumpSum,
        Recurring,
    }

    impl std::fmt::Display for PuaType {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            match self {
                PuaType::LumpSum => write!(f, "lump_sum"),
                PuaType::Recurring => write!(f, "recurring"),
            }
        }
    }

    pub async fn call(
        app: &impl crate::App,
        identity: &impl crate::Identity,
        input: Input,
    ) -> granite::Result<Output> {
        if !identity.scope_d2c_read() {
            return Err(granite::authorization_error!(
                "insufficient permissions to read client policy information"
            ));
        }

        let _identity_uuid = match identity.identity_uuid() {
            Some(identity_uuid) => identity_uuid,
            None => {
                return Err(granite::authorization_error!("User not logged in"));
            }
        };

        let dbcx = app.postgres_dbcx().await?;

        // Look up the advisor data and return as the fields defined in the Agent struct
        let row = match granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                agent_email: String,
                agent_first_name: String,
                agent_last_name: String,
                agent_contact_number: String,
                agent_access_level: String,
                client_agent_id: String,
                agent_state_approval: String,
                agent_address_street_one: String,
                agent_address_street_two: String,
                agent_address_city: String,
                agent_address_state: String,
                agent_address_zip: String,
                client_uuid: Uuid,
                client_first_name: String,
                client_last_name: String,
                client_street_one: String,
                client_street_two: String,
                client_city: String,
                client_state: String,
                client_zip: String,
                client_phone: String,
                client_gender: String,
                client_birth_date: DateUtc,
            };
            SELECT
                COALESCE(adv.email, "<EMAIL>") AS agent_email,
                adv.first_name AS agent_first_name,
                adv.last_name AS agent_last_name,
                COALESCE(adv.phone, "************") AS agent_contact_number,
                "agent" AS agent_access_level,
                adv.gbu_advisor_esid AS client_agent_id,
                (
                    SELECT
                        JSON_BUILD_OBJECT(
                            "product_name", "Debt2Capital",
                            "states", ARRAY_AGG(sl.state_code)
                        )
                    FROM
                        df4l.advisor_statelic asl
                        JOIN df4l.statelic sl ON asl.state_code = sl.state_code
                    WHERE adv.advisor_uuid = asl.advisor_uuid
                )::TEXT AS agent_state_approval,
                COALESCE(adv.address1, "456 North Main St") AS agent_address_street_one,
                COALESCE(adv.address2, "") AS agent_address_street_two,
                COALESCE(adv.city, "Raleigh") AS agent_address_city,
                COALESCE(adv.state, "NC") AS agent_address_state,
                COALESCE(adv.zip, "55555") AS agent_address_zip,
                cli.client_uuid,
                cli.first_name AS client_first_name,
                cli.last_name AS client_last_name,
                COALESCE(cli.address1, "123 Main St") AS client_street_one,
                COALESCE(cli.address2, "") AS client_street_two,
                COALESCE(cli.city, "Raleigh") AS client_city,
                COALESCE(cli.state, "NC") AS client_state,
                COALESCE(cli.zip, "66666") AS client_zip,
                COALESCE(cli.phone, "************") AS client_phone,
                COALESCE(cli.gender, "Male") AS client_gender,
                COALESCE(cli.birth_date, "1980-01-01") AS client_birth_date
            FROM
                df4l.client cli
                JOIN df4l.advisor adv USING (advisor_uuid)
            WHERE
                client_uuid = $client_uuid
        )
        .await
        {
            Ok(row) => {
                approck::debug!("Agent/Client data loaded successfully: {:?}", row);
                row
            }
            Err(e) => {
                approck::error!("Error (AF345AFE234AWEF) loading agent/client data: {:?}", e);
                return Err(granite::Error::new(granite::ErrorType::DataNotFound)
                    .add_context(e)
                    .add_context("client not found"));
            }
        };

        let agent_states_approved: Vec<AgentStateApproval> =
            match serde_json::from_str::<DbAgentStateApproval>(&row.agent_state_approval) {
                Ok(db_approval) => {
                    approck::debug!(
                        "Agent state approved licenses parsed successfully: {:?}",
                        db_approval
                    );
                    vec![AgentStateApproval {
                        product_name: db_approval.product_name,
                        states: db_approval.states,
                    }]
                }
                Err(e) => {
                    approck::error!(
                        "Error (AF345AFE234AWEF) parsing agent state licenses: {:?}",
                        e
                    );
                    return Err(granite::Error::new(granite::ErrorType::Unexpected)
                        .add_context(e)
                        .add_context("error parsing agent state licenses"));
                }
            };

        let agent_address = AgentAddress {
            street_one: row.agent_address_street_one,
            street_two: row.agent_address_street_two,
            city: row.agent_address_city,
            state: row.agent_address_state,
            zipcode: row.agent_address_zip,
        };

        Ok(Output {
            agent: Agent {
                agent_email: row.agent_email,
                agent_first_name: row.agent_first_name,
                agent_last_name: row.agent_last_name,
                agent_contact_number: row.agent_contact_number.chars().filter(|c| c.is_numeric()).collect(),
                agent_access_level: row.agent_access_level,
                agent_states_approved,
                // for now let's just make sure they get SOMETHING
                client_agent_id: row.client_agent_id,
                agent_address,
            },
            client: Client {
                client_uuid: row.client_uuid,
                first_name: row.client_first_name,
                last_name: row.client_last_name,
                street_one: row.client_street_one,
                street_two: row.client_street_two,
                city: row.client_city,
                state: row.client_state,
                zipcode: row.client_zip,
                // They only want digits in the phone number
                phone: row.client_phone.chars().filter(|c| c.is_numeric()).collect(),
                gender: row.client_gender,
                // format birthdate as MM/DD/YYYY
                birth_date: row.client_birth_date.format("%m/%d/%Y").to_string(),
                target_premium: granite::Decimal::new(1000, 0),
                pua_split: "50/50".to_string(),
                pua_type: PuaType::LumpSum,
                pua_amount: granite::Decimal::new(500, 0),
                pua_duration: 12,
            },
        })
    }
}
