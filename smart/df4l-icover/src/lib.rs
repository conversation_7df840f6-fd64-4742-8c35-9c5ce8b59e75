pub mod api;
pub mod web;

pub trait App:
    approck::App + approck_postgres::App + approck_redis::App + auth_fence_provider::App
{
    fn auth_fence_provider(&self) -> &auth_fence_provider::ModuleStruct;
    fn take_me_to_icover_client_url(&self, client_uuid: granite::Uuid) -> String;
}

pub trait Identity:
    approck::Identity + auth_fence::Identity + auth_fence_provider::Identity
{
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;

    fn scope_d2c_read(&self) -> bool;
}
