
schema df4l {
    table "client_debt" {
        primary_key = ["client_debt_uuid"]
        
        column "client_debt_uuid" Uuid {
            default = uuidv7()
            update = false
            primary_key = true
        }

        column "client_uuid" Uuid {
            fkrr = "client.client_uuid"
        }

        column "create_ts" DateTimeTz {
            default = now()
            update = false
        }

        column "update_ts" DateTimeTz {
            null = true
        }

        column "name" String {
            type = "Name"
        }

        column "interest" Decimal {
            nullable = true
        }

        column "payment" Decimal {
            nullable = true
        }

        column "balance" Decimal {
            nullable = true
        }
    }
}
