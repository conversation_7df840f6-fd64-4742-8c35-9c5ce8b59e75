#![allow(non_snake_case)]
pub mod Document;
pub mod DocumentPublic;
pub mod dashboard;
pub mod myaccount;

#[approck::prefix(/)]
pub mod prefix {
    pub fn menu(menu: Menu, identity: Identity) {
        if let Some(identity_uuid) = identity.identity_uuid() {
            menu.add_link("Dashboard", "/dashboard");
            menu.add_link("My Account", &crate::ml_myaccount(identity_uuid));
        }
    }
}
