#[approck::http(GET /myaccount/{identity_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("My Account");

        // Fetch all account data using our consolidated API
        use crate::api::myaccount::summary::get_myaccount_summary;

        let account_data = get_myaccount_summary::call(
            app,
            identity,
            get_myaccount_summary::Input {
                identity_uuid: path.identity_uuid,
            },
        )
        .await?;

        // Prepare display values
        let display_email = account_data
            .email
            .as_deref()
            .unwrap_or("Email not available");

        let mut advisors = Vec::new();
        for advisor in account_data.advisors.iter() {
            let mut advisor_info = bux::component::insight_deck::InsightDeck::new("Advisor Info");
            advisor_info.description(
                "An overview of your advisor identifiers, contact details, and state licensures.",
            );
            advisor_info.add_basic_tile(
                "fas fa-id-badge",
                "Advisor ID",
                html!((advisor.advisor_esid)),
            );

            advisor_info.add_edit_tile(
                "fas fa-pen",
                "GBU Advisor ID",
                match &advisor.gbu_advisor_esid {
                    Some(id) => html!((id)),
                    None => html!("None"),
                },
                df4l_advisor::ml_myaccount_advisor_gbu(path.identity_uuid, advisor.advisor_uuid),
            );

            advisor_info.add_edit_row_phone(advisor.phone.as_deref(), "");
            advisor_info.add_edit_row_email(advisor.email.as_deref(), "");
            advisor_info.add_edit_row_address(advisor.address.as_deref(), "");

            // states of licensure
            advisor_info.add_edit_row(
                "fas fa-id-badge",
                "States of Licensure",
                html! {
                    @if advisor.states_of_licensure.is_empty() {
                        label-tag.danger { "None" }
                    } @else {
                        @for (i, state) in advisor.states_of_licensure.iter().enumerate() {
                            @if i > 0 {
                                " "
                            }
                            label-tag.primary { (state) }
                        }
                    }
                },
                "foo", //&df4l_advisor::ml_myaccount_advisor_statelic(path.identity_uuid, advisor.advisor_uuid)
            );

            advisors.push(advisor_info);
        }

        doc.add_body(html!(
            d2c-my-account {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (account_data.name) }
                                    p.phone.mb-0 {
                                        "Phone not available"
                                    }
                                    p.email {
                                        @if let Some(email) = &account_data.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                    hr;
                                    a.btn.primary.block href="#" { "Visit Stripe Billing" }
                                }
                            }
                        }
                    }
                    cell-9 {
                        // Login Info Panel
                        panel.login-info {
                            header {
                                div {
                                    h5 { "Login Info" }
                                    p { "A summary of your login credentials and recent login activity." }
                                }
                                (bux::button::link::label_icon_class("Details", "fas fa-arrow-right", &format!("/myaccount/{}/security/", path.identity_uuid), "sm primary"))
                            }
                            content {
                                grid-2 {
                                    panel.gray-tile {
                                        content {
                                            hbox {
                                                i.fas.fa-fingerprint aria-hidden="true" {}
                                                dl {
                                                    dt { "Username" }
                                                    dd { (account_data.username) }
                                                }
                                            }
                                        }
                                    }
                                    panel.gray-tile {
                                        content {
                                            hbox {
                                                i.fas.fa-key aria-hidden="true" {}
                                                dl {
                                                    dt { "Password last updated" }
                                                    dd {
                                                        @if let Some(password_changed) = &account_data.password_last_changed {
                                                            (password_changed)
                                                        } @else {
                                                            "Unknown"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    panel.gray-tile {
                                        content {
                                            hbox {
                                                i.fas.fa-sign-in-alt aria-hidden="true" {}
                                                dl {
                                                    dt { "Last login" }
                                                    dd {
                                                        @if let Some(last_login) = &account_data.last_login {
                                                            (last_login)
                                                        } @else {
                                                            "Never"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    panel.gray-tile {
                                        content {
                                            hbox {
                                                i.fas.fa-calendar-alt aria-hidden="true" {}
                                                dl {
                                                    dt { "Login attempts today" }
                                                    dd {
                                                        @if account_data.login_attempts_today == 0 {
                                                            "0 attempts"
                                                        } @else if account_data.login_attempts_today <= 2 {
                                                            (format!("{} attempts", account_data.login_attempts_today))
                                                        } @else {
                                                            (format!("{} attempts", account_data.login_attempts_today))
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // Security Settings Panel
                        panel.security-settings {
                            header {
                                div {
                                    h5 { "Security Settings" }
                                    p { "An overview of your current security settings and verification status" }
                                }
                                (bux::button::link::label_icon_class("Details", "fas fa-arrow-right", &format!("/myaccount/{}/security/", path.identity_uuid), "sm primary"))
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-shield-alt aria-hidden="true" {}
                                            dl {
                                                dt { "2-Step Verification" }
                                                dd {
                                                    label-tag.success { "Enabled" }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-envelope-open-text aria-hidden="true" {}
                                            dl {
                                                dt { "MFA By Email" }
                                                dd {
                                                    @if account_data.mfa_email_enabled {
                                                        label-tag.success { "MFA Email Enabled" }
                                                    } @else {
                                                        label-tag.warning { "MFA Email Disabled" }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-cog aria-hidden="true" {}
                                            dl {
                                                dt { "MFA By Authenticator App" }
                                                dd {
                                                    label-tag.success { "Authenticator App Enabled" }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fab.fa-google aria-hidden="true" {}
                                            dl {
                                                dt { "Google Login" }
                                                dd {
                                                    label-tag.success { "Enabled" }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-mobile-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Mobile Number" }
                                                dd {
                                                    label-tag.success { "Verified" }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-envelope-open-text aria-hidden="true" {}
                                            dl {
                                                dt { "Email Address" }
                                                dd {
                                                    label-tag.success { "Verified" }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        @for advisor in &account_data.advisors {
                            panel.advisor-info {
                                header {
                                    div {
                                        h5 { "Advisor Info" }
                                        p { "An overview of your advisor identifiers, contact details, and state licensures." }
                                    }
                                }
                                content {
                                    // Top section with Advisor ID and GBU Advisor ID
                                    grid-2 {
                                        panel.gray-tile {
                                            content {
                                                hbox {
                                                    i.far.fa-id-badge.advisor-id aria-hidden="true" {}
                                                    dl {
                                                        dt { "Advisor ID" }
                                                        dd { (advisor.advisor_esid) }
                                                    }
                                                }
                                            }
                                        }
                                        panel.gray-tile {
                                            content {
                                                hbox {
                                                    @if advisor.can_edit_gbu_id {
                                                        a.btn.primary.edit-btn href=(format!("/myaccount/{}/advisor/{}/gbu", path.identity_uuid, advisor.advisor_uuid)) {
                                                            i.fas.fa-pen aria-hidden="true" {}
                                                        }
                                                    } @else {
                                                        i.fas.fa-pen.advisor-gbu aria-hidden="true" {}
                                                    }
                                                    dl {
                                                        dt { "GBU Advisor ID" }
                                                        dd {
                                                            @if let Some(gbu_id) = &advisor.gbu_advisor_esid {
                                                                (gbu_id)
                                                            } @else {
                                                                "None"
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    ul.data-list {
                                        li {
                                            hbox {
                                                i.fas.fa-map-marker-alt aria-hidden="true" {}
                                                dl {
                                                    dt { "Address" }
                                                    dd {
                                                        @if advisor.address.is_some() || advisor.city.is_some() || advisor.state.is_some() || advisor.zip.is_some() {
                                                            @if let Some(address) = &advisor.address {
                                                                (address) ", "
                                                            }
                                                            @if let Some(city) = &advisor.city {
                                                                (city)
                                                                @if advisor.state.is_some() || advisor.zip.is_some() {
                                                                    ", "
                                                                }
                                                            }
                                                            @if let Some(state) = &advisor.state {
                                                                (state)
                                                                @if let Some(zip) = &advisor.zip {
                                                                    " " (zip)
                                                                }
                                                            }
                                                        } @else {
                                                            "No address provided"
                                                        }
                                                    }
                                                }
                                                @if advisor.can_edit_gbu_id {
                                                    span {
                                                        " ("
                                                        a.edit-link href=(format!("/myaccount/{}/advisor/{}/contact", path.identity_uuid, advisor.advisor_uuid)) { "Edit" }
                                                        ")"
                                                    }
                                                }
                                            }
                                        }
                                        li {
                                            hbox {
                                                i.fas.fa-phone aria-hidden="true" {}
                                                dl {
                                                    dt { "Phone" }
                                                    dd {
                                                        @if let Some(phone) = &advisor.phone {
                                                            (phone)
                                                        } @else {
                                                            "No phone provided"
                                                        }
                                                    }
                                                }
                                                @if advisor.can_edit_gbu_id {
                                                    span {
                                                        " ("
                                                        a.edit-link href=(format!("/myaccount/{}/advisor/{}/contact", path.identity_uuid, advisor.advisor_uuid)) { "Edit" }
                                                        ")"
                                                    }
                                                }
                                            }
                                        }
                                        li {
                                            hbox {
                                                i.fas.fa-envelope-open-text aria-hidden="true" {}
                                                dl {
                                                    dt { "Email" }
                                                    dd {
                                                        @if let Some(email) = &advisor.email {
                                                            (email)
                                                        } @else {
                                                            "No email provided"
                                                        }
                                                    }
                                                }
                                                @if advisor.can_edit_gbu_id {
                                                    span {
                                                        " ("
                                                        a.edit-link href=(format!("/myaccount/{}/advisor/{}/contact", path.identity_uuid, advisor.advisor_uuid)) { "Edit" }
                                                        ")"
                                                    }
                                                }
                                            }
                                        }
                                        li {
                                            hbox {
                                                i.fas.fa-id-badge aria-hidden="true" {}
                                                dl {
                                                    dt { "States of Licensure" }
                                                    dd {
                                                        @if advisor.states_of_licensure.is_empty() {
                                                            label-tag.danger { "None" }
                                                        } @else {
                                                            @for (i, state) in advisor.states_of_licensure.iter().enumerate() {
                                                                @if i > 0 {
                                                                    " "
                                                                }
                                                                label-tag.primary { (state) }
                                                            }
                                                        }
                                                    }
                                                }
                                                @if advisor.can_edit_gbu_id {
                                                    span {
                                                        " ("
                                                        a.edit-link href=(format!("/myaccount/{}/advisor/{}/statelic", path.identity_uuid, advisor.advisor_uuid)) { "Edit" }
                                                        ")"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            // Add margin between multiple advisors
                            @if account_data.advisors.len() > 1 {
                                div style="margin-bottom: 1rem;" {}
                            }
                        }

                        // This is the new rendering implementation for advisors using the insight deck
                        @for advisor in &advisors {
                            (advisor)
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
