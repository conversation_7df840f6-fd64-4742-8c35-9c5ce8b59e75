#[approck::api]
pub mod get_myaccount_summary {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub name: String,
        pub email: Option<String>,
        pub username: String,
        pub mfa_email_enabled: bool,
        pub mfa_backup_codes_enabled: bool,
        pub last_login: Option<String>,
        pub password_last_changed: Option<String>,
        pub login_attempts_today: i32,
        pub advisors: Vec<AdvisorInfo>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct AdvisorInfo {
        pub advisor_uuid: Uuid,
        pub advisor_esid: String,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub gbu_advisor_esid: Option<String>,
        pub address: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub states_of_licensure: Vec<String>,
        pub can_edit_gbu_id: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.is_logged_in() {
            return_authorization_error!("User must be logged in to access account summary");
        }

        // 1. Get profile info from auth_fence.identity
        let profile_row_option = granite::pg_row_option!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                name: String,
                email: Option<String>,
            };
            SELECT
                name,
                email
            FROM
                auth_fence.identity
            WHERE
                identity_uuid = $identity_uuid::uuid
                AND active = true
        )
        .await?;

        let (name, email) = match profile_row_option {
            Some(row) => (row.name, row.email),
            None => return_authorization_error!("No profile found for this identity"),
        };

        // 2. Generate fake login security data
        // TODO:DF4L - Replace with real MFA and security data from database
        let uuid_str = input.identity_uuid.to_string();
        let last_char = uuid_str.chars().last().unwrap_or('0');

        let mfa_email_enabled = matches!(last_char, '0'..='7');
        let mfa_backup_codes_enabled = matches!(last_char, '0'..='5');
        let login_attempts = match last_char {
            '0'..='2' => 0,
            '3'..='6' => 1,
            '7'..='9' => 2,
            _ => 0,
        };

        let username = email
            .clone()
            .unwrap_or_else(|| "<EMAIL>".to_string());
        let last_login = Some("2025-01-15 14:30:00 UTC".to_string());
        let password_last_changed = Some("2024-12-01 09:15:00 UTC".to_string());

        // 3. Get advisor info from df4l.advisor
        let advisor_rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                advisor_uuid: Uuid,
                advisor_esid: String,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                gbu_advisor_esid: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
            };
            SELECT
                advisor_uuid,
                advisor_esid,
                first_name,
                last_name,
                email,
                phone,
                gbu_advisor_esid,
                address1,
                address2,
                city,
                state,
                zip
            FROM
                df4l.advisor
            WHERE
                identity_uuid = $identity_uuid::uuid
                AND active = true
        )
        .await?;

        let mut advisors = Vec::new();

        // For each advisor, get their states of licensure
        for advisor_row in advisor_rows {
            let state_rows = granite::pg_row_vec!(
                db = dbcx;
                args = {
                    $advisor_uuid: &advisor_row.advisor_uuid,
                };
                row = {
                    label: String,
                };
                SELECT
                    s.label
                FROM
                    df4l.advisor_statelic asl
                JOIN
                    df4l.statelic s ON asl.state_code = s.state_code
                WHERE
                    asl.advisor_uuid = $advisor_uuid::uuid
                ORDER BY
                    s.label
            )
            .await?;

            let states_of_licensure: Vec<String> = state_rows
                .into_iter()
                .map(|state_row| state_row.label)
                .collect();

            // Combine address fields into a single address string
            let address = match (&advisor_row.address1, &advisor_row.address2) {
                (Some(addr1), Some(addr2)) if !addr2.trim().is_empty() => {
                    Some(format!("{}, {}", addr1, addr2))
                }
                (Some(addr1), _) => Some(addr1.clone()),
                _ => None,
            };

            // TODO:DF4L - Implement proper advisor write permission check
            // For now, allow all logged-in users to edit GBU ID
            let can_edit_gbu_id = true;

            // Convert empty GBU ID strings to None
            let gbu_advisor_esid = match advisor_row.gbu_advisor_esid {
                Some(id) if !id.trim().is_empty() => Some(id),
                _ => None,
            };

            advisors.push(AdvisorInfo {
                advisor_uuid: advisor_row.advisor_uuid,
                advisor_esid: advisor_row.advisor_esid,
                first_name: advisor_row.first_name,
                last_name: advisor_row.last_name,
                email: advisor_row.email,
                phone: advisor_row.phone,
                gbu_advisor_esid,
                address,
                city: advisor_row.city,
                state: advisor_row.state,
                zip: advisor_row.zip,
                states_of_licensure,
                can_edit_gbu_id,
            });
        }

        Ok(Output {
            name,
            email,
            username,
            mfa_email_enabled,
            mfa_backup_codes_enabled,
            last_login,
            password_last_changed,
            login_attempts_today: login_attempts,
            advisors,
        })
    }
}
