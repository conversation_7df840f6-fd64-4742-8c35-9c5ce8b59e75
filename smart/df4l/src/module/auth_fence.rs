use crate::module::df4l_gbu::OpenIDGBUProvider;
use auth_fence::types::AuthProviderType;
use std::ops::{Deref, DerefMut};

impl auth_fence::App for crate::AppStruct {
    fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str {
        match next_uri {
            Some(next_uri) => next_uri,
            None => "/dashboard",
        }
    }
    fn auth_fence_system(&self) -> &auth_fence::types::ModuleStruct {
        &self.auth_fence
    }
}

impl auth_fence::Identity for crate::IdentityStruct {
    fn is_logged_in(&self) -> bool {
        self.auth_fence.is_some()
    }
    fn identity_uuid(&self) -> Option<granite::Uuid> {
        match &self.auth_fence {
            Some(auth_fence) => Some(auth_fence.identity_uuid),
            None => None,
        }
    }
    fn remote_address(&self) -> std::net::IpAddr {
        self.request.remote_address
    }
    fn session_token(&self) -> String {
        self.request.session_token.clone()
    }
}

#[derive(Debug)]
pub struct ModuleStruct(auth_fence::types::ModuleStruct);

impl Deref for ModuleStruct {
    type Target = auth_fence::types::ModuleStruct;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl DerefMut for ModuleStruct {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

impl approck::Module for ModuleStruct {
    type Config = auth_fence::types::ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        // Handles common providers (Google, Microsoft, etc.)
        let mut system = auth_fence::types::ModuleStruct::new(config.clone())?;

        for provider in config.openid.values() {
            // NOTE: BaseOpenIDConfig - A problem if the provider has custom fields in the config
            // TODO: Need to recognize custom provider configs in deserialize taking place in lib.rs
            if let auth_fence::types::OpenIDConfig::Custom(base_config) = provider {
                if base_config.key == "gbu" {
                    system.register_provider(AuthProviderType::OpenID(Box::new(
                        OpenIDGBUProvider {
                            config: base_config.clone(),
                        },
                    )));
                }
            }
        }

        Ok(ModuleStruct(system))
    }

    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}
