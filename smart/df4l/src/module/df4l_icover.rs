use crate::{AppStruct, IdentityStruct};

impl df4l_icover::App for AppStruct {
    fn auth_fence_provider(&self) -> &auth_fence_provider::ModuleStruct {
        &self.auth_fence_provider
    }
    fn take_me_to_icover_client_url(&self, client_uuid: granite::Uuid) -> String {
        format!(
            "https://gbu-local-development.icoverdemo.com/?client_uuid={}",
            client_uuid
        )
    }
}

impl df4l_icover::Identity for IdentityStruct {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
    fn scope_d2c_read(&self) -> bool {
        match &self.auth_fence_provider {
            Some(auth_fence_provider) => auth_fence_provider.scope_d2c_read,
            None => false,
        }
    }
}
