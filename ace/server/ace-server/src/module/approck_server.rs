use approck::server::tls::{CertificateRequestState, TlsCertificateKeyPemPair};
use ace_graph::GraphKeyExt;
use granite::ResultExt;

impl approck::server::App for crate::AppStruct {
    fn webserver_system(&self) -> &approck::server::Module {
        &self.webserver
    }

    async fn handle_api(
        &'static self,
        identity: &std::sync::Arc<Self::Identity>,
        api: &str,
        input: granite::JsonValue,
    ) -> granite::JsonValue {
        crate::libλ::api(self, identity, api, input).await
    }

    async fn webserver_route(
        &'static self,
        req: approck::server::Request,
    ) -> granite::Result<approck::server::response::Response> {
        crate::libλ::router(self, req).await
    }

    async fn request_certificate(
        &self,
        server_name: &str,
    ) -> granite::Result<CertificateRequestState> {
        let app = self.zero_system.ace_core_app;

        let ace2_server_name = match ace_graph::config::get(
            &ace_graph::Config::EtcConfig,
            &app.ace_db_app,
        )
        .await
        {
            Ok(config) => config.ace2_server_name,
            Err(e) => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .add_context(e.to_string()))
            }
        };

        if server_name == ace2_server_name {
            // load the certificate from the filesystem
            let tlscert_path = app.ace_db_app.data_path.join(format!(
                "tls.{}.cert.pem",
                ace_graph::TlsCert::Ace.serialize_dashed()
            ));
            let tlscert_key_path = app.ace_db_app.data_path.join(format!(
                "tls.{}.key.pem",
                ace_graph::TlsCert::Ace.serialize_dashed()
            ));

            if !tlscert_path.exists() {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .add_context(format!("TLS Certificate file not found: {tlscert_path:?}")));
            }

            let cert_contents = std::fs::read_to_string(&tlscert_path).amend(|e| {
                e.add_context(format!("Error reading {}", &tlscert_path.display()))
            })?;
            let key_contents = std::fs::read_to_string(&tlscert_key_path).amend(|e| {
                e.add_context(format!("Error reading {}", &tlscert_key_path.display()))
            })?;

            let pair = TlsCertificateKeyPemPair {
                cert_pem: cert_contents,
                key_pem: key_contents,
            }
            .try_into_key_pair()
            .amend(|e| e.add_context("Error parsing TLS certificate".to_string()))?;

            return Ok(CertificateRequestState::Ready(pair.into()));
        }

        // default is None available
        Ok(approck::server::tls::CertificateRequestState::None)
    }

}
