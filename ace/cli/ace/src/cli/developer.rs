use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};
use error_stack::{Result, ResultExt};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    InvalidGraphKey(String, String),
    GetDeveloper,
}
pub async fn info(gk_developer: &str, ace_db_app: &ace_db::App) -> Result<(), ErrorStack> {
    let gk_developer = ace_graph::Developer::deserialize(gk_developer)
        .map_err(|e| ErrorStack::InvalidGraphKey(gk_developer.to_owned(), e.to_string()))?;

    let developer = ace_graph::developer::get(&gk_developer, ace_db_app)
        .await
        .change_context(ErrorStack::GetDeveloper)?;

    println!("Developer Summary:");
    println!("Name:                   {}", developer.name);
    println!("Graph Key:              {}", developer.graphkey.serialize());
    println!("Region:                 {}", developer.region);
    println!("Subnet:                 {}", developer.subnet);
    println!(
        "Active:                 {}",
        if developer.active { "Yes" } else { "No" }
    );

    println!("\nNetwork Details:");
    println!("Public Hostname:        {}", developer.public_hostname);
    println!("Private Hostname:       {}", developer.private_hostname);
    println!(
        "Connection:             ssh app@{}",
        developer.private_hostname
    );

    println!("\nInstance Configuration:");
    println!("Instance Type:          {}", developer.instance_type);
    println!("Volume Size:            {}", developer.volume_size);

    println!("\nAccess Details:");
    println!("Base Endpoint:          {}", developer.base_endpoint);
    if let Some(ssh_key) = &developer.ssh_public_key {
        println!("SSH Public Key:         {}", ssh_key);
    } else {
        println!("SSH Public Key:         None");
    }

    if !developer.applications.is_empty() {
        println!("\nApplications:");
        for app in &developer.applications {
            println!("  - {}", app);
        }
    } else {
        println!("\nApplications: None");
    }

    Ok(())
}
pub async fn ls(verbose: bool, ace_db_app: &ace_db::App) {
    let developers_result =
        ace_graph::developer::select_result(&ace_graph::DeveloperFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut developers_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid developers
    match developers_result {
        Ok(developers) => {
            for developer in developers {
                match developer {
                    Ok(developer) => {
                        developers_sorted.push(developer);
                    }
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching developers: {:#?}", e);
            return;
        }
    }

    // Sort developers by their graphkey
    developers_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for developer in developers_sorted {
            println!("\n{:#?}", developer);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Name",
            "Graphkey",
            "Subnet",
            "Public Hostname",
            "Private Hostname",
            "Active",
        ]);

        for developer in developers_sorted {
            table.add_row(vec![
                developer.name,
                developer.graphkey.serialize(),
                developer.subnet.variant_to_name(),
                developer.public_hostname,
                developer.private_hostname,
                if developer.active {
                    "Yes".to_string()
                } else {
                    "No".to_string()
                },
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
