#![allow(clippy::unused_async)]
#![allow(clippy::no_effect_underscore_binding)]
#![allow(clippy::module_name_repetitions)]
pub mod ami;
pub mod app;
pub mod asset;
pub mod aws_account;
pub mod aws_ec_sls;
pub mod aws_elb;
pub mod aws_elb_tg;
pub mod aws_elb_tg_attach;
pub mod aws_rds;
pub mod aws_rds_sng;
pub mod aws_vpc_sg;
pub mod aws_vpc_sgr;
pub mod brdst;
pub mod brsrc;
pub mod bucket;
pub mod bucket_policy;
pub mod bucket_pub_access_blk;
pub mod bucket_repl_config;
pub mod bucket_vers;
pub mod config;
pub mod developer;
pub mod developer_app;
pub mod dns_rec;
pub mod dns_zone;
pub mod docker;
pub mod domain;
pub mod ecr;
pub mod ecr_public;
pub mod eip;
pub mod eip_assoc;
pub mod host;
pub mod iam_access_key;
pub mod iam_role;
pub mod iam_user;
pub mod igw;
pub mod ins;
pub mod ins_profile;
pub mod keypair;
pub mod lf;
pub mod mediaproctor;
pub mod mysqldevimg;
pub mod nat_gateway;
pub mod peercon;
pub mod pkr;
pub mod rt;
pub mod rt_assoc;
pub mod rtr;
pub mod sn;
pub mod tls_privkey;
pub mod tlscert;
pub mod user;
pub mod vpc;
pub mod vpn_client;

use ace_proc::GraphKeyDerive;
use error_stack::ResultExt;

pub trait GraphKeyName {
    fn get_name(&self) -> String;
}

pub trait GraphKeyExt {
    /// Will convert to a string such as `a(b(c,d))` depending on how nested the `GraphKey` is
    fn serialize(&self) -> String;

    fn serialize_inner(&self) -> String;

    /// Will fist serialize, and then convert to a safe dashed string such as `a-b-c-d` for use in cloud IDs
    fn serialize_dashed(&self) -> String {
        let re_non_alphanumeric: regex::Regex = regex::Regex::new(r"[^a-zA-Z0-9]+").unwrap();

        let s = self.serialize();
        let s = re_non_alphanumeric.replace_all(&s, "-");
        // trim dashes off the ends
        let s = s.trim_matches('-');
        s.to_string()
    }

    /// Will convert from a string such as `a(b(c,d))` into a `GraphKey` or return a `String` error
    fn deserialize(s: &str) -> core::result::Result<Self, String>
    where
        Self: std::marker::Sized;

    /// A private method used by deserialize to parse the inner `GraphKey`
    fn deserialize_inner(
        tokenator: &mut crate::parser::TokenIterator,
    ) -> core::result::Result<Self, String>
    where
        Self: std::marker::Sized;
}

pub trait GraphValueExt {}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "gk"]
pub enum GraphKey {
    #[graphkey = "ami"]
    Ami(Ami),

    #[graphkey = "app"]
    App(App),

    #[graphkey = "aws-acct"]
    AwsAccount(AwsAccount),

    #[graphkey = "aws-ec-sls"]
    AwsEcSls(AwsEcSls),

    #[graphkey = "aws-elb"]
    AwsElb(AwsElb),

    #[graphkey = "aws-elb-tg"]
    AwsElbTg(AwsElbTg),

    #[graphkey = "aws-elb-tga"]
    AwsElbTgAttach(AwsElbTgAttach),

    #[graphkey = "aws-rds"]
    AwsRds(AwsRds),

    #[graphkey = "aws-rds-sng"]
    AwsRdsSng(AwsRdsSng),

    #[graphkey = "aws-vpc-sg"]
    AwsVpcSg(AwsVpcSg),

    #[graphkey = "aws-vpc-sgr"]
    AwsVpcSgRule(AwsVpcSgRule),

    #[graphkey = "asset"]
    Asset(Asset),

    #[graphkey = "brdst"]
    BrDst(Brdst),

    #[graphkey = "brsrc"]
    BrSrc(Brsrc),

    #[graphkey = "bucket"]
    Bucket(Bucket),

    #[graphkey = "bucket-policy"]
    BucketPolicy(BucketPolicy),

    #[graphkey = "bucket-pub-access-blk"]
    BucketPublicAccessBlock(BucketPublicAccessBlock),

    #[graphkey = "bucket-repl-conf"]
    BucketReplicationConfig(BucketReplicationConfig),

    #[graphkey = "bucket-vers"]
    BucketVersioning(BucketVersioning),

    #[graphkey = "cfg"]
    Config(Config),

    #[graphkey = "dev"]
    Developer(Developer),

    #[graphkey = "devapp"]
    DeveloperApp(DeveloperApp),

    #[graphkey = "dnsrec"]
    DnsRecord(DnsRecord),

    #[graphkey = "dnszone"]
    DnsZone(DnsZone),

    #[graphkey = "docker"]
    Docker(Docker),

    #[graphkey = "domain"]
    Domain(Domain),

    #[graphkey = "ecr"]
    Ecr(Ecr),

    #[graphkey = "ecr-public"]
    EcrPublic(EcrPublic),

    #[graphkey = "eip-assoc"]
    EipAssoc(EipAssoc),

    #[graphkey = "eip"]
    ElasticIp(ElasticIp),

    #[graphkey = "host"]
    Host(Host),

    #[graphkey = "iam-access-key"]
    IamAccessKey(IamAccessKey),

    #[graphkey = "iam-role"]
    IamRole(IamRole),

    #[graphkey = "iam-user"]
    IamUser(IamUser),

    #[graphkey = "ins"]
    Instance(Instance),

    #[graphkey = "ins-profile"]
    InstanceProfile(InstanceProfile),

    #[graphkey = "igw"]
    InternetGateway(InternetGateway),

    #[graphkey = "keypair"]
    KeyPair(KeyPair),

    #[graphkey = "lf"]
    LocalFile(LocalFile),

    #[graphkey = "mp"]
    MediaProctor(MediaProctor),

    #[graphkey = "mysqldevimg"]
    MysqlDevImg(MysqlDevImg),

    #[graphkey = "natgat"]
    NatGateway(NatGateway),

    #[graphkey = "pkr"]
    Packer(Packer),

    #[graphkey = "peercon"]
    Peercon(Peercon),

    #[graphkey = "rt"]
    RouteTable(RouteTable),

    #[graphkey = "rtr-assoc"]
    RouteTableAssoc(RouteTableAssoc),

    #[graphkey = "rtr"]
    RouteTableRoute(RouteTableRoute),

    #[graphkey = "sn"]
    Subnet(Subnet),

    #[graphkey = "tlscert"]
    TlsCert(TlsCert),

    #[graphkey = "user"]
    User(User),

    #[graphkey = "tlsprivkey"]
    TlsPrivateKey(TlsPrivateKey),

    #[graphkey = "vpc"]
    Vpc(Vpc),

    #[graphkey = "vpn"]
    Vpn(Vpn),

    #[graphkey = "vpn-client"]
    VpnClient(VpnClient),
}

pub enum AmiFilter {
    All,
    AllLatest,
    One(Ami),
    None,
}

pub enum AppFilter {
    All,
    One(App),
    None,
}

pub enum AwsAccountFilter {
    All,
    One(AwsAccount),
    None,
}

pub enum AwsEcSlsFilter {
    All,
    One(AwsEcSls),
    None,
}

pub enum AwsElbFilter {
    All,
    One(AwsElb),
    None,
}

pub enum AwsElbTgFilter {
    All,
    One(AwsElbTg),
    None,
}

pub enum AwsRdsFilter {
    All,
    One(AwsRds),
    None,
}

pub enum AwsRdsSngFilter {
    All,
    One(AwsRdsSng),
    None,
}

pub enum AwsVpcSecurityGroupFilter {
    All,
    One(AwsVpcSg),
    None,
}

pub enum AssetFilter {
    All,
    None,

    /// All newest assets of any name, any target
    AllLatest,

    /// Newest assets of specific name, any target
    AllLatestSpecificAsset(Asset),

    /// Newest assets of specific target, any name
    AllLatestSpecificTarget(String),

    /// All assets of specific name
    AllSpecificAssetName(Asset),

    /// All assets of specific target
    AllSpecificTarget(String),

    /// All assets of specific version
    AllSpecificVersion(String),

    // Latest specific asset class + target?
    LatestSpecificAssetAndTarget(Asset),

    /// Exact name, target, version
    OneExactAsset(Asset),
}

impl From<&BucketFilter> for AppFilter {
    fn from(filter: &BucketFilter) -> Self {
        match filter {
            BucketFilter::All => AppFilter::All,
            BucketFilter::One(bucket_graphkey) => match bucket_graphkey {
                Bucket::App(app, _purpose) => AppFilter::One(app.to_owned()),
                Bucket::Ace => AppFilter::None,
            },
            BucketFilter::None => AppFilter::None,
        }
    }
}

impl From<&AwsVpcSecurityGroupFilter> for InstanceFilter {
    fn from(filter: &AwsVpcSecurityGroupFilter) -> Self {
        match filter {
            AwsVpcSecurityGroupFilter::All => InstanceFilter::All,
            AwsVpcSecurityGroupFilter::One(sg_gk) => match sg_gk {
                AwsVpcSg::Instance(instance) => InstanceFilter::One(instance.to_owned()),
                _ => InstanceFilter::None,
            },
            AwsVpcSecurityGroupFilter::None => InstanceFilter::None,
        }
    }
}

pub enum AwsVpcSecurityGroupRuleFilter {
    All,
    One(AwsVpcSgRule),
    None,
}

pub enum BrdstFilter {
    All,
    One(Brdst),
    None,
}

pub enum BrsrcFilter {
    All,
    One(Brsrc),
    None,
}

pub enum BucketFilter {
    All,
    One(Bucket),
    None,
}

pub enum BucketPolicyFilter {
    All,
    One(BucketPolicy),
    None,
}

pub enum BucketPublicAccessBlockFilter {
    All,
    One(BucketPublicAccessBlock),
    None,
}

pub enum BucketReplicationConfigFilter {
    All,
    One(BucketReplicationConfig),
    None,
}

pub enum BucketVersioningFilter {
    All,
    One(BucketVersioning),
    None,
}

pub enum DeveloperFilter {
    All,
    One(Developer),
    None,
}

pub enum DeveloperAppFilter {
    All,
    One(DeveloperApp),
    None,
}

pub enum DnsRecFilter {
    All,
    One(DnsRecord),
    None,
}

#[derive(Debug, Clone)]
pub enum DnsZoneFilter {
    All,
    One(DnsZone),
    None,
}

impl std::fmt::Display for DnsZoneFilter {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DnsZoneFilter::All => write!(f, "DnsZoneFilter::All"),
            DnsZoneFilter::One(zone) => write!(f, "DnsZoneFilter::One::{}", zone.serialize()),
            DnsZoneFilter::None => write!(f, "DnsZoneFilter::None"),
        }
    }
}

pub enum DockerFilter {
    All,
    One(Docker),
    None,
}

pub enum DomainFilter {
    All,
    One(Domain),
    None,
}

pub enum EcrFilter {
    All,
    One(Ecr),
    None,
}

impl From<&EcrFilter> for DockerFilter {
    fn from(filter: &EcrFilter) -> Self {
        match filter {
            EcrFilter::All => DockerFilter::All,
            EcrFilter::One(ecr_graphkey) => match ecr_graphkey {
                Ecr::App(_, _) => DockerFilter::None,
                Ecr::AppPreview(_, _) => DockerFilter::None,
                Ecr::Docker(docker) => DockerFilter::One(docker.to_owned()),
                Ecr::Db(_) => DockerFilter::None,
            },
            EcrFilter::None => DockerFilter::None,
        }
    }
}

pub enum EcrPublicFilter {
    All,
    One(EcrPublic),
    None,
}

impl From<&EcrPublicFilter> for DockerFilter {
    fn from(filter: &EcrPublicFilter) -> Self {
        match filter {
            EcrPublicFilter::All => DockerFilter::All,
            EcrPublicFilter::One(ecr_graphkey) => match ecr_graphkey {
                EcrPublic::Docker(docker) => DockerFilter::One(docker.to_owned()),
                EcrPublic::Db(_) => DockerFilter::None,
            },
            EcrPublicFilter::None => DockerFilter::None,
        }
    }
}

pub enum EipAssocFilter {
    All,
    One(EipAssoc),
    None,
}

pub enum EipFilter {
    All,
    One(ElasticIp),
    None,
}

pub enum HostFilter {
    All,
    One(Host),
    None,
}

pub enum IamAccessKeyFilter {
    All,
    One(IamAccessKey),
    None,
}

pub enum IamPolicyFilter {
    All,
    One(IamPolicy),
    None,
}

pub enum IamRoleFilter {
    All,
    One(IamRole),
    None,
}

pub enum IamUserFilter {
    All,
    One(IamUser),
    None,
}

#[derive(Debug)]
pub enum InstanceFilter {
    All,
    One(Instance),
    None,
}

impl From<&InstanceFilter> for AppFilter {
    fn from(filter: &InstanceFilter) -> Self {
        match filter {
            InstanceFilter::All => AppFilter::All,
            InstanceFilter::One(instance_graphkey) => match instance_graphkey {
                Instance::Ace => AppFilter::None,
                Instance::App(app) => AppFilter::One(app.to_owned()),
                Instance::AppPreview(app) => AppFilter::One(app.to_owned()),
                Instance::Developer(_) => AppFilter::None,
                Instance::Db(_) => AppFilter::None,
                Instance::Graylog => AppFilter::None,
                Instance::Vpn => AppFilter::None,
                Instance::Deploy => AppFilter::None,
            },
            InstanceFilter::None => AppFilter::None,
        }
    }
}

impl From<&InstanceFilter> for DeveloperFilter {
    fn from(filter: &InstanceFilter) -> Self {
        match filter {
            InstanceFilter::All => DeveloperFilter::All,
            InstanceFilter::One(Instance::Developer(developer)) => {
                DeveloperFilter::One(developer.to_owned())
            }
            _ => DeveloperFilter::None,
        }
    }
}

pub enum InstanceProfileFilter {
    All,
    One(InstanceProfile),
    None,
}

pub enum IgwFilter {
    All,
    One(InternetGateway),
    None,
}

pub enum KeyPairFilter {
    All,
    One(KeyPair),
    None,
}

impl From<&KeyPairFilter> for DeveloperFilter {
    fn from(filter: &KeyPairFilter) -> Self {
        match filter {
            KeyPairFilter::All => DeveloperFilter::All,
            KeyPairFilter::One(key_graphkey) => match key_graphkey {
                KeyPair::Developer(developer) => DeveloperFilter::One(developer.to_owned()),
                _ => DeveloperFilter::None,
            },
            KeyPairFilter::None => DeveloperFilter::None,
        }
    }
}

pub enum LocalFileFilter {
    All,
    One(LocalFile),
    None,
}

pub enum MediaProctorFilter {
    All,
    One(MediaProctor),
    None,
}

pub enum MysqlDevImgFilter {
    All,
    One(MysqlDevImg),
    None,
}

pub enum NatGatewayFilter {
    All,
    One(NatGateway),
    None,
}

pub enum PackerFilter {
    All,
    One(Packer),
    None,
}

pub enum PeerconFilter {
    All,
    One(Peercon),
    None,
}

pub enum RouteTableFilter {
    All,
    One(RouteTable),
    None,
}

pub enum RouteTableAssocFilter {
    All,
    One(RouteTableAssoc),
    None,
}

pub enum RouteTableRouteFilter {
    All,
    One(RouteTableRoute),
    None,
}

pub enum SubnetFilter {
    All,
    One(Subnet),
    None,
}

pub enum TlsCertFilter {
    All,
    One(TlsCert),
    Ace,
    Developer(Developer),
    DeveloperAndPurpose(Developer, String),
    App(App),
    AppAndPurpose(App, String),
    None,
}

impl From<&TlsCertFilter> for AppFilter {
    fn from(filter: &TlsCertFilter) -> Self {
        match filter {
            TlsCertFilter::All => AppFilter::All,
            TlsCertFilter::One(TlsCert::App(app, _purpose)) => AppFilter::One(app.to_owned()),
            TlsCertFilter::App(app) | TlsCertFilter::AppAndPurpose(app, _) => {
                AppFilter::One(app.to_owned())
            }
            _ => AppFilter::None,
        }
    }
}

// implement TlsCertFilter into DeveloperFilter
impl From<&TlsCertFilter> for DeveloperFilter {
    fn from(filter: &TlsCertFilter) -> Self {
        match filter {
            TlsCertFilter::All => DeveloperFilter::All,
            TlsCertFilter::One(TlsCert::Developer(developer, _purpose)) => {
                DeveloperFilter::One(developer.to_owned())
            }
            TlsCertFilter::Developer(developer) => DeveloperFilter::One(developer.to_owned()),
            TlsCertFilter::DeveloperAndPurpose(developer, _) => {
                DeveloperFilter::One(developer.to_owned())
            }
            _ => DeveloperFilter::None,
        }
    }
}

pub enum TlsPrivateKeyFilter {
    All,
    One(TlsPrivateKey),
    None,
}

pub enum UserFilter {
    All,
    One(User),
    None,
}

pub enum VpnFilter {
    All,
    One(Vpn),
    None,
}

pub enum VpnClientFilter {
    All,
    One(VpnClient),
    None,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-acct"]
pub enum AwsAccount {
    // (account_key)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "ami"]
pub enum Ami {
    /// (Packer)
    /// This ALWAYS means "LATEST" from Packer for a given type
    #[graphkey = "pkr"]
    Packer(Packer),

    /// (Packer, packer_run_uuid)
    /// This references a specific packer run
    #[graphkey = "pkrmfst"]
    PackerManifest(Packer, String),

    /// (etc/ami.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "app"]
pub enum App {
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-az"]
pub enum AwsAz {
    #[graphkey = "public-a"]
    PublicA,

    #[graphkey = "public-b"]
    PublicB,

    #[graphkey = "public-c"]
    PublicC,

    #[graphkey = "private-a"]
    PrivateA,

    #[graphkey = "private-b"]
    PrivateB,

    #[graphkey = "private-c"]
    PrivateC,

    #[graphkey = "ace"]
    Ace,

    #[graphkey = "temporal"]
    Temporal,

    #[graphkey = "vpn"]
    Vpn,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-ec-sls"]
pub enum AwsEcSls {
    #[graphkey = "app"]
    /// App, Purpose
    App(App, String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-vpc-sg"]
pub enum AwsVpcSg {
    /// Derived from an AwsElasticacheServerless
    #[graphkey = "aws-ec-sls"]
    AwsEcSls(AwsEcSls),

    #[graphkey = "aws-elb"]
    AwsElb(AwsElb),

    #[graphkey = "aws-rds"]
    AwsRds(AwsRds),

    /// (etc/aws-vpc-sg.{name}.toml)
    #[graphkey = "db"]
    Db(String),

    /// Derived from an Instance
    #[graphkey = "ins"]
    Instance(Instance),

    #[graphkey = "mp"]
    /// The SINGULAR mediaproctor security group
    MediaProctor,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-vpc-sgr"]
pub enum AwsVpcSgRule {
    /// (Owner, RuleKey)
    #[graphkey = "aws-vpc-sg"]
    AwsVpcSecurityGroup(AwsVpcSg, String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "asset"]
pub enum Asset {
    #[graphkey = "ace-agent"]
    /// (asset/ace-agent-version-target-hash)
    /// (target, version)
    AceAgent(String, String),

    #[graphkey = "ace-agent-updater"]
    /// (asset/ace-agent-updater-version-target-hash)
    /// (target, version)
    AceAgentUpdater(String, String),

    #[graphkey = "mp-process"]
    /// (asset/mp-process-version-target-hash)
    /// (target, version)
    MediaproctorProcess(String, String),

    #[graphkey = "mp-stream"]
    /// (asset/mp-stream-version-target-hash)
    /// (target, version)
    MediaproctorStream(String, String),
}

impl Asset {
    pub fn name(&self) -> String {
        match self {
            Asset::AceAgent(..) => "ace-agent".to_string(),
            Asset::AceAgentUpdater(..) => "ace-agent-updater".to_string(),
            Asset::MediaproctorProcess(..) => "mp-process".to_string(),
            Asset::MediaproctorStream(..) => "mp-stream".to_string(),
        }
    }

    pub fn target(&self) -> String {
        match self {
            Asset::AceAgent(target, _version) => target.to_string(),
            Asset::AceAgentUpdater(target, _version) => target.to_string(),
            Asset::MediaproctorProcess(target, _version) => target.to_string(),
            Asset::MediaproctorStream(target, _version) => target.to_string(),
        }
    }

    pub fn version(&self) -> String {
        match self {
            Asset::AceAgent(_target, version) => version.to_string(),
            Asset::AceAgentUpdater(_target, version) => version.to_string(),
            Asset::MediaproctorProcess(_target, version) => version.to_string(),
            Asset::MediaproctorStream(_target, version) => version.to_string(),
        }
    }
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "brsrc"]
pub enum Brsrc {
    /// (etc/brsrc.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "brdst"]
pub enum Brdst {
    /// (etc/brdst.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "bucket"]
pub enum Bucket {
    /// (App, Purpose)
    #[graphkey = "app"]
    App(App, String),

    /// The Ace Bucket
    #[graphkey = "ace"]
    Ace,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "bucket-policy"]
pub enum BucketPolicy {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "bucket-pub-access-blk"]
pub enum BucketPublicAccessBlock {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "bucket-repl-conf"]
pub enum BucketReplicationConfig {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "bucket-vers"]
pub enum BucketVersioning {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "cfg"]
pub enum Config {
    #[graphkey = "etc-config"]
    EtcConfig,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "dev"]
pub enum Developer {
    /// (etc/developer.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "devapp"]
pub enum DeveloperApp {
    /// Derived from a Developer
    #[graphkey = "db"]
    Db(Developer, App),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "dnsrec"]
pub enum DnsRecord {
    #[graphkey = "dnszone"]
    DnsZone(DnsZone),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "dnszone"]
pub enum DnsZone {
    #[graphkey = "ace-public-domain"]
    AcePublicDomain,

    #[graphkey = "ace-private-domain"]
    AcePrivateDomain,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "docker"]
pub enum Docker {
    /// (etc/docker.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "domain"]
pub enum Domain {
    /// (etc/domain.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "ecr"]
pub enum Ecr {
    /// Derived from an App
    #[graphkey = "app"]
    App(App, String),

    #[graphkey = "app-preview"]
    AppPreview(App, String),

    /// (etc/ecr.{name}.toml)
    #[graphkey = "db"]
    Db(String),

    /// Derived from a Docker
    #[graphkey = "docker"]
    Docker(Docker),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "ecr-public"]
pub enum EcrPublic {
    /// (etc/ecr-public.{name}.toml)
    #[graphkey = "ecr-public-etc"]
    Db(String),

    /// Derived from a Docker
    #[graphkey = "docker"]
    Docker(Docker),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "ecr-assoc"]
pub enum EipAssoc {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "eip"]
pub enum ElasticIp {
    /// (etc/account.toml elastic_ip)
    #[graphkey = "natgat"]
    NatGateway(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "host"]
pub enum Host {
    /// Instance-based host computer
    #[graphkey = "ins"]
    Instance(Instance),

    /// (etc/host.{name}.toml)
    #[graphkey = "db"]
    Db(String),
    // /// Your PC
    // #[graphkey = "workstation"]
    // Workstation(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "iam-access-key"]
pub enum IamAccessKey {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "iampolicy"]
pub enum IamPolicy {
    #[graphkey = "app"]
    App(App),

    #[graphkey = "dev"]
    Developer(Developer),

    #[graphkey = "standard"]
    Standard(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "iam-role"]
/// Do not create instance profiles for Brsrc.
pub enum IamRole {
    /// each app has a primary role
    #[graphkey = "app"]
    App(App),

    #[graphkey = "app-preview"]
    AppPreview(App),

    /// THIS DOES NOT CREATE INSTANCE PROFILE!!
    #[graphkey = "brsrc"]
    Brsrc(Brsrc),

    /// from config
    #[graphkey = "deploy"]
    Deploy,

    /// each developer has a primary role
    #[graphkey = "dev"]
    Developer(Developer),

    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "iam-user"]
pub enum IamUser {
    /// Ace has a master iam user
    #[graphkey = "ace"]
    Ace,

    /// (App)
    #[graphkey = "app"]
    App(App),

    /// (Developer)
    #[graphkey = "dev"]
    Developer(Developer),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "ins"]
pub enum Instance {
    /// Derived from the Ace config
    #[graphkey = "ace"]
    Ace,

    /// Derived from an App
    #[graphkey = "app"]
    App(App),

    /// Derived from an App that has [preview] in app toml
    #[graphkey = "app-preview"]
    AppPreview(App),

    /// Derived from the [deploy] in etc/config.toml
    #[graphkey = "deploy"]
    Deploy,

    /// Derived from a Developer
    #[graphkey = "dev"]
    Developer(Developer),

    /// Derived from the Graylog config
    #[graphkey = "graylog"]
    Graylog,

    /// Derived from the VPN config
    #[graphkey = "vpn"]
    Vpn,

    /// (etc/instance.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "ins-profile"]
pub enum InstanceProfile {
    #[graphkey = "iam-role"]
    /// Is created *CONDITIONALLY* from IamRole (see crate::iam_role.rs)
    IamRole(IamRole),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "igw"]
pub enum InternetGateway {
    #[graphkey = "vpc"]
    Vpc(Vpc),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "keypair"]
pub enum KeyPair {
    /// /data/ace.pub
    #[graphkey = "ace"]
    Ace,

    /// (etc/keypair.{name}.toml)
    #[graphkey = "db"]
    Db(String),

    /// (Developer Owner)
    #[graphkey = "dev"]
    Developer(Developer),

    /// (User Owner, Name)
    #[graphkey = "user"]
    User(User, String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "lf"]
pub enum LocalFile {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "mp"]
pub enum MediaProctor {
    /// (etc/mediaproctor.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "mysqldevimg"]
pub enum MysqlDevImg {
    /// (etc/mysqldevimg.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "natgat"]
pub enum NatGateway {
    #[graphkey = "private-a"]
    PrivateA,

    #[graphkey = "private-b"]
    PrivateB,

    #[graphkey = "private-c"]
    PrivateC,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "pkr"]
pub enum Packer {
    /// builtin
    #[graphkey = "ubuntu-24-04"]
    Ubuntu2404,

    /// builtin
    #[graphkey = "ubuntu-24-04-ace2"]
    Ubuntu2404Ace2,

    /// builtin
    #[graphkey = "ubuntu-24-04-devbox"]
    Ubuntu2404Devbox,

    /// builtin
    #[graphkey = "ubuntu-24-04-docker"]
    Ubuntu2404Docker,

    /// builtin
    #[graphkey = "ubuntu-24-04-openvpn"]
    Ubuntu2404Openvpn,

    /// builtin
    #[graphkey = "ubuntu-24-04-postgresql"]
    Ubuntu2404Postgres17,

    /// builtin
    #[graphkey = "ubuntu-22-04-ace2"]
    Ubuntu2204Ace2,

    /// builtin
    #[graphkey = "ubuntu-22-04-devbox"]
    Ubuntu2204Devbox,

    /// builtin
    #[graphkey = "ubuntu-22-04-docker"]
    Ubuntu2204Docker,

    /// builtin
    #[graphkey = "ubuntu-22-04-openvpn"]
    Ubuntu2204Openvpn,

    /// builtin
    #[graphkey = "ubuntu-22-04-postgresql"]
    Ubuntu2204Postgresql,

    /// builtin
    #[graphkey = "ubuntu-22-04-videoproc"]
    Ubuntu2204Videoproc,

    /// (etc/packer.{name}.toml)
    #[graphkey = "db"]
    Db(String),
}

impl Packer {
    #[must_use]
    pub fn variant_to_name(&self) -> String {
        match self {
            Packer::Ubuntu2404 => "ubuntu-24-04".to_string(),
            Packer::Ubuntu2404Ace2 => "ubuntu-24-04-ace2".to_string(),
            Packer::Ubuntu2404Devbox => "ubuntu-24-04-devbox".to_string(),
            Packer::Ubuntu2404Docker => "ubuntu-24-04-docker".to_string(),
            Packer::Ubuntu2404Openvpn => "ubuntu-24-04-openvpn".to_string(),
            Packer::Ubuntu2404Postgres17 => "ubuntu-24-04-postgresql".to_string(),
            Packer::Ubuntu2204Ace2 => "ubuntu-22-04-ace2".to_string(),
            Packer::Ubuntu2204Devbox => "ubuntu-22-04-devbox".to_string(),
            Packer::Ubuntu2204Docker => "ubuntu-22-04-docker".to_string(),
            Packer::Ubuntu2204Openvpn => "ubuntu-22-04-openvpn".to_string(),
            Packer::Ubuntu2204Postgresql => "ubuntu-22-04-postgresql".to_string(),
            Packer::Ubuntu2204Videoproc => "ubuntu-22-04-videoproc".to_string(),
            Packer::Db(name) => name.to_owned(),
        }
    }
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "peercon"]
pub enum Peercon {
    /// (etc/account.toml peering_connection_id)
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "rt"]
pub enum RouteTable {
    /// This is the ACE Public A routing table
    #[graphkey = "public-a"]
    PublicA,

    /// This is the ACE Public B routing table
    #[graphkey = "public-b"]
    PublicB,

    /// This is the ACE Public C routing table
    #[graphkey = "public-c"]
    PublicC,

    /// This is the ACE Private A routing table
    #[graphkey = "private-a"]
    PrivateA,

    /// This is the ACE Private B routing table
    #[graphkey = "private-b"]
    PrivateB,

    /// This is the ACE Private C routing table
    #[graphkey = "private-c"]
    PrivateC,

    /// This is the ACE VPN routing table
    #[graphkey = "vpn"]
    Vpn,

    /// This is the ACE ACE routing table
    #[graphkey = "ace"]
    Ace,

    /// This is the ACE Temporal routing table
    #[graphkey = "temporal"]
    Temporal,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "rtr-assoc"]
pub enum RouteTableAssoc {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "rtr"]
pub enum RouteTableRoute {
    /// (RouteTable, Name)
    #[graphkey = "rt"]
    RouteTable(RouteTable, String),

    #[graphkey = "peercon"]
    Peercon(RouteTable, Peercon),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "sn"]
pub enum Subnet {
    /// This is the ACE Public A subnet
    #[graphkey = "public-a"]
    PublicA,

    /// This is the ACE Public B subnet
    #[graphkey = "public-b"]
    PublicB,

    /// This is the ACE Public C subnet
    #[graphkey = "public-c"]
    PublicC,

    /// This is the ACE Private A subnet
    #[graphkey = "private-a"]
    PrivateA,

    /// This is the ACE Private B subnet
    #[graphkey = "private-b"]
    PrivateB,

    /// This is the ACE Private C subnet
    #[graphkey = "private-c"]
    PrivateC,

    /// This is the ACE VPN subnet
    #[graphkey = "vpn"]
    Vpn,

    /// This is the ACE ACE subnet
    #[graphkey = "ace"]
    Ace,

    /// This is the ACE Temporal subnet
    #[graphkey = "temporal"]
    Temporal,
}

impl Subnet {
    #[must_use]
    pub fn variant_to_name(&self) -> String {
        match self {
            Subnet::PublicA => "public-a".to_string(),
            Subnet::PublicB => "public-b".to_string(),
            Subnet::PublicC => "public-c".to_string(),
            Subnet::PrivateA => "private-a".to_string(),
            Subnet::PrivateB => "private-b".to_string(),
            Subnet::PrivateC => "private-c".to_string(),
            Subnet::Vpn => "vpn".to_string(),
            Subnet::Ace => "ace".to_string(),
            Subnet::Temporal => "temporal".to_string(),
        }
    }

    /// For use with translating new graphtype to old terraform resource names
    pub fn to_old_terraform_resource_suffix(&self) -> String {
        match &self {
            crate::Subnet::PublicA | crate::Subnet::PrivateA => "a".to_string(),
            crate::Subnet::PublicB | crate::Subnet::PrivateB => "b".to_string(),
            crate::Subnet::PublicC | crate::Subnet::PrivateC => "c".to_string(),
            crate::Subnet::Vpn => "vpn".to_string(),
            crate::Subnet::Ace => "ace".to_string(),
            crate::Subnet::Temporal => "temporal".to_string(),
        }
    }

    pub fn to_old_terraform_resource_name(&self) -> String {
        format!("sn-{}", self.to_old_terraform_resource_suffix())
    }
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "tlscert"]
pub enum TlsCert {
    /// The one and only AceServer
    #[graphkey = "ace"]
    Ace,

    /// (App Owner, Purpose)
    #[graphkey = "app"]
    App(App, String),

    #[graphkey = "app-preview"]
    AppPreview(App, String),

    /// (Developer Owner, Purpose)
    #[graphkey = "dev"]
    Developer(Developer, String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "tlsprivkey"]
pub enum TlsPrivateKey {
    /// TODO
    #[graphkey = "TODO"]
    TODO,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "user"]
pub enum User {
    /// (etc/user.{name}.toml) or from etc/account.toml
    #[graphkey = "db"]
    Db(String),
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "vpc"]
pub enum Vpc {
    #[graphkey = "ace"]
    Ace,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "vpn"]
pub enum Vpn {
    /// ACE server Vpn
    #[graphkey = "ace"]
    Ace,
}

#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "vpn-client"]
pub enum VpnClient {
    /// From 'ace vpn create-client <name>'
    #[graphkey = "manual"]
    Manual(Vpn, String),
}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CouldNotFindGitRepo,
    CouldNotLoadPathFromRepo,
    GlobError(glob::GlobError),
    GlobPatternError(glob::PatternError),
    InvalidDirectoryName(String),
    ParseError,
}

pub type AwsAccountKey = String;
pub type Region = String;

pub fn account_key_and_region() -> error_stack::Result<(AwsAccountKey, Region), ErrorStack> {
    let git2_repo =
        git2::Repository::discover(".").change_context(ErrorStack::CouldNotFindGitRepo)?;

    let path = match git2_repo.workdir() {
        Some(path) => path.to_path_buf(),
        None => {
            error_stack::bail!(ErrorStack::CouldNotLoadPathFromRepo)
        }
    };

    let basename = path.file_name().unwrap().to_string_lossy();

    // Bail out if dir does not have @
    if !basename.contains('@') {
        error_stack::bail!(ErrorStack::InvalidDirectoryName(basename.to_string()));
    }

    // split on first -
    let mut parts = basename.splitn(2, '@');
    let account_key = parts.next().unwrap_or("").to_string();
    let region = parts.next().unwrap_or("").to_string();

    Ok((account_key, region))
}

pub mod parser {

    #[derive(Debug)]
    pub enum Token {
        OpenParen,
        CloseParen,
        Comma,
        Ident(String),
        End,
    }

    #[derive(Debug)]
    pub struct TokenIterator(std::vec::IntoIter<Token>);

    impl TokenIterator {
        #[must_use]
        pub fn new(lexemes: Vec<Token>) -> Self {
            Self(lexemes.into_iter())
        }
        pub fn take_ident(&mut self) -> Result<String, String> {
            let value = self.0.next();
            match value {
                Some(Token::Ident(ident)) => Ok(ident),
                Some(value) => Err(format!("expected identifier; found {value:?}")),
                None => Err(format!("iterator exhausted; {self:?}")),
            }
        }
        pub fn take_open_paren(&mut self) -> Result<(), String> {
            let value = self.0.next();
            match value {
                Some(Token::OpenParen) => Ok(()),
                Some(value) => Err(format!("expected `(`; found {value:?}")),
                None => Err(format!("iterator exhausted; {self:?}")),
            }
        }
        pub fn take_close_paren(&mut self) -> Result<(), String> {
            let value = self.0.next();
            match value {
                Some(Token::CloseParen) => Ok(()),
                Some(value) => Err(format!("expected `)`; found {value:?}")),
                None => Err(format!("iterator exhausted; {self:?}")),
            }
        }
        pub fn take_comma(&mut self) -> Result<(), String> {
            let value = self.0.next();
            match value {
                Some(Token::Comma) => Ok(()),
                Some(value) => Err(format!("expected `,`; found {value:?}")),
                None => Err(format!("iterator exhausted; {self:?}")),
            }
        }
        pub fn take_end(&mut self) -> Result<(), String> {
            let value = self.0.next();
            match value {
                Some(Token::End) => Ok(()),
                Some(value) => Err(format!("expected end of input; found {value:?}")),
                None => Err(format!("iterator exhausted; {self:?}")),
            }
        }
    }

    pub fn lex(input: &str) -> Result<TokenIterator, String> {
        let mut chars = input.chars().peekable();
        let mut lexemes = Vec::new();
        let mut i = 1;

        loop {
            let char = chars.peek();

            // This is where we start a new token
            match char {
                // Skip whitespace
                Some(c) if c.is_whitespace() => {
                    chars.next();
                    i += 1;
                }
                // Open Paren
                Some(c) if *c == '(' => {
                    lexemes.push(Token::OpenParen);
                    chars.next();
                    i += 1;
                }
                // Close Paren
                Some(c) if *c == ')' => {
                    lexemes.push(Token::CloseParen);
                    chars.next();
                    i += 1;
                }
                // Comma
                Some(c) if *c == ',' => {
                    lexemes.push(Token::Comma);
                    chars.next();
                    i += 1;
                }
                // Ident is a string of [a-z0-9-] that starts with [a-z0-9], ends with a [a-z0-9], and has no consecutive dashes
                Some(c) if c.is_ascii_lowercase() || c.is_ascii_digit() => {
                    let mut ident = String::new();
                    // take the first character
                    ident.push(*c);
                    chars.next();
                    i += 1;
                    let mut require_az09 = false;
                    loop {
                        let c = chars.peek();
                        match (c, require_az09) {
                            // Any [a-z0-9] is allowed after the first character
                            (Some(c), _) if c.is_ascii_lowercase() || c.is_ascii_digit() => {
                                ident.push(*c);
                                chars.next();
                                i += 1;
                                require_az09 = false;
                            }
                            // Dashes are allowed if require_az09 is false
                            (Some(c), false) if *c == '-' => {
                                ident.push(*c);
                                chars.next();
                                i += 1;
                                require_az09 = true;
                            }
                            // If [a-z0-9] is required, then it's an error to encounter a different character
                            (Some(c), true) => {
                                return Err(format!(
                                    "unexpected '{c}' at character {i}; expected [a-z0-9]"
                                ));
                            }
                            // If the end is encountered and a [a-z0-9] is required, then it's an error
                            (None, true) => {
                                return Err(format!(
                                    "unexpected end of input at character {i}; expected [a-z0-9]"
                                ));
                            }
                            _ => {
                                break;
                            }
                        }
                    }
                    lexemes.push(Token::Ident(ident));
                }
                Some(char) => {
                    return Err(format!("Unexpected character `{char}` at position {i}"));
                }
                None => {
                    lexemes.push(Token::End);
                    chars.next();
                    break;
                }
            }
        }

        Ok(TokenIterator::new(lexemes))
    }
}

/// AWS ELB Network Load Balancer
#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-elb"]
pub enum AwsElb {
    #[graphkey = "app"]
    /// App, Purpose
    App(App, String),

    #[graphkey = "db"]
    /// (etc/aws-elb.{name}.toml)
    Db(String),
}

/// AWS ELB Target Group
#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-elb-tg"]
pub enum AwsElbTg {
    #[graphkey = "app"]
    /// Instance, Purpose
    App(App, String),
}

// AWS ELB Target Group Attachment
#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-elb-tga"]
pub enum AwsElbTgAttach {
    #[graphkey = "ins"]
    Ins(AwsElbTg, Instance),
}

/// AWS RDS Cluster
#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-rds"]
pub enum AwsRds {
    #[graphkey = "app"]
    /// App, Purpose
    App(App, String),
}

/// AWS RDS Subnet Group
#[derive(Clone, PartialEq, Eq, Hash, GraphKeyDerive)]
#[graphkey = "aws-rds-sng"]
pub enum AwsRdsSng {
    #[graphkey = "aws-rds"]
    AwsRds(AwsRds),
}
