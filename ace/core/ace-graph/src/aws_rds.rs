use error_stack::ResultExt;
use indexmap::IndexMap;

use crate::GraphKeyExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneAwsRdsFound(crate::AwsRds, usize),
    SelectFromApp,
}

#[derive(Debug)]
pub struct AwsRds {
    pub graphkey: crate::AwsRds,
    pub name: String,
    pub allocated_storage: u32,
    pub storage_type: String,
    pub instance_class: String,
    pub engine: String,
    pub engine_version: String,
    pub multi_az: bool,
    pub availability_zone: Option<crate::AwsAz>,
    pub preferred_maintenance_window: Option<String>,
    pub preferred_backup_window: Option<String>,
    pub username: String,
    pub password: String,
    pub parameter_family: String,
    pub parameters: IndexMap<String, String>,
    pub source: String,
}

impl crate::GraphValueExt for AwsRds {}

pub async fn get(
    gk_aws_rds_instance: crate::AwsRds,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<AwsRds, ErrorStack> {
    let filter = crate::AwsRdsFilter::One(gk_aws_rds_instance.clone());

    let mut items = select(filter, ace_db_app).await?;
    if items.len() == 1 {
        return Ok(items.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneAwsRdsFound(
        gk_aws_rds_instance,
        items.len()
    ));
}

pub async fn select(
    filter: crate::AwsRdsFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<AwsRds>, ErrorStack> {
    let mut rval = Vec::new();

    for items in select_result(filter, ace_db_app).await? {
        match items {
            Ok(aws_rds_instance) => rval.push(aws_rds_instance),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::AwsRdsFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<AwsRds, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    rval.extend(
        crate::app::select_result_aws_rds_instance(&filter, ace_db_app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromApp)),
    );

    Ok(rval)
}

pub async fn select_result_aws_rds_sng(
    filter: &crate::AwsRdsSngFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::aws_rds_sng::AwsRdsSng, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = vec![];

    let filter = match filter {
        crate::AwsRdsSngFilter::All => crate::AwsRdsFilter::All,
        crate::AwsRdsSngFilter::One(sg_gk) => match sg_gk {
            crate::AwsRdsSng::AwsRds(aws_rds_instance) => {
                crate::AwsRdsFilter::One(aws_rds_instance.clone())
            }
        },
        crate::AwsRdsSngFilter::None => return Ok(rval),
    };

    // iterate over mariadb dbs
    for mariadb in select_result(filter, ace_db_app).await? {
        match mariadb {
            Ok(mariadb) => {
                let graphkey = crate::AwsRdsSng::AwsRds(mariadb.graphkey.clone());
                let name = graphkey.serialize_dashed();

                rval.push(Ok(crate::aws_rds_sng::AwsRdsSng {
                    graphkey,
                    name,
                    description: None,
                    subnets: vec![
                        crate::Subnet::PrivateA,
                        crate::Subnet::PrivateB,
                        crate::Subnet::PrivateC,
                    ],
                    source: mariadb.source.clone(),
                }));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_aws_vpc_sg(
    filter: &crate::AwsVpcSecurityGroupFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::aws_vpc_sg::AwsVpcSecurityGroup, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = Vec::new();

    let filter = match filter {
        crate::AwsVpcSecurityGroupFilter::All => crate::AwsRdsFilter::All,
        crate::AwsVpcSecurityGroupFilter::One(sg_gk) => match sg_gk {
            crate::AwsVpcSg::AwsRds(aws_rds_instance) => {
                crate::AwsRdsFilter::One(aws_rds_instance.clone())
            }
            _ => return Ok(rval),
        },
        crate::AwsVpcSecurityGroupFilter::None => return Ok(rval),
    };

    for aws_rds_instance in select_result(filter, ace_db_app).await? {
        match aws_rds_instance {
            Ok(aws_rds_instance) => {
                match aws_rds_instance.engine.as_str() {
                    "mariadb" => {
                        rval.push(Ok(crate::aws_vpc_sg::AwsVpcSecurityGroup {
                            graphkey: crate::AwsVpcSg::AwsRds(aws_rds_instance.graphkey.clone()),
                            name: aws_rds_instance.name,
                            ingress: vec![
                                // Allow inbound MariaDB traffic from vpc
                                crate::ins::Ingress {
                                    ports: crate::ins::PortRange::One(3306),
                                    protocol: "tcp".to_string(),
                                    cidr_blocks: crate::ins::CidrBlocks::MainVpcCidrBlock,
                                },
                            ],
                            description: "Allow MariaDB inbound traffic".to_string(),
                            source: aws_rds_instance.graphkey.to_string(),
                        }));
                    }
                    "postgres" => {
                        rval.push(Ok(crate::aws_vpc_sg::AwsVpcSecurityGroup {
                            graphkey: crate::AwsVpcSg::AwsRds(aws_rds_instance.graphkey.clone()),
                            name: aws_rds_instance.name,
                            ingress: vec![
                                // Allow inbound MariaDB traffic from vpc
                                crate::ins::Ingress {
                                    ports: crate::ins::PortRange::One(5432),
                                    protocol: "tcp".to_string(),
                                    cidr_blocks: crate::ins::CidrBlocks::MainVpcCidrBlock,
                                },
                            ],
                            description: "Allow Postgres inbound traffic".to_string(),
                            source: aws_rds_instance.graphkey.to_string(),
                        }));
                    }
                    _ => {}
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}
