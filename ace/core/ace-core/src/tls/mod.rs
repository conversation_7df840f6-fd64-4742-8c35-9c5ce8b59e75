use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use std::path::PathBuf;

pub mod check_cert;
pub mod gen_cert;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CertificateNotFound(String),
    GenerateCertificate,
    GetTlsCerts,
}

pub type CertificateKey = String;

#[derive(Debug)]
pub struct TlsManager {
    // add a vec that is protected by a mutex
    cert_map: std::collections::HashMap<ace_graph::TlsCert, Certificate>,
}

#[derive(Debug)]
pub struct Certificate {
    pub graphkey: ace_graph::TlsCert,
    pub wildcard: bool,
    pub hosted_zone_key: String,
    pub cert: ace_graph::tlscert::TlsCert,
}

impl TlsManager {
    pub async fn new(app: &ace_db::App) -> error_stack::Result<Self, ErrorStack> {
        let mut me = TlsManager {
            cert_map: std::collections::HashMap::new(),
        };
        add_certificates(&mut me, app).await?;
        Ok(me)
    }

    pub fn get_list(&self) -> Vec<&Certificate> {
        let mut list: Vec<&Certificate> = Vec::new();

        for cert in self.cert_map.values() {
            list.push(cert);
        }

        list
    }

    pub fn add(
        &mut self,
        key: ace_graph::TlsCert,
        wildcard: bool,
        hosted_zone_key: String,
        cert: ace_graph::tlscert::TlsCert,
    ) {
        self.cert_map.insert(
            key.clone(),
            Certificate {
                graphkey: key,
                wildcard,
                hosted_zone_key,
                cert,
            },
        );
    }

    pub fn get_cert(
        &self,
        key: &ace_graph::TlsCert,
    ) -> error_stack::Result<&Certificate, ErrorStack> {
        match self.cert_map.get(key) {
            Some(cert) => Ok(cert),
            None => error_stack::bail!(ErrorStack::CertificateNotFound(key.to_string())),
        }
    }
}

impl Certificate {
    pub fn get_common_name(&self) -> String {
        self.cert.common_name.clone()
    }
    pub fn get_common_name_or_wildcard(&self) -> String {
        if self.wildcard {
            format!("*.{}", self.cert.common_name)
        } else {
            self.cert.common_name.clone()
        }
    }

    pub fn get_key_path(&self, app: &crate::Application) -> PathBuf {
        app.ace_db_app
            .data_path
            .join(format!("tls.{}.key.pem", self.graphkey.serialize_dashed()))
    }

    pub fn get_cert_path(&self, app: &crate::Application) -> PathBuf {
        app.ace_db_app
            .data_path
            .join(format!("tls.{}.cert.pem", self.graphkey.serialize_dashed()))
    }

    pub fn is_wildcard(&self) -> bool {
        self.wildcard
    }

    pub async fn generate(&self, app: &crate::Application) -> error_stack::Result<(), ErrorStack> {
        self::gen_cert::run(app, self)
            .await
            .change_context(ErrorStack::GenerateCertificate)?;
        Ok(())
    }

    pub async fn read_cert(&self, app: &crate::Application) -> granite::Result<String> {
        let cert_path = self.get_cert_path(app);
        let cert = match tokio::fs::read_to_string(&cert_path).await {
            Ok(cert) => cert,
            Err(e) => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .add_context(format!("Error reading {}:\n{e}", cert_path.display())));
            }
        };
        Ok(cert)
    }

    pub async fn read_key(&self, app: &crate::Application) -> granite::Result<String> {
        let key_path = self.get_key_path(app);
        let key = match tokio::fs::read_to_string(&key_path).await {
            Ok(key) => key,
            Err(e) => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .add_context(format!("Error reading {}:\n{e}", key_path.display())));
            }
        };
        Ok(key)
    }
}

pub async fn add_certificates(
    tls_manager: &mut TlsManager,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let tlscerts = ace_graph::tlscert::select(&ace_graph::TlsCertFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetTlsCerts)?;

    for tlscert in tlscerts {
        let (wildcard, hosted_zone_key) = match &tlscert.graphkey {
            ace_graph::TlsCert::Ace => (false, "ace-private-domain".to_string()),
            ace_graph::TlsCert::Developer(_dev, purpose) => {
                // TODO: This is so non-optimal, but we don't have the data to know what hosted zone to use
                if purpose.contains("pub") {
                    (true, "ace-public-domain".to_string())
                } else {
                    (true, "ace-private-domain".to_string())
                }
            }
            ace_graph::TlsCert::App(_app, purpose) => {
                if purpose.contains("pub") {
                    (true, "ace-public-domain".to_string())
                } else {
                    (false, "ace-private-domain".to_string())
                }
            }
            ace_graph::TlsCert::AppPreview(_app, purpose) => {
                if purpose.contains("pub") {
                    (true, "ace-public-domain".to_string())
                } else {
                    (false, "ace-private-domain".to_string())
                }
            }
        };

        tls_manager.add(tlscert.graphkey.clone(), wildcard, hosted_zone_key, tlscert);
    }

    Ok(())
}
