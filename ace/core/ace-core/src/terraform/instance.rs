use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use granite::dash;

#[allow(dead_code)]
#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AceInstanceSelectedWhenAceConfigIsNone,
    AmiNotFound(String),
    GenerateInstallSSHKeyScript,
    GenerateOne,
    GetConfig,
    GetIamRole(String),
    GetInstances,
    VolumeSizeNotSpecified,
}

/// instance.tf is generated HERE.  ALL generated instances go in this file.
pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<(String, String)>, ErrorStack> {
    let mut rval = vec![];

    let mut sorted_instances = ace_graph::ins::select(&ace_graph::InstanceFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetInstances)?;

    // This prevents undeterministic ordering to the output
    sorted_instances.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    for instance in sorted_instances {
        let keypairs = generate_one(config, &instance, output, ace_db_app)
            .await
            .change_context(ErrorStack::GenerateOne)?;

        rval.extend(keypairs);
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(rval)
}

async fn generate_one(
    config: &ace_graph::config::Config,
    instance: &ace_graph::ins::Instance,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<(String, String)>, ErrorStack> {
    // Create local references for convenience below

    // Will automatically get the latest ami if it is from packer.manifest.json.
    let ami_id = ace_graph::ami::get(instance.ami_graphkey.clone(), ace_db_app)
        .await
        .change_context(ErrorStack::AmiNotFound(instance.ami_graphkey.serialize()))?
        .ami_id;

    let dashed_instance_name = dash(&instance.graphkey.map_name_to_terraform_resource_name());
    let account_key = &config.account_key;

    // TODO: Move security group resource mapping to internal method
    // (Requires use of ace_graph::SecurityGroup)
    let security_group_resource = if matches!(instance.graphkey, ace_graph::Instance::Developer(_))
    {
        dashed_instance_name.clone()
    } else {
        format!("{}_sg", dashed_instance_name)
    };

    // TODO: Move instance resource mapping to internal method.
    let instance_resource = {
        if matches!(instance.graphkey, ace_graph::Instance::Developer(_)) {
            dash(&instance.graphkey.map_name_to_terraform_resource_name())
        } else {
            format!(
                "{}_instance",
                dash(&instance.graphkey.map_name_to_terraform_resource_name())
            )
        }
    };

    // TODO: Move instance_profile resource mapping to internal gk method.
    let instance_profile_resource = match &instance.instance_profile_role {
        Some(iam_role_gk) => {
            // ** VERIFY IAMROLE EXISTS **

            if let ace_graph::Instance::Db(_) = &instance.graphkey {
                // This is already checked in ace_graph::ins::construct().
                // So we don't need to check it again.  The other variants need it.
            } else {
                let _role = ace_graph::iam_role::get(iam_role_gk.clone(), ace_db_app)
                    .await
                    .change_context(ErrorStack::GetIamRole(iam_role_gk.serialize()))?;
            }

            // Get the instance profile terraform resource name
            let instance_profile_gk = ace_graph::InstanceProfile::IamRole(iam_role_gk.clone());
            let instance_profile = ace_graph::ins_profile::InstanceProfile {
                graphkey: instance_profile_gk,
                name: "".to_string(),   // Not used for terraform_resource
                source: "".to_string(), // Not used for terraform_resource
            };
            let (ins_profile_resource, _ins_profile_name, _tags) =
                instance_profile.terraform_resource(config);
            Some(ins_profile_resource)
        }
        None => None,
    };

    let data_tf_outputs = generate_data_tf_kv_pairs(&instance.graphkey, &dashed_instance_name);

    let public_domain_resource = format!("{}_public_domain", dashed_instance_name);
    let public_domain_wildcard_resource =
        format!("{}_public_domain_wildcard", dashed_instance_name);
    let private_domain_resource = format!("{}_private_domain", dashed_instance_name);
    let private_domain_wildcard_resource =
        format!("{}_private_domain_wildcard", dashed_instance_name);

    let server_name = format!(
        "{}.{}.{}",
        instance.name, config.region, config.private_domain_name
    );

    let eip_resource = format!("{}_eip", dashed_instance_name);
    let eip_tag = format!("{}-{}-eip", account_key, dashed_instance_name);
    let eip_association_resource = if matches!(instance.graphkey, ace_graph::Instance::App(_)) {
        format!("{}_eip_association", dashed_instance_name)
    } else {
        format!("{}_eip", dashed_instance_name)
    };

    let instance_tag_name = server_name.clone();

    let install_keys_script =
        crate::gen_install_keys::generate_script(ace_db_app, account_key.to_owned())
            .await
            .change_context(ErrorStack::GenerateInstallSSHKeyScript)?;

    // Only the Ace instance, App, Developer, and DB instances get root_block_devices, which require a known volume size.
    // The rest of the instances do not get root_block_devices, so an invalid volume_size does not matter.
    let instance_volume_size = match &instance.volume_size {
        ace_graph::ins::VolumeSize::DeterminedByAws => {
            // Db, Developer and App instances HAVE to have a specific volume size specified.
            if matches!(
                instance.graphkey,
                ace_graph::Instance::App(_)
                    | ace_graph::Instance::Db(_)
                    | ace_graph::Instance::Developer(_)
            ) {
                error_stack::bail!(ErrorStack::VolumeSizeNotSpecified)
            }
            // Ace can only have it if the config is set.
            // (Ace instance shouldn't even be in the list if the config is not set, error here for compiler's sake)
            else if matches!(instance.graphkey, ace_graph::Instance::Ace) {
                let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app)
                    .await
                    .change_context(ErrorStack::GetConfig)?;

                if let Some(ace_config) = config.ace {
                    &ace_config.volume_size.clone()
                } else {
                    // Technically this should NEVER be able to happen.
                    error_stack::bail!(ErrorStack::AceInstanceSelectedWhenAceConfigIsNone);
                }
            } else {
                &0
            }
        }
        ace_graph::ins::VolumeSize::Specific(size) => size,
    };

    // The user_data script is the same for all currently-allowed instance types, except the ACE instance.
    #[rustfmt::skip]
    let user_data = CNSL!(r#"
        #!/bin/bash
        echo "Terraform: START USER DATA SCRIPT from Terraform"


        # set the hostname
        echo "#, shell_quote::sh::quote(format!("Terraform: Setting hostname to {}", &server_name)).to_string_lossy(), r#"
        hostnamectl set-hostname "#, shell_quote::sh::quote(&server_name).to_string_lossy(), r#"

        # Create app user
        echo "Terraform: Creating app user..."
        useradd -m -s /bin/bash app
        echo "app ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers.d/app

        # copy keys from ubuntu user to app user
        mkdir -p /home/<USER>/.ssh
        cp /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys
        "#, install_keys_script, r#"
        chown -R app:app /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        chmod 600 /home/<USER>/.ssh/authorized_keys
        "#, if matches!(instance.graphkey, ace_graph::Instance::Ace) {
            CNSL!(r"
                chmod 600 /home/<USER>/.ssh/authorized_keys2
                ")
        } else {"".to_string()}, r#"

        # Delete ubuntu user
        echo "Terraform: Deleting ubuntu user..."
        userdel -r ubuntu

        "#, if matches!(instance.graphkey, ace_graph::Instance::Ace) {
            CNSL!(r#"
                echo "Terraform: Creating postgresql user and database for app..."
                sudo -u postgres createuser -s app
                sudo -u postgres createdb -O app app
            "#)
        } else {"".to_string()}, r#"

        echo "Terraform: END USER DATA SCRIPT from Terraform"
        EOF123
        "#);

    #[rustfmt::skip]
    let wildcard_dns = if instance.wildcard_dns {
        CNSL!(r#"
            // Create a wildcard route53 record for the openvpn instance public ip
            resource "aws_route53_record" "#, JE!(public_domain_wildcard_resource), r#" {
                zone_id = aws_route53_zone.public-domain.zone_id
                name    = "#, JE!(format!("*.{}", &instance.name)), r#"
                type    = "A"
                ttl     = "300"
                records = ["#, format!("aws_instance.{}.public_ip", &instance_resource), r#"]
            }

            // Create a wildcard route53 record for the openvpn instance private ip
            resource "aws_route53_record" "#, JE!(private_domain_wildcard_resource), r#" {
                zone_id = aws_route53_zone.private-domain.zone_id
                name    = "#, JE!(format!("*.{}", &instance.name)), r#"
                type    = "A"
                ttl     = "300"
                records = ["#, format!("aws_instance.{}.private_ip", &instance_resource), r#"]
            }
        "#)
    }
    else {
        CNSL!(r#"
            // No wildcard route53 record for the openvpn instance public ip
        "#)
    };

    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"
        resource "aws_instance" "#, JE!(instance_resource), r#" {
            ami = "#, JE!(&ami_id), r#"
            instance_type = "#, JE!(&instance.instance_type), r"
            ", if let Some(resource) = &instance_profile_resource {
                CNSL!(r#"
                    iam_instance_profile = aws_iam_instance_profile."#, &resource, r#".name
                "#)
            } else { "".to_string() }, r"
            key_name = aws_key_pair.ace2_key.key_name
            subnet_id = ", format!("aws_subnet.sn-{}.id", &instance.subnet.to_old_terraform_resource_suffix()), r#"
            vpc_security_group_ids = ["#, format!("aws_security_group.{}.id", &security_group_resource), r#"]
            "#, if matches!(instance.graphkey, ace_graph::Instance::Vpn) {
                CNSL!(r"
                    source_dest_check = false
                ")
            } else { "".to_string()}, r"

            ", if !matches!(instance.graphkey, ace_graph::Instance::Vpn | ace_graph::Instance::Graylog) {
                CNSL!(r#"
                    root_block_device {
                        volume_size = "#, JE!(&instance_volume_size), r#" // size in GB
                        volume_type = "gp3"
                    }
                "#)
            } else { "".to_string() },r#"

            lifecycle {
                prevent_destroy = true
                ignore_changes = [ami, instance_type, key_name, subnet_id, ebs_block_device, root_block_device, user_data, availability_zone]
            }

            user_data = "#, JE!(&user_data), r#"

            tags = {
                Name = "#, JE!(&instance_tag_name), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        "#, if !matches!(instance.graphkey, ace_graph::Instance::Developer(_)) {
            CNSL!(r#"
                // Create a route53 record for the openvpn instance public ip
                resource "aws_route53_record" "#, JE!(private_domain_resource), r#" {
                    zone_id = aws_route53_zone.private-domain.zone_id
                    name    = "#, JE!(&instance.name), r#"
                    type    = "A"
                    ttl     = "300"
                    records = ["#, format!("aws_instance.{}.private_ip", &instance_resource), r#"]
                }

                // Create a route53 record for the openvpn instance public ip
                resource "aws_route53_record" "#, JE!(public_domain_resource), r#" {
                    zone_id = aws_route53_zone.public-domain.zone_id
                    name    = "#, JE!(&instance.name), r#"
                    type    = "A"
                    ttl     = "300"
                    records = ["#, format!("aws_instance.{}.public_ip", &instance_resource), r#"]
                }
            "#, wildcard_dns, r#"
            "#)
        } else {
            "\n // Associated Route 53 resources for this instance are found in developer.tf \n".to_string()
        }, r#"
    "#));

    if instance.use_elastic_ip {
        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            resource "aws_eip" "#, JE!(eip_resource), r#" {
                domain = "vpc"
                tags = {
                    Name = "#, JE!(eip_tag), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
            }

            resource "aws_eip_association" "#, JE!(eip_association_resource), r#" {
                instance_id   = "#, format!("aws_instance.{}.id", &instance_resource), r#"
                allocation_id = "#, format!("aws_eip.{}.id", &eip_resource), r#"
            }
        "#));
    }

    // Add a divider here for ease of human readability
    output.write(
        &None,
        &CNSL!(
            "


    /////////////////////////////////////////////////


    "
        ),
    );

    Ok(data_tf_outputs)
}

fn generate_data_tf_kv_pairs(
    instance_gk: &ace_graph::Instance,
    dashed_ins_name: &String,
) -> Vec<(String, String)> {
    let mut rval = vec![];

    match instance_gk {
        ace_graph::Instance::Ace
        | ace_graph::Instance::Graylog
        | ace_graph::Instance::Vpn
        | ace_graph::Instance::Deploy => {
            let ins_resource = format!("{dashed_ins_name}_instance");

            let id_pair = (
                format!("{ins_resource}_id"),
                format!("aws_instance.{}.id", ins_resource),
            );

            let public_ip_pair = if matches!(instance_gk, ace_graph::Instance::Ace) {
                (
                    format!("{ins_resource}_public_ip"),
                    format!("aws_eip.{}_eip.public_ip", dashed_ins_name),
                )
            } else {
                (
                    format!("{ins_resource}_public_ip"),
                    format!("aws_instance.{}.public_ip", ins_resource),
                )
            };

            let private_ip_pair = (
                format!("{ins_resource}_private_ip"),
                format!("aws_instance.{}.private_ip", ins_resource),
            );
            let public_domain_pair = (
                format!("{ins_resource}_public_domain"),
                format!("aws_route53_record.{}_public_domain.fqdn", dashed_ins_name),
            );
            let private_domain_pair = (
                format!("{ins_resource}_private_domain"),
                format!("aws_route53_record.{}_private_domain.fqdn", dashed_ins_name),
            );
            let ami_id_pair = (
                format!("{ins_resource}_ami_id"),
                format!("aws_instance.{}.ami", ins_resource),
            );

            rval.push(id_pair);
            rval.push(public_ip_pair);
            rval.push(private_ip_pair);
            rval.push(public_domain_pair);
            rval.push(private_domain_pair);
            rval.push(ami_id_pair);
        }
        _ => {}
    }

    rval
}
