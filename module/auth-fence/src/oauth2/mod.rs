use async_trait::async_trait;
use oauth2::{EndpointNotSet, EndpointSet};
use provider::OAuth2ProviderType;

pub mod provider;

// TODO: Finish traits for oauth2
pub trait OAuth2Config {
    fn generate_auth_url(&self) -> String;
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct AuthState {
    state: String,
    pub next_uri: String,
}

#[derive(serde::Deserialize, serde::Serialize, Debug)]
pub struct AccessToken {
    pub provider: provider::OAuth2ProviderType,
    pub access_token: String,
    pub token_type: String,
    pub expires_in: u64,
    pub refresh_token: Option<String>,
    pub scopes: Option<Vec<String>>,
}

#[async_trait]
pub trait OAuth2Provider: Send + Sync {
    fn key(&self) -> String;
    fn provider(&self) -> OAuth2ProviderType;
    fn client_id(&self) -> String;
    fn client_secret(&self) -> String;
    fn pkce_enabled(&self) -> bool {
        true
    }
    fn get_redirect_uri(&self, app_url: String) -> String {
        format!("{}/oauth2/{}/redirect", app_url, self.provider())
    }
    fn get_button_uri(&self, next_uri: Option<String>) -> String {
        match next_uri {
            Some(n) => format!("/oauth2/{}/?next_uri={}", self.key(), n),
            None => format!("/oauth2/{}/", self.key()),
        }
    }
    fn get_button_html(&self, next_uri: Option<String>) -> maud::PreEscaped<String>;
    fn get_auth_uri(&self) -> String;
    fn get_token_uri(&self) -> String;
    fn get_user_info_uri(&self) -> String;
    fn get_scopes(&self) -> Vec<String>;
    async fn get_access_token(
        &self,
        code: &str,
        pkce_code_verifier: Option<&str>,
        app_url: String,
    ) -> granite::Result<oauth2::basic::BasicTokenResponse> {
        // `oauth2::basic::BasicTokenResponse` needs `use oauth2::TokenResponse;` to be in scope to use `.access_token()`
        // Most providers should have the same flow/usage of the oauth2 crate
        // TODO: Clean this up and provide error handling
        let client = match self.get_client(app_url) {
            Ok(client) => client,
            Err(e) => return Err(granite::process_error!("Error getting client: {e}")),
        };

        let http_client = reqwest::ClientBuilder::new()
            // Following redirects opens the client up to SSRF vulnerabilities.
            .redirect(reqwest::redirect::Policy::none())
            .build()
            .expect("Client should build");

        let token_response = match self.pkce_enabled() {
            true => match pkce_code_verifier {
                Some(verifier) => {
                    approck::debug!("Setting pkce verifier");
                    approck::debug!("Exchanging code for token");
                    client
                        .exchange_code(oauth2::AuthorizationCode::new(code.to_string()))
                        .set_pkce_verifier(oauth2::PkceCodeVerifier::new(verifier.to_string()))
                        .request_async(&http_client)
                        .await
                        .map_err(|e| {
                            granite::Error::new(granite::ErrorType::ProcessError).add_context(e)
                        })?
                }
                None => {
                    return Err(granite::process_error!("No pkce code verifier provided"));
                }
            },
            false => {
                approck::debug!("Skipping pkce verifier");
                approck::debug!("Exchanging code for token");
                client
                    .exchange_code(oauth2::AuthorizationCode::new(code.to_string()))
                    .request_async(&http_client)
                    .await
                    .map_err(|e| {
                        granite::Error::new(granite::ErrorType::ProcessError).add_context(e)
                    })?
            }
        };

        Ok(token_response)
    }
    fn get_client(
        &self,
        app_url: String,
    ) -> granite::Result<
        oauth2::basic::BasicClient<
            EndpointSet,
            EndpointNotSet,
            EndpointNotSet,
            EndpointNotSet,
            EndpointSet,
        >,
    > {
        let auth_url = match oauth2::AuthUrl::new(self.get_auth_uri().clone()) {
            Ok(auth_url) => auth_url,
            Err(e) => {
                return Err(granite::process_error!("Error creating auth url: {e}"));
            }
        };

        let token_url = match oauth2::TokenUrl::new(self.get_token_uri().clone()) {
            Ok(token_url) => token_url,
            Err(e) => {
                return Err(granite::process_error!("Error creating token url: {e}"));
            }
        };

        // Set the redirect URL - needed for auth_url and verifying the token
        let redirect_url = match oauth2::RedirectUrl::new(self.get_redirect_uri(app_url)) {
            Ok(redirect_url) => redirect_url,
            Err(e) => {
                return Err(granite::process_error!("Error creating redirect url: {e}"));
            }
        };

        /*
        // TODO: Microsoft does not support token introspection - grabbing `/userinfo` instead confirms validity of token
        // TODO: Facebook also has limited token introspection and requires an input_token (variable) and access_token or admin access_token
        let introspect_url = match oauth2::IntrospectionUrl::new(self.introspect_uri.clone()) {
            Ok(introspect_url) => introspect_url,
            Err(_) => todo!(), // log/display this error
        };
         */

        // if this fails, we need to log it and return a nice error
        let client = oauth2::basic::BasicClient::new(oauth2::ClientId::new(self.client_id()))
            .set_client_secret(oauth2::ClientSecret::new(self.client_secret().clone()))
            .set_auth_uri(auth_url)
            .set_token_uri(token_url)
            .set_redirect_uri(redirect_url);

        Ok(client)
    }
    async fn build_auth_url(
        &self,
        redis: &mut approck_redis::RedisCX<'_>,
        req_session_token: String,
        next_uri: &str,
        app_url: String,
    ) -> granite::Result<String> {
        let redis_session_key = super::redis_session_key(req_session_token.as_str());

        let client = match self.get_client(app_url) {
            Ok(client) => client,
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting client for {}: {}",
                    self.provider(),
                    e
                ));
            }
        };

        // Start building the authorization URL
        let get_state = || -> oauth2::CsrfToken {
            let state_json = serde_json::json!({
                "next_uri": next_uri,
                "state": oauth2::CsrfToken::new_random()
            })
            .to_string();

            oauth2::CsrfToken::new(base64_light::base64_encode_bytes(state_json.as_bytes()))
        };

        let mut auth_url_builder = client.authorize_url(get_state);

        // Only generate and set PKCE challenge if PKCE is enabled
        let pkce_code_verifier = if self.pkce_enabled() {
            let (pkce_code_challenge, pkce_code_verifier) =
                oauth2::PkceCodeChallenge::new_random_sha256();
            auth_url_builder = auth_url_builder.set_pkce_challenge(pkce_code_challenge);
            Some(pkce_code_verifier)
        } else {
            None
        };

        for scope in self.get_scopes().iter() {
            auth_url_builder = auth_url_builder.add_scope(oauth2::Scope::new(scope.to_string()));
        }

        let (auth_url, csrf_token) = auth_url_builder.url();

        // Save PKCE code verifier to Redis only if PKCE is enabled
        if let Some(pkce_code_verifier) = pkce_code_verifier {
            redis
                .hset_val(
                    &redis_session_key,
                    "pkce_code_verifier",
                    pkce_code_verifier.secret(),
                )
                .await?;
        }
        redis
            .hset_val(&redis_session_key, "csrf_token", csrf_token.secret())
            .await?;

        // Return the authorization URL
        Ok(auth_url.into())
    }

    // Returns an identity and a url to send the user to upon successful authentication
    async fn handle_auth_redirect(
        &self,
        state: &str,
        code: &str,
        redis: &mut approck_redis::RedisCX<'_>,
        req_session_token: String,
        redirect_uri: String,
    ) -> granite::Result<(crate::BasicUserInfo, String)> {
        let redis_session_key = super::redis_session_key(&req_session_token);

        let pkce_code_verifier_string = match self.pkce_enabled() {
            true => {
                match redis
                    .hget_val::<Option<String>>(&redis_session_key, "pkce_code_verifier")
                    .await?
                {
                    Some(pkce_code_verifier) => Some(pkce_code_verifier),
                    None => {
                        return Err(granite::process_error!(
                            "No pkce code verifier found in redis"
                        ));
                    }
                }
            }
            false => None,
        };

        let pkce_code_verifier = pkce_code_verifier_string.as_deref();

        let csrf_token = match redis.hget_val(&redis_session_key, "csrf_token").await? {
            Some(csrf_token) => csrf_token,
            None => "".to_string(),
        };

        // TODO: Fix error handling below
        // if state does not match, we need to log it and return a nice error
        if state != csrf_token {
            return Err(granite::process_error!("CSRF token mismatch"));
        }

        let auth_state: crate::oauth2::AuthState =
            match serde_json::from_str(base64_light::base64_decode_str(state).as_str()) {
                Ok(decoded_state) => decoded_state,
                Err(e) => return Err(granite::process_error!("Error decoding state: {e}")),
            };

        let next_uri = urlencoding::decode(&auth_state.next_uri)
            .expect("utf-8")
            .to_string();

        let access_token = match self
            .get_access_token(code, pkce_code_verifier, redirect_uri)
            .await
        {
            Ok(access_token) => access_token,
            Err(e) => {
                return Err(granite::process_error!("Error getting access token: {e}"));
            }
        };

        match self.get_user_info(&access_token).await {
            Ok(user_info) => Ok((user_info, next_uri)),
            Err(e) => Err(granite::process_error!("Error getting user info: {e}")),
        }
    }
    async fn get_user_info(
        &self,
        token: &oauth2::basic::BasicTokenResponse,
    ) -> granite::Result<crate::BasicUserInfo>;
}
