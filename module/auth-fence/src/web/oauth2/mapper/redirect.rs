#[approck::http(GET /oauth2/{provider:String}/redirect?state=String&code=Option<String>&error=Option<String>; AUTH None; return Redirect;)]
pub mod redirect {
    use crate::postgres::log;

    async fn request(
        db: Postgres,
        app: App,
        req: Request,
        qs: QueryString,
        path: Path,
        redis: Redis,
    ) -> Result<Response> {
        let provider = path.provider;
        let ip_addr = req.remote_address().ip();
        let auth_fence = app.auth_fence_system();
        let state = qs.state.as_str();
        let error = qs.error.as_deref().unwrap_or("");
        let code = qs.code.as_deref().unwrap_or("");

        approck::debug!("OAuth2 redirect request initiated");
        approck::debug!("OAuth2 State: {}", state);
        approck::debug!("OAuth2 Error: {}", error);
        approck::debug!("OAuth2 Code: {}", code);

        if !error.is_empty() || (error.is_empty() && code.is_empty()) {
            match log::auth_log(
                &db,
                log::AuthLogData {
                    create_addr: ip_addr.to_string(),
                    session_token: req.session_token(),
                    identity_uuid: None,
                    user_esid: None,
                    user_email: None,
                    auth_type: "OAuth2".to_string(),
                    auth_action: "LoginRedirectError".to_string(),
                    auth_provider: Some(provider),
                    success: false,
                    blocked: false,
                    data: None,
                },
            )
            .await
            {
                Ok(_) => {}
                Err(e) => {
                    return Err(granite::Error::process_error(
                        "Error logging authentication failure".to_string(),
                    )
                    .add_context(e));
                }
            }

            if !error.is_empty() {
                return Err(granite::Error::authentication(format!(
                    "Something went wrong. We are unable to authenticate your login request. Please try again. Error: {}",
                    error
                )));
            }

            return Err(granite::Error::authentication("Something went wrong. We are unable to authenticate your login request. Please try again.".to_string()));
        }

        let (user_info, next_uri) = match auth_fence.get_provider(&provider) {
            Some(provider_data) => match provider_data {
                crate::types::AuthProviderType::OAuth2(oauth_provider) => {
                    match oauth_provider
                        .handle_auth_redirect(
                            state,
                            code,
                            &mut redis,
                            req.session_token(),
                            app.webserver_system().url(),
                        )
                        .await
                    {
                        Ok((user_info, next_uri)) => (user_info, next_uri),
                        Err(e) => {
                            println!("Error handling auth redirect: {:?}", e);
                            return Err(granite::Error::authentication("Something went wrong. We are unable to authenticate your oauth2 login request. Please try again.".to_string())
                            .add_context(e));
                        }
                    }
                }
                crate::types::AuthProviderType::OpenID(openid_provider) => {
                    match openid_provider
                        .handle_auth_redirect(
                            state,
                            code,
                            &mut redis,
                            req.session_token(),
                            app.webserver_system().url(),
                        )
                        .await
                    {
                        Ok((user_info, next_uri)) => (user_info, next_uri),
                        Err(e) => {
                            println!("Error handling auth redirect: {:?}", e);
                            return Err(granite::Error::authentication("Something went wrong. We are unable to authenticate your oidc login request. Please try again.".to_string())
                            .add_context(e));
                        }
                    }
                }
            },
            None => {
                return Err(granite::Error::authentication(
                    "Something went wrong on our end. Please try again.".to_string(),
                )
                .add_context(format!("No provider data for {}", provider)));
            }
        };

        let mut identity = match crate::api::identity::validate(&db, &user_info).await {
            Ok(identity) => identity,
            Err(e) => {
                return Err(granite::Error::authentication(format!(
                    "The email address {} is not yet registered to use this website.",
                    user_info.email
                ))
                .add_context(e));
            }
        };

        // save the profile pic if we do not already have one on record and the api gave us one
        if !user_info.avatar_uri.is_empty() && identity.avatar_uri.is_none() {
            crate::api::identity::update_avatar_uri(&db, &mut identity, &user_info.avatar_uri)
                .await?;
        }

        // Link identity with given sso provider they authenticated with
        crate::api::identity::create_first_login_ssopro(
            &db,
            &identity.identity_uuid,
            &user_info.provider,
            &user_info.id,
            &user_info.email,
            &ip_addr,
        )
        .await?;

        // Set the user identity in redis
        crate::api::identity::set_redis_session(&mut redis, &req.session_token(), &identity)
            .await?;

        match log::auth_log(
            &db,
            log::AuthLogData {
                create_addr: ip_addr.to_string(),
                session_token: req.session_token(),
                identity_uuid: Some(identity.identity_uuid),
                user_esid: Some(user_info.id),
                user_email: Some(user_info.email),
                auth_type: "OAuth2".to_string(),
                auth_action: "LoginRedirectDone".to_string(),
                auth_provider: Some(provider),
                success: true,
                blocked: false,
                data: None,
            },
        )
        .await
        {
            Ok(_) => Ok(Response::Redirect(next_uri.as_str().into())),
            Err(e) => {
                return Err(granite::Error::process_error(
                    "Error logging authentication redirect/completion".to_string(),
                )
                .add_context(e));
            }
        }
    }
}
