#[approck::http(GET /admin/auth/permission/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, doc: Document, qs: QueryString) -> Result<Response> {
        use maud::html;

        doc.set_title("Permission Records");
        doc.add_css("/admin/auth/permission/index.css");
        doc.add_js("/admin/auth/permission/index.js");

        use crate::api::admin::permission::list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = list::list::call(
            app,
            list::list::Input {
                keyword: qs.keyword.clone(),
            },
        )
        .await?;

        let mut dt = bux::component::detail_table(output.permissions);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.set_heading("Permissions");
        dt.add_column("Name", |p| html! { (p.name) });
        dt.add_column(
            "Description",
            |p| html! { (p.description.as_deref().unwrap_or("-")) },
        );
        dt.add_details_column(|p| crate::api::admin::permission::ml_admin(&p.permission_uuid, ""));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}
