#[approck::http(GET /admin/auth/identity/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        doc: Document,
        app: App,
        identity: Identity,
        qs: QueryString,
    ) -> Result<Response> {
        // Set page title and load related resources
        doc.set_title("Identity Records");
        doc.add_css("/admin/auth/mod.css");
        doc.page_nav_add_record("Create Identity", "/admin/auth/identity/add");
        //doc.page_nav_enable_go_back("/admin/dashboard");

        // Retrieve identity rows from the database
        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = crate::api::admin::identity::list::list::call(
            app,
            identity,
            crate::api::admin::identity::list::list::Input {
                keyword: qs.keyword.clone(),
                active: None,
            },
        )
        .await?;
        let identities = output.identities;
        let mut dt = bux::component::detail_table(identities);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        // Build HTML table with identity records
        // Add columns to the detail table
        dt.add_column("Type", |ident| maud::html! { (ident.identity_type) });
        dt.add_column("Created", |ident| maud::html! { (ident.create_ts) });
        dt.add_column("Name", |ident| maud::html! { (ident.name) });
        dt.add_column(
            "Email",
            |ident| maud::html! { (ident.email.clone().unwrap_or("-".to_string())) },
        );
        dt.add_column(
            "Active",
            |ident| maud::html! { (if ident.active { "Yes" } else { "No" }) },
        );
        dt.add_details_column(|ident| ident.ml_admin(""));

        doc.add_body(maud::html! { (dt) });
        Ok(Response::HTML(doc.into()))
    }
}
