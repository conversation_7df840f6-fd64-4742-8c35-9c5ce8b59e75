#[approck::http(GET /admin/auth/role/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        // Set page title and load related resources
        doc.set_title("Role Records");
        doc.add_css("/admin/auth/role/index.css");
        doc.add_js("/admin/auth/role/index.js");
        // Note: No "Create Role" button as roles are not editable

        // Retrieve roles using the API
        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = crate::api::admin::role::list::list::call(
            app,
            identity,
            crate::api::admin::role::list::list::Input {
                keyword: qs.keyword.clone(),
            },
        )
        .await?;

        let roles = output.roles;

        // Build detail table with role records
        let mut dt = bux::component::detail_table(roles);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.set_heading("Roles");
        dt.add_column(
            "ID",
            |r| maud::html! { (r.role_psid.as_deref().unwrap_or("-")) },
        );
        dt.add_column("Name", |r| maud::html! { (r.name) });
        dt.add_details_column(|r| crate::api::admin::role::ml_admin(&r.role_uuid, ""));

        doc.add_body(maud::html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}
