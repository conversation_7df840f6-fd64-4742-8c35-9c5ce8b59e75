pub mod security;

pub fn add_page_nav(doc: &mut impl bux::document::PageNav) {
    doc.add_page_nav_menu("Account Info", "/myaccount/accountinfo/");
    doc.add_page_nav_menu("Business Profile", "/myaccount/businessprofile/");
    doc.add_page_nav_menu("Email", "/myaccount/mapper/email");
    doc.add_page_nav_menu("Password", "/myaccount/mapper/password");
    doc.add_page_nav_menu("MFA", "/myaccount/mapper/mfa/");
    doc.add_page_nav_menu("Google", "/myaccount/mapper/sso/google");
    doc.add_page_nav_menu("Microsoft", "/myaccount/mapper/sso/microsoft");
    doc.add_page_nav_menu("Apple", "/myaccount/mapper/sso/apple");
}
