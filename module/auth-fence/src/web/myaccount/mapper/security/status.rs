#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/status; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Login Status");

        // Get username details via API
        let detail_input = crate::api::myaccount::security::detail::Input {
            identity_uuid: path.identity_uuid,
        };

        let detail_response =
            crate::api::myaccount::security::detail::call(app, identity, detail_input).await?;

        let (current_username, has_login) = match detail_response {
            crate::api::myaccount::security::detail::Response::Output(output) => {
                (output.username, output.has_login)
            }
            _ => (None, false), // Default to no login if there's an error
        };

        let status_label = if has_login {
            html! {
                span.status-badge.status-active {
                    i.fas.fa-check-circle {}
                    " Login Configured"
                }
            }
        } else {
            html! {
                span.status-badge.status-inactive {
                    i.fas.fa-times-circle {}
                    " No Login Configured"
                }
            }
        };

        let username_display = if let Some(username) = &current_username {
            html! {
                div.username-display {
                    strong { "Current Username: " }
                    span.username-value { (username) }
                }
            }
        } else {
            html! {
                div.username-display {
                    span.no-username { "No username configured" }
                }
            }
        };

        let action_buttons = if has_login {
            html! {
                div.action-buttons {
                    a.btn.btn-primary href=(format!("/myaccount/{}/security/login", path.identity_uuid)) {
                        i.fas.fa-edit {}
                        " Edit Login"
                    }
                    button.btn.btn-danger type="button" id="remove-login-btn" {
                        i.fas.fa-trash {}
                        " Remove Login"
                    }
                }
            }
        } else {
            html! {
                div.action-buttons {
                    a.btn.btn-success href=(format!("/myaccount/{}/security/login", path.identity_uuid)) {
                        i.fas.fa-plus {}
                        " Create Login"
                    }
                }
            }
        };

        doc.add_body(html!(
            div.login-status-container {
                div.status-panel {
                    header.panel-header {
                        h2 { "Login Status" }
                        (status_label)
                    }

                    div.panel-body {
                        (username_display)

                        hr;

                        (action_buttons)
                    }
                }

                // Hidden form for remove action
                @if has_login {
                    form id="remove-login-form" method="POST" style="display: none;" {
                        input type="hidden" name="identity_uuid" value=(path.identity_uuid.to_string()) {}
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
