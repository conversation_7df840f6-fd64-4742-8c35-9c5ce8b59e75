/* Login Status Page Styles */

.login-status-container {
    max-width: 600px;
    margin: 2rem auto;
    padding: 0 1rem;

    .status-panel {
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .panel-header {
            background: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            h2 {
                margin: 0;
                color: #333;
                font-size: 1.5rem;
            }

            .status-badge {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-weight: 500;
                font-size: 0.9rem;

                &.status-active {
                    background-color: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;

                    i.fas.fa-check-circle {
                        color: #155724;
                    }
                }

                &.status-inactive {
                    background-color: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;

                    i.fas.fa-times-circle {
                        color: #721c24;
                    }
                }
            }
        }

        .panel-body {
            padding: 1.5rem;

            .username-display {
                margin-bottom: 1.5rem;
                padding: 1rem;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #007bff;

                strong {
                    color: #333;
                }

                .username-value {
                    color: #007bff;
                    font-family: monospace;
                    font-size: 1.1rem;
                    font-weight: 600;
                }

                .no-username {
                    color: #6c757d;
                    font-style: italic;
                }
            }

            hr {
                border: none;
                border-top: 1px solid #e0e0e0;
                margin: 1.5rem 0;
            }

            .action-buttons {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;

                .btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 6px;
                    text-decoration: none;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-size: 0.95rem;

                    &.btn-primary {
                        background-color: #007bff;
                        color: white;

                        &:hover {
                            background-color: #0056b3;
                            color: white;
                            text-decoration: none;
                        }

                        i.fas.fa-edit {
                            color: white;
                        }
                    }

                    &.btn-success {
                        background-color: #28a745;
                        color: white;

                        &:hover {
                            background-color: #218838;
                            color: white;
                            text-decoration: none;
                        }

                        i.fas.fa-plus {
                            color: white;
                        }
                    }

                    &.btn-danger {
                        background-color: #dc3545;
                        color: white;

                        &:hover {
                            background-color: #c82333;
                        }

                        i.fas.fa-trash {
                            color: white;
                        }
                    }

                    &:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                    }

                    &.loading {
                        position: relative;

                        &::after {
                            content: "";
                            position: absolute;
                            width: 16px;
                            height: 16px;
                            margin: auto;
                            border: 2px solid transparent;
                            border-top-color: currentColor;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                        }
                    }
                }
            }
        }
    }

    form#remove-login-form {
        display: none;

        input[type="hidden"] {
            display: none;
        }
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .login-status-container {
        margin: 1rem;
        max-width: none;

        .status-panel {
            .panel-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .panel-body {
                .action-buttons {
                    flex-direction: column;

                    .btn {
                        justify-content: center;
                        width: 100%;
                    }
                }
            }
        }
    }
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
