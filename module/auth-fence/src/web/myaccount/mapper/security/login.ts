import "./login.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/text/password.mts";

import { SE } from "@granite/lib.mts";
import { username } from "@crate/api/myaccount/security/loginλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextPassword from "@bux/input/text/password.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $identity_uuid: HTMLInputElement = SE($form, "[name=identity_uuid]");
const $username: BuxInputTextString = SE($form, "[name=username]");
const $password: BuxInputTextPassword = SE($form, "[name=password]");
const $confirm_password: BuxInputTextPassword = SE($form, "[name=confirm_password]");

new FormPanel({
    $form,
    api: username.api,
    on_cancel: go_back,

    err: (errors) => {
        $username.set_e(errors.username);
        $password.set_e(errors.password);
        $confirm_password.set_e(errors.confirm_password);
    },

    get: () => {
        return {
            identity_uuid: $identity_uuid.value,
            username: $username.value,
            password: $password.value,
            confirm_password: $confirm_password.value,
        };
    },

    set: (_value) => {
        // No need to set values for registration form
    },

    out: (output) => {
        // Show success message and redirect back to status page
        alert(output.success_message);

        go_next(`/myaccount/${output.identity_uuid}/security/status`);
    },
});
