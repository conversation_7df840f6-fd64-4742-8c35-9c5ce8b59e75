import "./status.mcss";

import { SE } from "@granite/lib.mts";
import { remove } from "@crate/api/myaccount/security/loginλ.mts";
import { go_next } from "@bux/singleton/nav_stack.mts";

// Get DOM elements
const $removeBtn = document.getElementById("remove-login-btn") as HTMLButtonElement;
const $removeForm = document.getElementById("remove-login-form") as HTMLFormElement;

// Only set up remove functionality if the button exists (i.e., user has a login)
if ($removeBtn && $removeForm) {
    const $identityUuid: HTMLInputElement = SE($removeForm, "[name=identity_uuid]");

    $removeBtn.addEventListener("click", async () => {
        // Show confirmation dialog
        const confirmed = confirm(
            "Are you sure you want to remove your login credentials? " +
                "You will need to create new login credentials to access your account.",
        );

        if (!confirmed) {
            return;
        }

        // Disable button and show loading state
        $removeBtn.disabled = true;
        $removeBtn.classList.add("loading");
        $removeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Removing...';

        try {
            // Call the remove API using the standard pattern
            const response = await remove.call({
                identity_uuid: $identityUuid.value,
            });

            // Check if the response contains Output (success case)
            if ("Output" in response) {
                const output = response.Output[0];

                // Show success message
                alert(output.success_message);

                // Redirect back to status page to show updated state
                go_next(`/myaccount/${output.identity_uuid}/security/status`);
            } else {
                // Handle validation or other errors
                console.error("Remove login failed:", response);
                alert("Failed to remove login credentials. Please try again.");

                // Reset button state
                $removeBtn.disabled = false;
                $removeBtn.classList.remove("loading");
                $removeBtn.innerHTML = '<i class="fas fa-trash"></i> Remove Login';
            }
        } catch (error) {
            console.error("Error removing login:", error);
            alert("An error occurred while removing login credentials. Please try again.");

            // Reset button state
            $removeBtn.disabled = false;
            $removeBtn.classList.remove("loading");
            $removeBtn.innerHTML = '<i class="fas fa-trash"></i> Remove Login';
        }
    });
}
