#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/sso/; AUTH is_logged_in; return HTML;)]
pub mod page {
    use crate::api::identity::sso::providers::index;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        // Get the current user's identity UUID
        let identity_uuid = path.identity_uuid;

        doc.set_title("SSO Providers");
        doc.add_css("./index.css");
        doc.add_js("./index.js");

        // Call the API to get all SSO providers with connection data
        let providers_result = index::call(app, identity, index::Input { identity_uuid }).await;

        let providers = match providers_result {
            Ok(output) => output.providers,
            Err(e) => {
                approck::error!("Failed to fetch SSO providers: {}", e);
                Vec::new()
            }
        };

        // Single SSO Providers section
        doc.add_body(html!(
            panel {
                header {
                    h2 { "SSO Providers" }
                }
                content {
                    p { "Manage your authentication providers:" }

                    grid-3 {
                        @for provider in &providers {
                            bux-provider-card {
                                header {
                                    h4 {
                                        span.provider-name { "Sign in with " (provider.name) }
                                    }
                                    @if provider.is_connected {
                                        label-tag.success { "Connected" }
                                    }
                                    @else {
                                        label-tag.default { "Not Connected" }
                                    }
                                }
                                content {
                                    @if let Some(ref description) = provider.description {
                                        p { (description) }
                                    }
                                    @if provider.is_connected {
                                        div.connection-details {
                                            @if let Some(ref email) = provider.remote_identity_email {
                                                p {
                                                    strong { "Email: " }
                                                    (email)
                                                }
                                            }
                                            @if let Some(ref connected_date) = provider.formatted_first_auth_ts {
                                                p {
                                                    strong { "Connected: " }
                                                    (connected_date)
                                                }
                                            }
                                        }
                                    } @else {
                                        // Show last authentication date for disconnected providers
                                        @if let Some(ref last_auth_date) = provider.formatted_last_auth_ts {
                                            div.connection-details {
                                                p {
                                                    strong { "Last used: " }
                                                    (last_auth_date)
                                                }
                                            }
                                        }
                                    }
                                }
                                footer {
                                    @if provider.is_connected {
                                        (bux::button::link::label_icon_class(
                                            "Disconnect",
                                            "fas fa-unlink",
                                            &format!("/myaccount/{}/security/sso/{}/disconnect", identity_uuid, provider.ssopro_xsid.to_lowercase()),
                                            "danger"
                                        ))
                                    } @else {
                                        (bux::button::link::label_icon_class(
                                            "Connect",
                                            "fas fa-link",
                                            &provider.connect_url,
                                            "primary"
                                        ))
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}
