import "./index.mcss";

// Import BUX components
import "@bux/component/detail_table.mts";

// Add interactive functionality for the SSO providers page
document.addEventListener("DOMContentLoaded", () => {
    console.log("SSO Providers page loaded");

    // Add click handlers for provider action buttons
    const actionButtons = document.querySelectorAll(
        'a[href*="/myaccount/"][href*="/sso/"], a[href*="/auth/"]',
    );
    actionButtons.forEach((button) => {
        button.addEventListener("click", (e) => {
            const href = (button as HTMLAnchorElement).href;
            if (href.includes("/auth/")) {
                console.log("Connecting to SSO provider");
            } else if (href.includes("/disconnect")) {
                console.log("Disconnecting SSO provider");
                // Add confirmation dialog for disconnect
                const providerName =
                    button.closest("bux-provider-card")?.querySelector(".provider-name")
                        ?.textContent?.replace("Sign in with ", "") || "this provider";
                if (
                    !confirm(
                        `Are you sure you want to disconnect from ${providerName}? You can reconnect at any time.`,
                    )
                ) {
                    e.preventDefault();
                }
            } else {
                console.log("Managing SSO provider details");
            }
        });
    });

    // Add hover effects for provider cards
    const providerCards = document.querySelectorAll("bux-provider-card");
    providerCards.forEach((card) => {
        card.addEventListener("mouseenter", (evt) => {
            console.log("Hovering over provider card");
            const target = evt.target as HTMLDivElement;
            target.style.transform = "translateY(-2px)";
        });

        card.addEventListener("mouseleave", (evt) => {
            console.log("Hovering over provider card");
            const target = evt.target as HTMLDivElement;
            target.style.transform = "translateY(0)";
        });
    });
});
