/* SSO Providers page styling */

/* SSO Providers section */
panel {
    margin: 2rem auto;
    max-width: 1200px;

    header {
        h2 {
            color: #333;
            margin-bottom: 0.5rem;
        }
    }

    content {
        padding: 2rem;

        p {
            margin-bottom: 2rem;
            color: #666;
            font-size: 1rem;
            line-height: 1.5;
        }

        .alert {
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: 0.375rem;
            border: 1px solid transparent;

            &.alert-success {
                color: #0f5132;
                background-color: #d1e7dd;
                border-color: #badbcc;

                p {
                    margin: 0;
                    color: inherit;
                }
            }
        }

        grid-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
    }
}

/* Provider cards */
bux-provider-card {
    display: flex;
    flex-direction: column;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    height: 100%;

    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    header {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h4 {
            margin: 0;
            color: #495057;
            font-weight: 600;

            .provider-name {
                padding-left: 0.5rem;
            }
        }

        label-tag {
            &.success {
                background-color: #d4edda;
                color: #155724;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.75rem;
                font-weight: 500;
            }
        }
    }

    content {
        padding: 1rem 1.5rem;
        flex-grow: 1;
        display: flex;
        flex-direction: column;

        p {
            margin: 0 0 0.5rem 0;
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.4;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .connection-details {
            margin-top: auto;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #28a745;

            p {
                margin: 0 0 0.5rem 0;
                font-size: 0.875rem;
                color: #495057;
                line-height: 1.4;

                &:last-child {
                    margin-bottom: 0;
                }

                strong {
                    color: #212529;
                    font-weight: 600;
                }
            }
        }
    }

    footer {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        text-align: center;
        margin-top: auto;

        .btn {
            min-width: 120px;
            font-weight: 500;
        }
    }
}

/* Responsive design */
@media (max-width: 768px) {
    panel {
        margin: 1rem;

        content {
            padding: 1rem;

            table-wrapper.data-list {
                overflow-x: auto;

                table {
                    min-width: 600px;
                }
            }
        }

        footer {
            padding: 1rem;
        }
    }
}
