auth-panel {
    display: block;
    max-width: 450px;
    margin: 0 auto;

    panel {
        padding: 2rem;

        content {
            padding: 1rem;
            background-color: #f7f7f7;
            text-align: center;

            h1 {
                font-size: 18pt;
            }

            login-buttons {
                display: grid;
                gap: 1rem;
                margin-bottom: 1rem;

                > a.btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 1rem;
                }

                .btn-google {
                    background-color: #3f5ff1;
                    border-color: #3f5ff1;
                    color: #fff;

                    &:hover, &:focus {
                        background-color: #3f5ff1;
                        border-color: #3f5ff1;
                        color: #fff;
                    }
                }

                .btn-microsoft {
                    background-color:#2c2b2d;
                    border-color: #2c2b2d;
                    color: #fff;

                    &:hover, &:focus {
                        background-color: #2c2b2d;
                        border-color: #2c2b2d;
                        color: #fff;
                    }
                }
            }

            p {
                display: flex;
                align-items: center;
                margin-bottom: 0.5rem;
                font-weight: 500;

                &:before, &:after {
                    content: "";
                    flex: 1;
                    border-bottom: 1px solid #ccc;
                }

                &:before {
                    margin-right: 0.5rem;
                }

                &:after {
                    margin-left: 0.5rem;
                }
            }
        }
    }
}    
