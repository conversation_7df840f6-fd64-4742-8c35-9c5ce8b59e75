#[approck::api]
pub mod username {
    use granite::NestedError;
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        #[gtype(trim=both; max=64; no_empty;)]
        pub username: String,
        #[gtype(trim=both; min=8; max=128; no_empty;)]
        pub password: String,
        #[gtype(trim=both; min=8; max=128; no_empty;)]
        pub confirm_password: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub username: String,
        pub success_message: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Response> {
        // For now, allow anyone to register. In production, you might want to add authorization checks
        // if !identity.some_permission() {
        //     return_authorization_error!("insufficient permissions to register users");
        // }

        // make sure passwords match
        if input.password != input.confirm_password {
            return Ok(Response::ValidationError(NestedError {
                outer: "Passwords do not match".to_string(),
                inner: None,
            }));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            // Verify the identity exists
            let identity_exists = granite::pg_row_option!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                };
                row = {
                    identity_uuid: Uuid,
                    name: String,
                };
                SELECT
                    identity_uuid,
                    name
                FROM
                    auth_fence.identity
                WHERE
                    identity_uuid = $identity_uuid::uuid
            )
            .await?;

            if identity_exists.is_none() {
                let input_error = Input_Error {
                    identity_uuid: Some("Identity UUID does not exist".into()),
                    username: None,
                    password: None,
                    confirm_password: None,
                };
                return Ok(Response::ValidationError(NestedError {
                    outer: "validation error".to_string(),
                    inner: Some(input_error),
                }));
            };

            // Generate salt and hash password
            let salt = crate::api::generate_salt();
            let password_hash = crate::api::hash_password(&input.password, &salt);

            // Check if username already exists for a different identity
            match granite::pg_row_option!(
                db = dbtx;
                args = {
                    $username: &input.username,
                    $identity_uuid: &input.identity_uuid,
                };
                row = {
                    identity_uuid: Uuid,
                    username: String,
                };
                SELECT
                    identity_uuid,
                    username
                FROM
                    auth_fence.login
                WHERE
                    LOWER(username) = LOWER($username::text)
                    AND identity_uuid != $identity_uuid::uuid
                    AND active = true
            )
            .await
            {
                Ok(Some(_)) => {
                    let input_error = Input_Error {
                        identity_uuid: None,
                        username: Some("Username already exists for a different identity".into()),
                        password: None,
                        confirm_password: None,
                    };
                    return Ok(Response::ValidationError(NestedError {
                        outer: "validation error".to_string(),
                        inner: Some(input_error),
                    }));
                }
                Ok(None) => {}
                Err(e) => {
                    return Err(
                        granite::process_error!("Error checking for existing username")
                            .add_context(e),
                    );
                }
            }

            // Update existing login
            granite::pg_execute!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                    $username: &input.username,
                    $password_hash: &password_hash,
                    $salt: &salt,
                };
                MERGE INTO
                    auth_fence.login AS target
                USING
                    (VALUES ($identity_uuid::uuid, $username::text, $password_hash::text, $salt::text)) AS source (identity_uuid, username, password_hash, salt)
                ON
                    target.identity_uuid = source.identity_uuid
                WHEN MATCHED THEN
                    UPDATE SET
                        username = source.username,
                        password_base64_pbkdf2_sha256_hash = source.password_hash,
                        password_salt = source.salt,
                        active = TRUE
                WHEN NOT MATCHED THEN
                    INSERT (identity_uuid, username, password_base64_pbkdf2_sha256_hash, password_salt, active)
                    VALUES
                        (source.identity_uuid, source.username, source.password_hash, source.salt, TRUE)
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;

            Ok(Response::Output(Output {
                identity_uuid: input.identity_uuid,
                username: input.username,
                success_message: "Login credentials have been set".to_string(),
            }))
        }
    }
}

#[approck::api]
pub mod remove {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub success_message: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            // Verify the identity exists
            let identity_exists = granite::pg_row_option!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                };
                row = {
                    identity_uuid: Uuid,
                    name: String,
                };
                SELECT
                    identity_uuid,
                    name
                FROM
                    auth_fence.identity
                WHERE
                    identity_uuid = $identity_uuid::uuid
            )
            .await?;

            if identity_exists.is_none() {
                let input_error = Input_Error {
                    identity_uuid: Some("Identity UUID does not exist".into()),
                };
                return Ok(Response::ValidationError(granite::NestedError {
                    outer: "validation error".to_string(),
                    inner: Some(input_error),
                }));
            };

            // Deactivate the login by setting active = false
            granite::pg_execute!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                };
                UPDATE
                    auth_fence.login
                SET
                    active = FALSE
                WHERE
                    identity_uuid = $identity_uuid::uuid
                    AND active = TRUE
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;

            Ok(Response::Output(Output {
                identity_uuid: input.identity_uuid,
                success_message: "Login credentials have been removed".to_string(),
            }))
        }
    }
}
