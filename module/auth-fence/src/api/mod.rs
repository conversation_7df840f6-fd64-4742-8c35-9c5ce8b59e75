pub mod admin;
pub mod identity;
pub mod myaccount;

pub fn generate_salt() -> String {
    use rand::Rng;
    let mut rng = rand::thread_rng();
    let salt: [u8; 32] = rng.r#gen();
    base64_light::base64_encode_bytes(&salt)
}

pub fn hash_password(password: &str, salt: &str) -> String {
    use pbkdf2::pbkdf2_hmac;
    use sha2::Sha256;

    let mut hash_bytes = [0u8; 32];
    pbkdf2_hmac::<Sha256>(
        password.as_bytes(),
        salt.as_bytes(),
        100000,
        &mut hash_bytes,
    );
    base64_light::base64_encode_bytes(&hash_bytes)
}
