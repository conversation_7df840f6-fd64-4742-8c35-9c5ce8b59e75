#[approck::api]
pub mod index {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub providers: Vec<SsoProvider>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct SsoProvider {
        pub ssopro_xsid: String,
        pub name: String,
        pub description: Option<String>,
        pub active: Option<bool>,
        pub is_connected: bool,
        pub remote_identity_email: Option<String>,
        pub formatted_first_auth_ts: Option<String>,
        pub formatted_last_auth_ts: Option<String>,
        pub connect_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user is logged in and has permission to view SSO providers
        if !identity.is_logged_in() {
            return_authorization_error!("must be logged in to view SSO providers");
        }

        // Verify the user can only access their own SSO providers
        let current_identity_uuid = identity.identity_uuid();
        if current_identity_uuid.is_none() {
            return_authorization_error!(
                "insufficient permissions to view SSO providers for this identity"
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        // Query all SSO providers with LEFT JOIN to get connection data if it exists
        // Include both active and inactive connections to show disconnected providers with last auth date
        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                ssopro_xsid: String,
                name: String,
                description: Option<String>,
                remote_identity_email: Option<String>,
                first_authentication_ts: Option<DateTimeUtc>,
                last_authentication_ts: Option<DateTimeUtc>,
                active: Option<bool>,
            };
            SELECT
                sp.ssopro_xsid,
                sp.name,
                sp.description,
                isp.remote_identity_email,
                isp.first_authentication_ts,
                isp.last_authentication_ts,
                isp.active
            FROM auth_fence.ssopro sp
            LEFT JOIN auth_fence.identity_ssopro isp
                ON sp.ssopro_xsid = isp.ssopro_xsid
                AND isp.identity_uuid = $identity_uuid::uuid
            WHERE sp.active = true
            ORDER BY sp.name
        )
        .await?;

        let providers = rows
            .into_iter()
            .map(|row| {
                // Provider is connected if it has an email AND is active
                let is_connected =
                    row.remote_identity_email.is_some() && row.active.unwrap_or(false);

                let formatted_first_auth_ts = row
                    .first_authentication_ts
                    .as_ref()
                    .map(|ts| ts.format("%B %d, %Y").to_string());

                let formatted_last_auth_ts = row
                    .last_authentication_ts
                    .as_ref()
                    .map(|ts| ts.format("%B %d, %Y").to_string());

                // Create connect URL with nuri parameter to redirect back to myaccount
                let connect_url = format!(
                    // TODO: This should be a super trait
                    "/oauth2/{}/?next_uri={}",
                    row.ssopro_xsid,
                    urlencoding::encode(&format!(
                        "/myaccount/{}/security/sso/",
                        input.identity_uuid
                    ))
                );

                SsoProvider {
                    ssopro_xsid: row.ssopro_xsid.clone(),
                    name: row.name,
                    description: row.description,
                    active: row.active,
                    is_connected,
                    remote_identity_email: if is_connected {
                        row.remote_identity_email
                    } else {
                        None
                    },
                    formatted_first_auth_ts,
                    formatted_last_auth_ts,
                    connect_url,
                }
            })
            .collect();

        Ok(Output { providers })
    }
}
