#[approck::api]
pub mod revoke {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        pub ssopro_xsid: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub success: bool,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // TODO: Call the actual provider revoke api
        // Check if the user is logged in and has permission to revoke SSO providers
        if !identity.is_logged_in() {
            return_authorization_error!("must be logged in to revoke SSO providers");
        }

        // Verify the user can only revoke their own SSO providers
        let current_identity_uuid = identity.identity_uuid();
        if current_identity_uuid.is_none() {
            return_authorization_error!(
                "insufficient permissions to revoke SSO providers for this identity"
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        // Check if the SSO provider connection exists
        let connection_check = granite::pg_row_option!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
                $ssopro_xsid: &input.ssopro_xsid,
            };
            row = {
                identity_uuid: Uuid,
            };
            SELECT identity_uuid
            FROM auth_fence.identity_ssopro
            WHERE identity_uuid = $identity_uuid::uuid
                AND ssopro_xsid = $ssopro_xsid
        )
        .await?;

        if connection_check.is_none() {
            return Ok(Output {
                success: false,
                message: "SSO provider connection not found".to_string(),
            });
        }

        // Soft delete the SSO provider connection by setting active = false
        // This preserves the connection history while marking it as disconnected
        // This is a local revocation - we're not notifying the remote provider
        let rows_affected = granite::pg_execute!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
                $ssopro_xsid: &input.ssopro_xsid,
            };
            UPDATE auth_fence.identity_ssopro
            SET active = false
            WHERE identity_uuid = $identity_uuid::uuid
                AND ssopro_xsid = $ssopro_xsid
                AND active = true
        )
        .await?;

        if rows_affected > 0 {
            approck::info!(
                "SSO provider {} revoked for identity {}",
                input.ssopro_xsid,
                input.identity_uuid
            );

            Ok(Output {
                success: true,
                message: format!("Successfully disconnected from {}", input.ssopro_xsid),
            })
        } else {
            Ok(Output {
                success: false,
                message: "Failed to revoke SSO provider".to_string(),
            })
        }
    }
}
