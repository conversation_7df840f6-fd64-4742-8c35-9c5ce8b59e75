use async_trait::async_trait;
use openidconnect::IdTokenClaims;

use crate::openid::{CoreGenderClaim, EmptyAdditionalClaims, OpenIDProvider, OpenIDProviderType};
use crate::types::BaseOpenIDConfig;

#[derive(serde::Deserialize, Debug, Clone)]
pub struct OpenIDMicrosoftConfig {
    #[serde(flatten)]
    pub base: BaseOpenIDConfig,
    pub tenant_id: Option<String>,
}

pub struct OpenIDMicrosoftProvider {
    pub config: OpenIDMicrosoftConfig,
}

impl OpenIDMicrosoftProvider {
    pub fn get_tenant_id(&self) -> String {
        "common".to_string()
    }
    pub fn tenant_id(&self) -> String {
        match self.config.tenant_id {
            Some(ref t) => t.to_string(),
            None => "common".to_string(),
        }
    }
}

#[async_trait]
impl OpenIDProvider for OpenIDMicrosoftProvider {
    fn key(&self) -> String {
        self.config.base.key.clone()
    }
    fn provider(&self) -> OpenIDProviderType {
        OpenIDProviderType::Microsoft
    }
    fn client_id(&self) -> String {
        self.config.base.client_id.clone()
    }
    fn client_secret(&self) -> String {
        self.config.base.client_secret.clone()
    }
    fn get_scopes(&self) -> Vec<String> {
        vec![
            "openid".to_string(),
            "profile".to_string(),
            "email".to_string(),
            "User.Read".to_string(), // needed for calling graph api
        ]
    }
    fn get_redirect_uri(&self, app_url: String) -> String {
        format!("{}/oauth2/{}/redirect", app_url, self.key())
    }
    fn get_base_uri(&self) -> String {
        "https://login.microsoftonline.com".to_string()
    }
    fn get_issuer_url(&self) -> String {
        format!("{}/{}/v2.0", self.get_base_uri(), self.tenant_id())
    }
    fn get_auth_uri(&self) -> String {
        format!(
            "{}/{}/oauth2/v2.0/authorize",
            self.get_base_uri(),
            self.get_tenant_id()
        )
    }
    fn get_token_uri(&self) -> String {
        format!(
            "{}/{}/oauth2/v2.0/token",
            self.get_base_uri(),
            self.get_tenant_id()
        )
    }
    fn get_user_info_uri(&self) -> String {
        "https://graph.microsoft.com/v1.0/me".to_string()
    }
    fn get_well_known_uri(&self) -> String {
        format!(
            "{}/{}/v2.0/.well-known/openid-configuration",
            self.get_base_uri(),
            self.get_tenant_id()
        )
    }
    fn get_jwks_uri(&self) -> String {
        format!(
            "{}/{}/discovery/v2.0/keys",
            self.get_base_uri(),
            self.get_tenant_id()
        )
    }
    fn get_button_html(&self, next_uri: Option<String>) -> maud::PreEscaped<String> {
        maud::html!(
            a href=(self.get_button_uri(next_uri)) class="btn btn-outline-custom btn-microsoft social-btn" {
                i class="fab fa-microsoft" {}
                " Sign in with Microsoft"
            }
        )
    }
    fn unpack_user_claims(
        &self,
        token: &IdTokenClaims<EmptyAdditionalClaims, CoreGenderClaim>,
    ) -> crate::types::BasicUserInfo {
        let name_claim = token
            .name()
            .and_then(|n| n.get(None))
            .map(|n| n.to_string())
            .unwrap_or_default();
        let email_claim = token
            .preferred_username()
            .map(|n| n.to_string())
            .unwrap_or_default();
        let avatar_uri = token
            .picture()
            .and_then(|n| n.get(None))
            .map(|n| n.to_string())
            .unwrap_or_default();

        // split the name into first and last name
        let (firstname, lastname) = match name_claim.split_once(" ") {
            Some((firstname, lastname)) => (firstname.to_string(), lastname.to_string()),
            None => (name_claim.clone(), "".to_string()),
        };

        crate::types::BasicUserInfo {
            provider: self.provider().to_string(),
            id: token.subject().to_string(),
            email: email_claim,
            first_name: firstname,
            last_name: lastname,
            avatar_uri,
        }
    }
}
