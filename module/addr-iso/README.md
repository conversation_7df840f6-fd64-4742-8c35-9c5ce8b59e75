# addr-iso Module

This module provides address and location-related functionality, including US state selection components.

## US State Select Components

The module provides async functions to render bux select dropdowns populated with US states from the database.

### Functions

#### `us_state_select`

Renders a basic bux select dropdown containing all US states.

```rust
use addr_iso::input::address_us_select::us_state_select;

// In your async function
let state_select_html = us_state_select(
    &app,                    // App instance with database access
    "state",                 // HTML name attribute
    "Select State",          // Display label
    Some("CA")              // Optional pre-selected state code
).await?;
```

#### `us_state_select_with_help`

Renders a bux select dropdown with help text.

```rust
use addr_iso::input::address_us_select::us_state_select_with_help;

// In your async function
let state_select_html = us_state_select_with_help(
    &app,                    // App instance with database access
    "state",                 // HTML name attribute
    "Select State",          // Display label
    Some("TX"),             // Optional pre-selected state code
    "Choose your state"     // Help text
).await?;
```

### Requirements

- The app parameter must implement `approck_postgres::App` trait
- Database must have the `addr_iso.us_state` table populated
- Functions return `granite::Result<bux::Markup>`

### Database Schema

The functions query the `addr_iso.us_state` table:

- `state_code` (char(2)) - Two-letter state code (e.g., "CA", "TX")
- `name` (varchar(128)) - Full state name (e.g., "California", "Texas")

States are ordered alphabetically by name in the dropdown.
