use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, Serialize, Deserialize, Default)]
#[granite::gtype(
    RsDebug,
    RsType,
    RsTypeEncode,
    RsTypeDecode,
    RsError,
    RsErrorEncode,
    TsType,
    TsTypeEncode,
    TsPartial,
    TsError,
    TsErrorDecode
)]
pub struct AddressISO {
    #[serde(default)]
    pub address1: String,
    #[serde(default)]
    pub address2: String,
    #[serde(default)]
    pub city: String,
    #[serde(default)]
    pub state_province: String,
    #[serde(default)]
    pub postal_code: String,
    #[serde(default)]
    pub country_code: String,
}
