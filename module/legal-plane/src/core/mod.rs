#[granite::gtype]
pub struct Document {
    pub document_uuid: Uuid,
    pub document_psid: Option<String>,
    pub active: bool,
    pub create_ts: DateTimeUtc,
    pub name: String,
    pub body_markdown: String,
    pub body_html: String,
    pub revision: String,
}

pub async fn load_active_by_psid(
    db: &impl approck_postgres::DB,
    document_psid: &str,
) -> granite::Result<Option<Document>> {
    let row = granite::pg_row_option!(
        db = db;
        args = {
            $document_psid: &document_psid,
        };
        row = {
            document_uuid: Uuid,
            document_psid: Option<String>,
            active: bool,
            create_ts: DateTimeUtc,
            name: String,
            body_markdown: String,
            revision: String,
        };
        SELECT
            document_uuid,
            document_psid,
            active,
            create_ts,
            name,
            body_markdown,
            revision
        FROM
            legal_plane.document
        WHERE
            document_psid = $document_psid
            AND active = true
        ORDER BY
            create_ts DESC
        LIMIT 1
    )
    .await?;

    match row {
        Some(row) => {
            let body_html = {
                let parser = pulldown_cmark::Parser::new(&row.body_markdown);
                let mut body_html = String::new();
                pulldown_cmark::html::push_html(&mut body_html, parser);
                body_html
            };

            Ok(Some(Document {
                document_uuid: row.document_uuid,
                document_psid: row.document_psid,
                active: row.active,
                create_ts: row.create_ts,
                name: row.name,
                body_markdown: row.body_markdown,
                body_html,
                revision: row.revision,
            }))
        }
        None => Ok(None),
    }
}
