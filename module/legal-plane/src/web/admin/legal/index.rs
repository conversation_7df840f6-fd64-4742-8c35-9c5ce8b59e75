#[approck::http(GET /admin/legal/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Legal Documents");
        doc.set_body_display_fluid();

        let active = bux::parse_active_qs(&qs.active, Some(true));

        let output = crate::api::admin::legal::document::admin_legal_document_list::call(
            app,
            identity,
            crate::api::admin::legal::document::admin_legal_document_list::Input {
                active_only: active,
            },
        )
        .await?;

        let mut dt = bux::component::detail_table(output.documents);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_column("Name", |d| html! { (d.name) });
        dt.add_column("PSID", |d| {
            html! {
                @if let Some(ref psid) = d.document_psid {
                    (psid)
                } @else {
                    em { "None" }
                }
            }
        });
        dt.add_column("Revision", |d| html! { (d.revision) });
        dt.add_active_status_column("Status", |d| d.active);
        dt.add_column("Created", |d| html! { (d.create_ts.format("%Y-%m-%d")) });
        dt.add_details_column(|d| crate::ml_document_details(d.document_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}
