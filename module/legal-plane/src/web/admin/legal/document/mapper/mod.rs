pub mod index;

#[approck::prefix(/admin/legal/document/{document_uuid:Uuid}/)]
pub mod prefix {
    pub async fn menu(app: App, menu: Menu, _identity: Identity, document_uuid: Uuid) {
        let current_name = app.uuid_to_label(document_uuid);

        menu.set_label_name_uri("Details", current_name, &ml(&document_uuid, ""));
    }

    pub fn ml(document_uuid: &granite::Uuid, uri: &str) -> String {
        format!("/admin/legal/document/{}/{}", document_uuid, uri)
    }
}
