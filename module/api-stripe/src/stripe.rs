use granite::{Error, ErrorType};
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};
use serde::Deserialize;
use serde_json::Value;
use std::collections::HashMap;

#[derive(Deserialize, Debug)]
pub struct StripeCustomerResponse {
    pub id: String,
    pub object: String,
    pub email: String,
    pub name: String,
}

#[derive(Deserialize, Debug)]
pub struct StripeBillingPortalResponse {
    pub id: String,
    pub object: String,
    pub url: String,
    #[serde(default)]
    pub created: i64,
    #[serde(default)]
    pub customer: String,
    #[serde(default)]
    pub livemode: bool,
    #[serde(default)]
    pub return_url: Option<String>,
    #[serde(flatten)]
    pub additional_properties: HashMap<String, Value>,
}

pub async fn create_customer(
    api_key: &str,
    name: &str,
    email: &str,
) -> granite::Result<StripeCustomerResponse> {
    let url = "https://api.stripe.com/v1/customers";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {}", api_key);
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::new(ErrorType::Unexpected).add_context(e.to_string()))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Create form parameters
    let mut params = HashMap::new();
    params.insert("name", name);
    params.insert("email", email);

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&params)
        .send()
        .await
        .map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!("Failed to send request: {}", e))
        })?;

    // Parse response
    let customer: StripeCustomerResponse = response.json().await.map_err(|e| {
        Error::new(ErrorType::Unexpected).add_context(format!("Failed to parse response: {}", e))
    })?;

    Ok(customer)
}

pub async fn create_billing_portal_link(
    api_key: &str,
    customer_id: &str,
    return_url: &str,
    configuration_id: Option<&str>,
) -> granite::Result<StripeBillingPortalResponse> {
    /*
      https://docs.stripe.com/customer-management/integrate-customer-portal?shell=true&api=true&resource=billing_portal%20configurations&action=create

      API doc:
      https://docs.stripe.com/api/customer_portal/sessions/object

      curl https://api.stripe.com/v1/billing_portal/sessions \
    -u "sk_test_lq9XrBPArA2OZHwVSdl75keb:" \
    -d customer={{CUSTOMER_ID}} \
    --data-urlencode return_url="https://example.com/account"
      */
    let url = "https://api.stripe.com/v1/billing_portal/sessions";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {}", api_key);
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::new(ErrorType::Unexpected).add_context(e.to_string()))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Create form parameters
    let mut params = HashMap::new();
    params.insert("customer", customer_id);
    params.insert("return_url", return_url);
    params.insert("configuration", configuration_id.unwrap_or(""));

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&params)
        .send()
        .await
        .map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!("Failed to send request: {}", e))
        })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unable to read error response".to_string());
        return Err(Error::new(ErrorType::Unexpected).add_context(format!(
            "Stripe API returned error status: {} with message: {}",
            status, error_text
        )));
    }

    // Get the response text for debugging
    let body_text = response.text().await?;
    println!("Raw Stripe billing portal response: {}", body_text);

    // Parse response
    let portal_session: StripeBillingPortalResponse =
        serde_json::from_str(&body_text).map_err(|e| {
            Error::new(ErrorType::Unexpected).add_context(format!(
                "Failed to parse response: {}. Response body: {}",
                e, body_text
            ))
        })?;

    Ok(portal_session)
}
