[package]
name = "api-sendgrid"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["approck", "bux", "granite"]

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
approck-postgres = { workspace = true }
granite = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
tokio = { workspace = true, features = ["rt"] }
uuid = { workspace = true, features = ["v4", "serde"] }
chrono = { workspace = true, features = ["serde"] }
maud = { workspace = true }
