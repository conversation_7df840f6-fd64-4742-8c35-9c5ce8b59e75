#[approck::http(GET /admin/sendgrid/test; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, _identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("SendGrid Test");

        // Get available senders from the config
        let sender_map = &app.sendgrid().sender_map;
        let sender_options: Vec<(&str, &str)> = sender_map
            .iter()
            .map(|(key, email)| (key.as_str(), email.as_str()))
            .collect();

        let mut form_panel =
            bux::component::add_cancel_form_panel("Send Test Email", "/admin/sendgrid/");

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::select::nilla::nilla_select(
                        "sender_key",
                        "Sender",
                        &sender_options,
                        None,
                    ))
                    (bux::input::text::string::name_label_value("to_email", "To Email:", None))
                    (bux::input::text::string::name_label_value("subject", "Subject:", Some("SendGrid Test Email")))
                }
                cell-6 {
                    (bux::input::textarea::string::name_label_value("message", "Message:", Some("This is a test email sent from SendGrid API.")))
                }
            }
        ));

        doc.add_body(html! {
            section {
                (form_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}
