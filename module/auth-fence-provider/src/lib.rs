pub mod sso;
pub mod web;

pub trait Identity: bux::Identity + std::fmt::Debug + auth_fence::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
    fn scope_email(&self) -> bool;
    fn scope_profile(&self) -> bool;
}

pub trait App: approck::App + approck_redis::App + approck_postgres::App + auth_fence::App {
    fn auth_fence_provider(&self) -> &ModuleStruct;
}

pub trait Document: bux::document::Base + bux::document::PageNav {}

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub aes_key: String,
    pub auth_code_ttl: i64,
    pub access_token_ttl: i64,
}

#[derive(serde::Deserialize)]
pub struct ModuleStruct {
    aes_key: String,
    auth_code_ttl: i64,
    access_token_ttl: i64,
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            aes_key: config.aes_key,
            auth_code_ttl: config.auth_code_ttl,
            access_token_ttl: config.access_token_ttl,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

impl ModuleStruct {
    pub fn aes_key(&self) -> Vec<u8> {
        // base64 decode the key to get the raw bytes
        base64_light::base64_decode(&self.aes_key)
    }
    pub fn auth_code_ttl(&self) -> i64 {
        self.auth_code_ttl
    }
    pub fn access_token_ttl(&self) -> i64 {
        self.access_token_ttl
    }
}
