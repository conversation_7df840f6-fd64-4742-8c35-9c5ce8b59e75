#[approck::api]
pub mod list {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub limit: Option<i64>,
        pub offset: Option<i64>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub logs: Vec<LogApiSend>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct LogApiSend {
        pub log_api_send_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub sender_key: String,
        pub from_number: String,
        pub to_number: String,
        pub message_body: String,
        pub request_ts: DateTimeUtc,
        pub response_ts: Option<DateTimeUtc>,
        pub response_status: Option<i32>,
        pub response_text: Option<String>,
        pub response_error: Option<String>,
        pub duration_ms: Option<i32>,
        pub success: bool,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;
        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $limit: &input.limit.unwrap_or(100),
                $offset: &input.offset.unwrap_or(0),
            };
            row = {
                log_api_send_uuid: Uuid,
                create_ts: DateTimeUtc,
                sender_key: String,
                from_number: String,
                to_number: String,
                message_body: String,
                request_ts: DateTimeUtc,
                response_ts: Option<DateTimeUtc>,
                response_status: Option<i32>,
                response_text: Option<String>,
                response_error: Option<String>,
                duration_ms: Option<i32>,
                success: bool,
            };
            SELECT
                log_api_send_uuid,
                create_ts,
                sender_key,
                from_number,
                to_number,
                message_body,
                request_ts,
                response_ts,
                response_status,
                response_text,
                response_error,
                duration_ms,
                success
            FROM api_twilio.log_api_send
            ORDER BY create_ts DESC
            LIMIT $limit OFFSET $offset
        )
        .await?;

        let logs = rows
            .into_iter()
            .map(|row| LogApiSend {
                log_api_send_uuid: row.log_api_send_uuid,
                create_ts: row.create_ts,
                sender_key: row.sender_key,
                from_number: row.from_number,
                to_number: row.to_number,
                message_body: row.message_body,
                request_ts: row.request_ts,
                response_ts: row.response_ts,
                response_status: row.response_status,
                response_text: row.response_text,
                response_error: row.response_error,
                duration_ms: row.duration_ms,
                success: row.success,
            })
            .collect();

        Ok(Output { logs })
    }
}

#[approck::api]
pub mod summary {
    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub summary: SendSummary,
    }

    #[granite::gtype(ApiOutput)]
    pub struct SendSummary {
        pub total_messages: i64,
        pub successful_messages: i64,
        pub failed_messages: i64,
    }

    pub async fn call(app: App, _identity: Identity, _input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;
        let row = granite::pg_row!(
            db = dbcx;
            args = {};
            row = {
                total_messages: i64,
                successful_messages: i64,
                failed_messages: i64,
            };
            SELECT
                COUNT(*) as total_messages,
                COUNT(*) FILTER (WHERE success = true) as successful_messages,
                COUNT(*) FILTER (WHERE success = false) as failed_messages
            FROM api_twilio.log_api_send
        )
        .await?;

        let summary = SendSummary {
            total_messages: row.total_messages,
            successful_messages: row.successful_messages,
            failed_messages: row.failed_messages,
        };

        Ok(Output { summary })
    }
}
