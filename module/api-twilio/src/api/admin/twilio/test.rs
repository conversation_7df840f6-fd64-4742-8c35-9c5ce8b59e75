#[approck::api]
pub mod admin_twilio_test_send {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub sender_key: String,
        pub to_number: String,
        pub message: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub success: bool,
        pub message: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Output> {
        // Validate that the sender key exists
        let sender_map = &app.twilio().sender_map;
        if !sender_map.contains_key(&input.sender_key) {
            return Ok(Output {
                success: false,
                message: format!("Unknown sender key: {}", input.sender_key),
            });
        }

        // Send the test SMS
        match app
            .twilio()
            .send_sms(app, &input.sender_key, &input.to_number, &input.message)
            .await
        {
            Ok(()) => Ok(Output {
                success: true,
                message: "Test SMS sent successfully".to_string(),
            }),
            Err(e) => Ok(Output {
                success: false,
                message: format!("Failed to send test SMS: {}", e),
            }),
        }
    }
}
