#[approck::http(GET /admin/twilio/test; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, _identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Twilio Test");

        // Get available senders from the config
        let sender_map = &app.twilio().sender_map;
        let sender_options: Vec<(&str, &str)> = sender_map
            .iter()
            .map(|(key, number)| (key.as_str(), number.as_str()))
            .collect();

        let mut form_panel =
            bux::component::add_cancel_form_panel("Send Test SMS", "/admin/twilio/");

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::select::nilla::nilla_select(
                        "sender_key",
                        "Sender",
                        &sender_options,
                        None,
                    ))
                    (bux::input::text::string::name_label_value("to_number", "To Number:", None))
                }
                cell-6 {
                    (bux::input::textarea::string::name_label_value("message", "Message:", Some("This is a test SMS sent from Twilio API.")))
                }
            }
        ));

        doc.add_body(html! {
            section {
                (form_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}
