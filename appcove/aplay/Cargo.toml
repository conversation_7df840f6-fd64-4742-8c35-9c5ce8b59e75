[package]
name = "aplay"
version = "0.1.0"
edition = "2021"

[package.metadata.acp]
app.port = 3021
extends = ["approck", "bux", "granite"]

[dependencies]
approck = { path = "../../lib/approck" }
bux = { path = "../../lib/bux" }
granite = { path = "../../lib/granite" }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }