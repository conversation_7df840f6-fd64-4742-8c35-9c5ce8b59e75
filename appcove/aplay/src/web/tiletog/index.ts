// -------------------------------------------------------------------------------------------------
// 1. Import Components
import "./index.mcss";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SEC } from "@granite/lib.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements
const $canvas = SEC(HTMLCanvasElement, document, "#game-canvas");

// -------------------------------------------------------------------------------------------------
// 4. State & Data

// Canvas & Grid
const ctx = $canvas.getContext("2d") as CanvasRenderingContext2D;

const SQUARE_SIZE_CM = 1;
const SQUARE_SIZE_PX = cm_to_px(SQUARE_SIZE_CM);

let canvas_width = 0;
let canvas_height = 0;
let grid_width = 0;
let grid_height = 0;

let grid: boolean[][] = [];

// Mouse state
const mouse = {
    is_drawing: false,
    draw_mode: null as "draw" | "erase" | null,
};

// -------------------------------------------------------------------------------------------------
// 5. Library & Main

function cm_to_px(cm: number): number {
    const dpi = 96;
    const inches = cm / 2.54;
    return inches * dpi;
}

function handle_mouse_down(e: MouseEvent) {
    mouse.is_drawing = true;
    if (e.button === 0) { // Left click
        mouse.draw_mode = "draw";
    } else if (e.button === 2) { // Right click
        mouse.draw_mode = "erase";
    }
    update_square_from_event(e);
}

function handle_mouse_up() {
    mouse.is_drawing = false;
    mouse.draw_mode = null;
}

function handle_mouse_move(e: MouseEvent) {
    if (mouse.is_drawing) {
        update_square_from_event(e);
    }
}

function update_square_from_event(e: MouseEvent) {
    const rect = $canvas.getBoundingClientRect();
    const scale_x = $canvas.width / rect.width;
    const scale_y = $canvas.height / rect.height;
    const x = Math.floor((e.clientX - rect.left) * scale_x / SQUARE_SIZE_PX);
    const y = Math.floor((e.clientY - rect.top) * scale_y / SQUARE_SIZE_PX);

    if (x >= 0 && x < grid_width && y >= 0 && y < grid_height) {
        const row = grid[y];
        if (row) {
            if (mouse.draw_mode === "draw") {
                row[x] = true;
            } else if (mouse.draw_mode === "erase") {
                row[x] = false;
            }
        }
    }
}

function get_cell(x: number, y: number): boolean {
    if (y < 0 || y >= grid_height || x < 0 || x >= grid_width) {
        return false; // Out of bounds is considered white/false
    }
    const row = grid[y];
    return row ? row[x] || false : false;
}

function draw_corner_triangles(x: number, y: number): void {
    const current = get_cell(x, y);
    const up = get_cell(x, y - 1);
    const down = get_cell(x, y + 1);
    const left = get_cell(x - 1, y);
    const right = get_cell(x + 1, y);

    // Only draw triangles if current cell is white
    if (current) return;

    const square_x = x * SQUARE_SIZE_PX;
    const square_y = y * SQUARE_SIZE_PX;

    ctx.fillStyle = "#000";

    // Three-direction cases: draw quarter-sized triangles adjacent to black squares
    // Imagine the square divided by two diagonal lines creating 4 triangular quarters

    const center_x = square_x + SQUARE_SIZE_PX / 2;
    const center_y = square_y + SQUARE_SIZE_PX / 2;

    // Case 1: Black in West, South, East (leaving North white) - draw left, bottom, right quarter triangles
    if (left && down && right && !up) {
        // Left quarter triangle (adjacent to West black)
        ctx.beginPath();
        ctx.moveTo(square_x, square_y); // Top-left corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.closePath();
        ctx.fill();

        // Bottom quarter triangle (adjacent to South black)
        ctx.beginPath();
        ctx.moveTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.closePath();
        ctx.fill();

        // Right quarter triangle (adjacent to East black)
        ctx.beginPath();
        ctx.moveTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.closePath();
        ctx.fill();
    } // Case 2: Black in West, North, East (leaving South white) - draw left, top, right quarter triangles
    else if (left && up && right && !down) {
        // Left quarter triangle (adjacent to West black)
        ctx.beginPath();
        ctx.moveTo(square_x, square_y); // Top-left corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.closePath();
        ctx.fill();

        // Top quarter triangle (adjacent to North black)
        ctx.beginPath();
        ctx.moveTo(square_x, square_y); // Top-left corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.closePath();
        ctx.fill();

        // Right quarter triangle (adjacent to East black)
        ctx.beginPath();
        ctx.moveTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.closePath();
        ctx.fill();
    } // Case 3: Black in North, West, South (leaving East white) - draw top, left, bottom quarter triangles
    else if (up && left && down && !right) {
        // Top quarter triangle (adjacent to North black)
        ctx.beginPath();
        ctx.moveTo(square_x, square_y); // Top-left corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.closePath();
        ctx.fill();

        // Left quarter triangle (adjacent to West black)
        ctx.beginPath();
        ctx.moveTo(square_x, square_y); // Top-left corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.closePath();
        ctx.fill();

        // Bottom quarter triangle (adjacent to South black)
        ctx.beginPath();
        ctx.moveTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.closePath();
        ctx.fill();
    } // Case 4: Black in North, East, South (leaving West white) - draw top, right, bottom quarter triangles
    else if (up && right && down && !left) {
        // Top quarter triangle (adjacent to North black)
        ctx.beginPath();
        ctx.moveTo(square_x, square_y); // Top-left corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.closePath();
        ctx.fill();

        // Right quarter triangle (adjacent to East black)
        ctx.beginPath();
        ctx.moveTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.closePath();
        ctx.fill();

        // Bottom quarter triangle (adjacent to South black)
        ctx.beginPath();
        ctx.moveTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.lineTo(center_x, center_y); // Center
        ctx.lineTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.closePath();
        ctx.fill();
    } // Two-direction cases: single triangles in corners

    // Top-right triangle: black above and right, white below and left
    else if (up && right && !down && !left) {
        ctx.beginPath();
        ctx.moveTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.lineTo(square_x, square_y); // Top-left corner
        ctx.closePath();
        ctx.fill();
    } // Bottom-right triangle: black right and below, white left and above
    else if (right && down && !left && !up) {
        ctx.beginPath();
        ctx.moveTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.lineTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.closePath();
        ctx.fill();
    } // Bottom-left triangle: black below and left, white above and right
    else if (down && left && !up && !right) {
        ctx.beginPath();
        ctx.moveTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.lineTo(square_x, square_y); // Top-left corner
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y + SQUARE_SIZE_PX); // Bottom-right corner
        ctx.closePath();
        ctx.fill();
    } // Top-left triangle: black left and above, white right and below
    else if (left && up && !right && !down) {
        ctx.beginPath();
        ctx.moveTo(square_x, square_y); // Top-left corner
        ctx.lineTo(square_x + SQUARE_SIZE_PX, square_y); // Top-right corner
        ctx.lineTo(square_x, square_y + SQUARE_SIZE_PX); // Bottom-left corner
        ctx.closePath();
        ctx.fill();
    }
}

function draw() {
    ctx.clearRect(0, 0, canvas_width, canvas_height);

    // Draw grid and squares
    for (let y = 0; y < grid_height; y++) {
        const row = grid[y];
        if (row) {
            for (let x = 0; x < grid_width; x++) {
                const cell = row[x];
                // Main canvas
                ctx.fillStyle = cell ? "#000" : "#fff";
                ctx.fillRect(
                    x * SQUARE_SIZE_PX,
                    y * SQUARE_SIZE_PX,
                    SQUARE_SIZE_PX,
                    SQUARE_SIZE_PX,
                );
                ctx.strokeStyle = "#ccc";
                ctx.strokeRect(
                    x * SQUARE_SIZE_PX,
                    y * SQUARE_SIZE_PX,
                    SQUARE_SIZE_PX,
                    SQUARE_SIZE_PX,
                );

                // Draw triangles for white squares with specific black neighbor patterns
                if (!cell) { // Only for white squares
                    draw_corner_triangles(x, y);
                }
            }
        }
    }

    // Draw minimap
    const minimap_tile_size = 2;
    const minimap_size_x = grid_width * minimap_tile_size;
    const minimap_size_y = grid_height * minimap_tile_size;
    const minimap_x = canvas_width - minimap_size_x - 10;
    const minimap_y = 10;

    ctx.fillStyle = "rgba(255, 255, 255, 0.8)";
    ctx.fillRect(minimap_x - 1, minimap_y - 1, minimap_size_x + 2, minimap_size_y + 2);
    ctx.strokeStyle = "#000";
    ctx.strokeRect(minimap_x - 1, minimap_y - 1, minimap_size_x + 2, minimap_size_y + 2);

    for (let y = 0; y < grid_height; y++) {
        const row = grid[y];
        if (row) {
            for (let x = 0; x < grid_width; x++) {
                const cell = row[x];
                ctx.fillStyle = cell ? "#000" : "#fff";
                ctx.fillRect(
                    minimap_x + x * minimap_tile_size,
                    minimap_y + y * minimap_tile_size,
                    minimap_tile_size,
                    minimap_tile_size,
                );
            }
        }
    }

    requestAnimationFrame(draw);
}

function setup_event_listeners(): void {
    $canvas.addEventListener("mousedown", handle_mouse_down);
    $canvas.addEventListener("mouseup", handle_mouse_up);
    $canvas.addEventListener("mousemove", handle_mouse_move);
    $canvas.addEventListener("contextmenu", (e) => e.preventDefault());
    globalThis.addEventListener("mouseup", handle_mouse_up);
}

function resize_canvas(): void {
    canvas_width = $canvas.clientWidth;
    canvas_height = $canvas.clientHeight;
    $canvas.width = canvas_width;
    $canvas.height = canvas_height;

    grid_width = Math.floor(canvas_width / SQUARE_SIZE_PX) + 1;
    grid_height = Math.floor(canvas_height / SQUARE_SIZE_PX) + 1;

    // Re-initialize grid, preserving existing state
    const new_grid: boolean[][] = Array(grid_height).fill(null).map(() =>
        Array(grid_width).fill(false)
    );
    for (let y = 0; y < Math.min(grid.length, grid_height); y++) {
        const row = grid[y];
        const new_row = new_grid[y];
        if (row && new_row) {
            for (let x = 0; x < Math.min(row.length, grid_width); x++) {
                const cell = row[x];
                if (cell) {
                    new_row[x] = cell;
                }
            }
        }
    }
    grid = new_grid;
}

function initialize_game(): void {
    resize_canvas();
    setup_event_listeners();
    globalThis.addEventListener("resize", resize_canvas);
    draw();
}

// Start the game
initialize_game();
