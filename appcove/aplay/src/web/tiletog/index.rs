#[approck::http(GET /tiletog/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Tiletog");
        doc.set_body_display_fixed();
        doc.hide_page_nav();

        doc.add_body(html!(
            canvas id="game-canvas" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" {}
        ));

        Response::HTML(doc.into())
    }
}