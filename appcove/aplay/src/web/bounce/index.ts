// -------------------------------------------------------------------------------------------------
// 1. Import Components
import "./index.mcss";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SEC } from "@granite/lib.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements
const $canvas = SEC(HTMLCanvasElement, document, "canvas");

// -------------------------------------------------------------------------------------------------
// 4. State & Data

// Physics constants
const GRAVITY = 9.8;
const DRAG_FACTOR = 0.999;
const MOUSE_ATTRACTION_STRENGTH = 5.0;
const MOUSE_CLICK_ATTRACTION_MULTIPLIER = 10;
const BALL_SIZE = 100;
const BALL_RADIUS = BALL_SIZE / 2;

// Radar constants
const RADAR_SIZE = 120;
const RADAR_SCALE = 0.3; // Scale factor for velocity vectors

// Canvas context
const ctx = $canvas.getContext("2d") as CanvasRenderingContext2D;

// Ball state
const ball = {
    x: 200,
    y: 200,
    vx: 0, // velocity x
    vy: 0, // velocity y
    radius: BALL_RADIUS,
    mass: 1,
};

// Mouse state
const mouse = {
    x: 0,
    y: 0,
    is_on_canvas: false,
    is_clicked: false,
};

// Canvas dimensions
let canvas_width = 0;
let canvas_height = 0;

// Animation state
// (no state needed for continuous game loop)

// -------------------------------------------------------------------------------------------------
// 5. Library & Main

function get_ball_speed(): number {
    return Math.sqrt(ball.vx * ball.vx + ball.vy * ball.vy);
}

function get_ball_color(): string {
    const speed = get_ball_speed();
    const max_speed = 256; // Normalize color based on this max speed
    const normalized_speed = Math.min(speed / max_speed, 1);

    // Color transition: blue (slow) -> green -> yellow -> red (fast)
    if (normalized_speed < 0.33) {
        // Blue to green
        const ratio = normalized_speed / 0.33;
        const red = 0;
        const green = Math.floor(255 * ratio);
        const blue = Math.floor(255 * (1 - ratio));
        return `rgb(${red}, ${green}, ${blue})`;
    } else if (normalized_speed < 0.66) {
        // Green to yellow
        const ratio = (normalized_speed - 0.33) / 0.33;
        const red = Math.floor(255 * ratio);
        const green = 255;
        const blue = 0;
        return `rgb(${red}, ${green}, ${blue})`;
    } else {
        // Yellow to red
        const ratio = (normalized_speed - 0.66) / 0.34;
        const red = 255;
        const green = Math.floor(255 * (1 - ratio));
        const blue = 0;
        return `rgb(${red}, ${green}, ${blue})`;
    }
}

function apply_gravity(dt: number): void {
    ball.vy += GRAVITY * dt;
}

function apply_drag(): void {
    ball.vx *= DRAG_FACTOR;
    ball.vy *= DRAG_FACTOR;
}

function apply_mouse_attraction(dt: number): void {
    if (!mouse.is_on_canvas) return;

    const dx = mouse.x - ball.x;
    const dy = mouse.y - ball.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance > 0) {
        const force_multiplier = mouse.is_clicked ? MOUSE_CLICK_ATTRACTION_MULTIPLIER : 1;
        const force = MOUSE_ATTRACTION_STRENGTH * force_multiplier;

        const fx = (dx / distance) * force * dt;
        const fy = (dy / distance) * force * dt;

        ball.vx += fx;
        ball.vy += fy;
    }
}

function handle_wall_collisions(): void {
    // Left wall
    if (ball.x - ball.radius <= 0) {
        ball.x = ball.radius;
        ball.vx = Math.abs(ball.vx) * 0.8; // Bounce with some energy loss
    }

    // Right wall
    if (ball.x + ball.radius >= canvas_width) {
        ball.x = canvas_width - ball.radius;
        ball.vx = -Math.abs(ball.vx) * 0.8;
    }

    // Top wall
    if (ball.y - ball.radius <= 0) {
        ball.y = ball.radius;
        ball.vy = Math.abs(ball.vy) * 0.8;
    }

    // Bottom wall
    if (ball.y + ball.radius >= canvas_height) {
        ball.y = canvas_height - ball.radius;
        ball.vy = -Math.abs(ball.vy) * 0.8;
    }
}

function update_ball_position(dt: number): void {
    ball.x += ball.vx * dt;
    ball.y += ball.vy * dt;
}

function update_physics(dt: number): void {
    apply_gravity(dt);
    apply_mouse_attraction(dt);
    apply_drag();
    update_ball_position(dt);
    handle_wall_collisions();
}

function draw_ball(): void {
    ctx.beginPath();
    ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
    ctx.fillStyle = get_ball_color();
    ctx.fill();
    ctx.closePath();
}

function draw_debug_info(): void {
    ctx.fillStyle = "rgba(255, 255, 255, 0.8)";
    ctx.font = "14px monospace";

    const speed = get_ball_speed().toFixed(1);
    const pos_x = ball.x.toFixed(1);
    const pos_y = ball.y.toFixed(1);
    const vel_x = ball.vx.toFixed(1);
    const vel_y = ball.vy.toFixed(1);

    ctx.fillText(`Speed: ${speed}`, 10, 20);
    ctx.fillText(`Position: (${pos_x}, ${pos_y})`, 10, 40);
    ctx.fillText(`Velocity: (${vel_x}, ${vel_y})`, 10, 60);
    ctx.fillText(`Mouse: ${mouse.is_on_canvas ? "On Canvas" : "Off Canvas"}`, 10, 80);
    ctx.fillText(`Click: ${mouse.is_clicked ? "Active" : "Inactive"}`, 10, 100);
}

function draw_radar(): void {
    const radar_center_x = canvas_width - RADAR_SIZE / 2 - 20;
    const radar_center_y = RADAR_SIZE / 2 + 20;

    // Draw radar background circle
    ctx.beginPath();
    ctx.arc(radar_center_x, radar_center_y, RADAR_SIZE / 2, 0, Math.PI * 2);
    ctx.fillStyle = "rgba(0, 0, 0, 0.7)";
    ctx.fill();
    ctx.strokeStyle = "rgba(0, 255, 0, 0.5)";
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw radar grid lines
    ctx.strokeStyle = "rgba(0, 255, 0, 0.3)";
    ctx.lineWidth = 1;

    // Cross hairs
    ctx.beginPath();
    ctx.moveTo(radar_center_x - RADAR_SIZE / 2, radar_center_y);
    ctx.lineTo(radar_center_x + RADAR_SIZE / 2, radar_center_y);
    ctx.moveTo(radar_center_x, radar_center_y - RADAR_SIZE / 2);
    ctx.lineTo(radar_center_x, radar_center_y + RADAR_SIZE / 2);
    ctx.stroke();

    // Inner circle
    ctx.beginPath();
    ctx.arc(radar_center_x, radar_center_y, RADAR_SIZE / 4, 0, Math.PI * 2);
    ctx.stroke();

    // Draw ball velocity vector (red arrow)
    if (ball.vx !== 0 || ball.vy !== 0) {
        const vel_length = Math.sqrt(ball.vx * ball.vx + ball.vy * ball.vy) * RADAR_SCALE;
        const vel_angle = Math.atan2(ball.vy, ball.vx);
        const vel_end_x = radar_center_x +
            Math.cos(vel_angle) * Math.min(vel_length, RADAR_SIZE / 2 - 5);
        const vel_end_y = radar_center_y +
            Math.sin(vel_angle) * Math.min(vel_length, RADAR_SIZE / 2 - 5);

        // Draw velocity arrow
        ctx.strokeStyle = "red";
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(radar_center_x, radar_center_y);
        ctx.lineTo(vel_end_x, vel_end_y);
        ctx.stroke();

        // Draw arrowhead
        const arrow_size = 8;
        const arrow_angle = Math.PI / 6;
        ctx.beginPath();
        ctx.moveTo(vel_end_x, vel_end_y);
        ctx.lineTo(
            vel_end_x - arrow_size * Math.cos(vel_angle - arrow_angle),
            vel_end_y - arrow_size * Math.sin(vel_angle - arrow_angle),
        );
        ctx.moveTo(vel_end_x, vel_end_y);
        ctx.lineTo(
            vel_end_x - arrow_size * Math.cos(vel_angle + arrow_angle),
            vel_end_y - arrow_size * Math.sin(vel_angle + arrow_angle),
        );
        ctx.stroke();
    }

    // Draw mouse attraction vector (blue arrow)
    if (mouse.is_on_canvas) {
        const dx = mouse.x - ball.x;
        const dy = mouse.y - ball.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0) {
            const force_multiplier = mouse.is_clicked ? MOUSE_CLICK_ATTRACTION_MULTIPLIER : 1;
            const force = MOUSE_ATTRACTION_STRENGTH * force_multiplier;
            const attraction_length = force * RADAR_SCALE * 10; // Scale up for visibility
            const attraction_angle = Math.atan2(dy, dx);
            const attraction_end_x = radar_center_x +
                Math.cos(attraction_angle) * Math.min(attraction_length, RADAR_SIZE / 2 - 5);
            const attraction_end_y = radar_center_y +
                Math.sin(attraction_angle) * Math.min(attraction_length, RADAR_SIZE / 2 - 5);

            // Draw attraction arrow
            ctx.strokeStyle = mouse.is_clicked ? "cyan" : "blue";
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(radar_center_x, radar_center_y);
            ctx.lineTo(attraction_end_x, attraction_end_y);
            ctx.stroke();

            // Draw arrowhead
            const arrow_size = 6;
            const arrow_angle = Math.PI / 6;
            ctx.beginPath();
            ctx.moveTo(attraction_end_x, attraction_end_y);
            ctx.lineTo(
                attraction_end_x - arrow_size * Math.cos(attraction_angle - arrow_angle),
                attraction_end_y - arrow_size * Math.sin(attraction_angle - arrow_angle),
            );
            ctx.moveTo(attraction_end_x, attraction_end_y);
            ctx.lineTo(
                attraction_end_x - arrow_size * Math.cos(attraction_angle + arrow_angle),
                attraction_end_y - arrow_size * Math.sin(attraction_angle + arrow_angle),
            );
            ctx.stroke();
        }
    }

    // Draw radar labels
    ctx.fillStyle = "rgba(0, 255, 0, 0.8)";
    ctx.font = "12px monospace";
    ctx.fillText("RADAR", radar_center_x - 25, radar_center_y + RADAR_SIZE / 2 + 15);
    ctx.fillStyle = "red";
    ctx.fillText("VEL", radar_center_x - RADAR_SIZE / 2 + 5, radar_center_y - RADAR_SIZE / 2 + 15);
    ctx.fillStyle = mouse.is_clicked ? "cyan" : "blue";
    ctx.fillText("ATT", radar_center_x + RADAR_SIZE / 2 - 25, radar_center_y - RADAR_SIZE / 2 + 15);
}

function render(): void {
    // Clear canvas
    ctx.clearRect(0, 0, canvas_width, canvas_height);

    // Draw ball
    draw_ball();

    // Draw debug info
    draw_debug_info();

    // Draw radar
    draw_radar();
}

function resize_canvas(): void {
    canvas_width = $canvas.clientWidth;
    canvas_height = $canvas.clientHeight;
    $canvas.width = canvas_width;
    $canvas.height = canvas_height;

    // Keep ball within bounds after resize
    ball.x = Math.max(ball.radius, Math.min(ball.x, canvas_width - ball.radius));
    ball.y = Math.max(ball.radius, Math.min(ball.y, canvas_height - ball.radius));
}

function setup_event_listeners(): void {
    // Mouse movement tracking
    $canvas.addEventListener("mousemove", (event) => {
        const rect = $canvas.getBoundingClientRect();
        mouse.x = event.clientX - rect.left;
        mouse.y = event.clientY - rect.top;
    });

    // Mouse enter/leave tracking
    $canvas.addEventListener("mouseenter", () => {
        mouse.is_on_canvas = true;
    });

    $canvas.addEventListener("mouseleave", () => {
        mouse.is_on_canvas = false;
    });

    // Mouse click tracking
    $canvas.addEventListener("mousedown", () => {
        mouse.is_clicked = true;
    });

    $canvas.addEventListener("mouseup", () => {
        mouse.is_clicked = false;
    });

    // Global mouse up (in case mouse is released outside canvas)
    globalThis.addEventListener("mouseup", () => {
        mouse.is_clicked = false;
    });

    // Window resize
    globalThis.addEventListener("resize", resize_canvas);
}

function game_loop(): void {
    const dt = 1 / 60; // Fixed timestep for consistent physics

    update_physics(dt);
    render();

    requestAnimationFrame(game_loop);
}

function initialize_game(): void {
    resize_canvas();
    setup_event_listeners();

    // Position ball in center of canvas
    ball.x = canvas_width / 2;
    ball.y = canvas_height / 2;

    // Start game loop
    game_loop();
}

// Start the game
initialize_game();
