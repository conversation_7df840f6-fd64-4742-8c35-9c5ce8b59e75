#[approck::http(GET /dashboard/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Dashboard");

        doc.add_body(html! {
            div.container.mt-4 {
                div.row {
                    div.col-12 {
                        h1 { "Dashboard" }
                        p { "Welcome to PM5" }
                    }
                }
            }
        });

        Response::HTML(doc.into())
    }
}
