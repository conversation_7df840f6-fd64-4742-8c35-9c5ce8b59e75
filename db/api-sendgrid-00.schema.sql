CREATE SCHEMA IF NOT EXISTS api_sendgrid;

CREATE TABLE api_sendgrid.log_api_send (
    log_api_send_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    sender_key varchar(64) NOT NULL,
    from_email varchar(256) NOT NULL,
    to_email varchar(256) NOT NULL,
    subject varchar(512) NOT NULL,
    message_body text NOT NULL,
    request_data jsonb NOT NULL DEFAULT '{}'::jsonb,
    request_ts timestamptz(6) NOT NULL,
    response_ts timestamptz(6),
    response_status integer,
    response_text text,
    response_error text,
    response_data jsonb NOT NULL DEFAULT '{}'::jsonb,
    duration_ms integer,
    success boolean NOT NULL DEFAULT false,
    CONSTRAINT log_api_send_pkey PRIMARY KEY (log_api_send_uuid)
);

CREATE INDEX idx_api_sendgrid_log_api_send_create_ts ON api_sendgrid.log_api_send (create_ts);
CREATE INDEX idx_api_sendgrid_log_api_send_success ON api_sendgrid.log_api_send (success);
CREATE INDEX idx_api_sendgrid_log_api_send_to_email ON api_sendgrid.log_api_send (to_email);
CREATE INDEX idx_api_sendgrid_log_api_send_sender_key ON api_sendgrid.log_api_send (sender_key);