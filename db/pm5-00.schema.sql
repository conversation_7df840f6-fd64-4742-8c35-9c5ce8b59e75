-- PM5 Database Schema
-- Project Management System v5

CREATE SCHEMA IF NOT EXISTS pm5;

-- Project status enumeration
CREATE TYPE pm5.project_status AS ENUM (
    'Planning',
    'Active', 
    'OnHold',
    'Completed',
    'Cancelled'
);

-- Task priority enumeration
CREATE TYPE pm5.task_priority AS ENUM (
    'Low',
    'Medium', 
    'High',
    'Critical'
);

-- Task status enumeration
CREATE TYPE pm5.task_status AS ENUM (
    'Todo',
    'InProgress',
    'Review',
    'Done',
    'Blocked'
);

-- Projects table
CREATE TABLE pm5.project (
    project_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    create_ts timestamptz NOT NULL DEFAULT now(),
    update_ts timestamptz NOT NULL DEFAULT now(),
    created_by_identity_uuid uuid NOT NULL,
    updated_by_identity_uuid uuid NOT NULL,
    name varchar(255) NOT NULL,
    description text,
    status pm5.project_status NOT NULL DEFAULT 'Planning',
    start_date date,
    end_date date,
    budget_amount decimal(12,2),
    budget_currency varchar(3) DEFAULT 'USD',
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT project_pkey PRIMARY KEY (project_uuid),
    CONSTRAINT project_created_by_fkey FOREIGN KEY (created_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT project_updated_by_fkey FOREIGN KEY (updated_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- Project members table (many-to-many relationship between projects and identities)
CREATE TABLE pm5.project_member (
    project_uuid uuid NOT NULL,
    identity_uuid uuid NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    added_by_identity_uuid uuid NOT NULL,
    role varchar(64) NOT NULL DEFAULT 'Member', -- Member, Lead, Admin
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT project_member_pkey PRIMARY KEY (project_uuid, identity_uuid),
    CONSTRAINT project_member_project_fkey FOREIGN KEY (project_uuid) REFERENCES pm5.project(project_uuid) ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT project_member_identity_fkey FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT project_member_added_by_fkey FOREIGN KEY (added_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- Tasks table
CREATE TABLE pm5.task (
    task_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    project_uuid uuid NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    update_ts timestamptz NOT NULL DEFAULT now(),
    created_by_identity_uuid uuid NOT NULL,
    updated_by_identity_uuid uuid NOT NULL,
    assigned_to_identity_uuid uuid,
    parent_task_uuid uuid, -- For subtasks
    title varchar(255) NOT NULL,
    description text,
    status pm5.task_status NOT NULL DEFAULT 'Todo',
    priority pm5.task_priority NOT NULL DEFAULT 'Medium',
    due_date timestamptz,
    estimated_hours decimal(6,2),
    actual_hours decimal(6,2),
    completion_percentage integer DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT task_pkey PRIMARY KEY (task_uuid),
    CONSTRAINT task_project_fkey FOREIGN KEY (project_uuid) REFERENCES pm5.project(project_uuid) ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT task_created_by_fkey FOREIGN KEY (created_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT task_updated_by_fkey FOREIGN KEY (updated_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT task_assigned_to_fkey FOREIGN KEY (assigned_to_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE SET NULL ON UPDATE RESTRICT,
    CONSTRAINT task_parent_fkey FOREIGN KEY (parent_task_uuid) REFERENCES pm5.task(task_uuid) ON DELETE CASCADE ON UPDATE RESTRICT
);

-- Task comments table
CREATE TABLE pm5.task_comment (
    comment_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    task_uuid uuid NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    created_by_identity_uuid uuid NOT NULL,
    comment_text text NOT NULL,
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT task_comment_pkey PRIMARY KEY (comment_uuid),
    CONSTRAINT task_comment_task_fkey FOREIGN KEY (task_uuid) REFERENCES pm5.task(task_uuid) ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT task_comment_created_by_fkey FOREIGN KEY (created_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- Task attachments table
CREATE TABLE pm5.task_attachment (
    attachment_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    task_uuid uuid NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    uploaded_by_identity_uuid uuid NOT NULL,
    filename varchar(255) NOT NULL,
    file_size bigint NOT NULL,
    mime_type varchar(255),
    file_path text NOT NULL, -- Path to stored file
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT task_attachment_pkey PRIMARY KEY (attachment_uuid),
    CONSTRAINT task_attachment_task_fkey FOREIGN KEY (task_uuid) REFERENCES pm5.task(task_uuid) ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT task_attachment_uploaded_by_fkey FOREIGN KEY (uploaded_by_identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

-- Time tracking table
CREATE TABLE pm5.time_entry (
    time_entry_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    task_uuid uuid NOT NULL,
    identity_uuid uuid NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    start_time timestamptz NOT NULL,
    end_time timestamptz,
    duration_minutes integer, -- Calculated field
    description text,
    active boolean NOT NULL DEFAULT true,
    CONSTRAINT time_entry_pkey PRIMARY KEY (time_entry_uuid),
    CONSTRAINT time_entry_task_fkey FOREIGN KEY (task_uuid) REFERENCES pm5.task(task_uuid) ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT time_entry_identity_fkey FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT time_entry_duration_check CHECK (end_time IS NULL OR end_time > start_time)
);

-- Indexes for better performance
CREATE INDEX idx_project_status ON pm5.project(status);
CREATE INDEX idx_project_active ON pm5.project(active);
CREATE INDEX idx_project_created_by ON pm5.project(created_by_identity_uuid);

CREATE INDEX idx_project_member_project ON pm5.project_member(project_uuid);
CREATE INDEX idx_project_member_identity ON pm5.project_member(identity_uuid);
CREATE INDEX idx_project_member_active ON pm5.project_member(active);

CREATE INDEX idx_task_project ON pm5.task(project_uuid);
CREATE INDEX idx_task_status ON pm5.task(status);
CREATE INDEX idx_task_priority ON pm5.task(priority);
CREATE INDEX idx_task_assigned_to ON pm5.task(assigned_to_identity_uuid);
CREATE INDEX idx_task_parent ON pm5.task(parent_task_uuid);
CREATE INDEX idx_task_due_date ON pm5.task(due_date);
CREATE INDEX idx_task_active ON pm5.task(active);

CREATE INDEX idx_task_comment_task ON pm5.task_comment(task_uuid);
CREATE INDEX idx_task_comment_created_by ON pm5.task_comment(created_by_identity_uuid);
CREATE INDEX idx_task_comment_create_ts ON pm5.task_comment(create_ts);

CREATE INDEX idx_task_attachment_task ON pm5.task_attachment(task_uuid);
CREATE INDEX idx_task_attachment_uploaded_by ON pm5.task_attachment(uploaded_by_identity_uuid);

CREATE INDEX idx_time_entry_task ON pm5.time_entry(task_uuid);
CREATE INDEX idx_time_entry_identity ON pm5.time_entry(identity_uuid);
CREATE INDEX idx_time_entry_start_time ON pm5.time_entry(start_time);
