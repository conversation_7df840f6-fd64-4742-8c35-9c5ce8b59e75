

INSERT INTO df4l_admin.identity (identity_uuid, perm_web_usage, perm_api_usage, perm_agency_read, perm_agency_write, perm_advisor_read, perm_advisor_write, perm_client_read, perm_client_write) 
SELECT identity_uuid, true, true, true, true, true, true, true, true FROM auth_fence.identity;



-- iCover API scopes
INSERT INTO auth_fence_provider.oauth2_scope (scope_id, name, description, active) VALUES
('d2c:read', 'Debt2Capital Read', 'Read access to Debt2Capital data', true);

-- Legal documents
INSERT INTO legal_plane.document (document_uuid, document_psid, active, name, body_markdown, revision) VALUES
(public.uuidv7(), 'AdvisorAgreement', true, 'Advisor Agreement', '# Advisor Agreement

## Terms and Conditions

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

### Section 1: Advisor Responsibilities

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

- Lorem ipsum dolor sit amet
- Consectetur adipiscing elit
- Sed do eiusmod tempor incididunt
- Ut labore et dolore magna aliqua

### Section 2: Compensation and Terms

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.

Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.

### Section 3: Confidentiality

Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.

**By signing this agreement, you acknowledge that you have read, understood, and agree to be bound by these terms and conditions.**', '1.0');

-- Legal document version history
INSERT INTO legal_plane.document_version (document_version_uuid, document_uuid, name, body_markdown, revision)
SELECT public.uuidv7(), document_uuid, name, body_markdown, revision
FROM legal_plane.document 
WHERE document_psid = 'AdvisorAgreement';
