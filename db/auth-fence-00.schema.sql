CREATE SCHEMA IF NOT EXISTS auth_fence;

CREATE TABLE "auth_fence"."auth_log" (
  "auth_log_uuid" uuid DEFAULT public.uuidv7() NOT NULL,
  "create_addr" inet NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "session_token" varchar(255) COLLATE "pg_catalog"."default",
  "user_esid" varchar(255) COLLATE "pg_catalog"."default",
  "user_email" varchar(255) COLLATE "pg_catalog"."default",
  "auth_type" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "auth_action" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "auth_provider" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "success" bool NOT NULL DEFAULT false,
  "blocked" bool NOT NULL DEFAULT false,
  "data" jsonb NOT NULL DEFAULT '{}'::jsonb,
  CONSTRAINT auth_log_pkey PRIMARY KEY (auth_log_uuid)
);


-- some-uuid, partner_edit, [], "edit partner information"


CREATE TABLE "auth_fence"."permission" (
  "permission_uuid" uuid DEFAULT public.uuidv7() NOT NULL,
  "permission_psid" varchar(64) NOT NULL,
  "func" varchar(64) NULL,
  "args" text[] not null default '{}'::text[],
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "name" varchar(255) NOT NULL,
  "description" text,
  CONSTRAINT permission_pkey PRIMARY KEY (permission_uuid)
);

CREATE UNIQUE INDEX "auth_fence.permission.permission_psid.index" ON "auth_fence"."permission" USING btree ("permission_psid");


CREATE TABLE "auth_fence"."role" (
  "role_uuid" uuid DEFAULT public.uuidv7() NOT NULL,
  "role_psid" varchar(64) NOT NULL,
  "role_gsid" varchar(32),
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "name" varchar(255) NOT NULL,
  CONSTRAINT role_pkey PRIMARY KEY (role_uuid)
);

CREATE UNIQUE INDEX "auth_fence.role.role_psid.index" ON "auth_fence"."role" USING btree ("role_psid");

CREATE TABLE "auth_fence"."role_permission" (
  "role_uuid" uuid NOT NULL,
  "permission_uuid" uuid NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  CONSTRAINT "role_permission.pkey" PRIMARY KEY (role_uuid, permission_uuid),
  CONSTRAINT "role_permission>>role" FOREIGN KEY (role_uuid) REFERENCES "auth_fence"."role" (role_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT "role_permission>>permission" FOREIGN KEY (permission_uuid) REFERENCES "auth_fence"."permission" (permission_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);



CREATE TYPE auth_fence.identity_type AS ENUM ('User', 'Service');

CREATE TABLE "auth_fence"."identity" (
  "identity_uuid" uuid DEFAULT public.uuidv7() NOT NULL,
  "identity_type" auth_fence.identity_type NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "name" varchar(255) NOT NULL,
  "email" varchar(255),
  "note" text,
  "avatar_uri" varchar(4096),
  "active" boolean DEFAULT true NOT NULL,
  CONSTRAINT "identity.pkey" PRIMARY KEY (identity_uuid)
);

CREATE UNIQUE INDEX "auth_fence.identity.email.index" ON "auth_fence"."identity" USING btree (LOWER("email"));

CREATE TABLE "auth_fence"."login" (
  "identity_uuid" uuid NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "username" varchar(64) NOT NULL,
  "password_base64_pbkdf2_sha256_hash" varchar(256) NOT NULL,
  "password_salt" varchar(64) NOT NULL,
  "active" boolean DEFAULT true NOT NULL,
  CONSTRAINT "login.pkey" PRIMARY KEY (identity_uuid),
  CONSTRAINT "login>>identity" FOREIGN KEY (identity_uuid) REFERENCES "auth_fence"."identity" (identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

CREATE UNIQUE INDEX "auth_fence.login.username.index" ON "auth_fence"."login" USING btree (LOWER("username"));

CREATE TABLE "auth_fence"."identity_role" (
  "identity_uuid" uuid NOT NULL,
  "role_uuid" uuid NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  CONSTRAINT "identity_role.pkey" PRIMARY KEY (identity_uuid, role_uuid),
  CONSTRAINT "identity_role>>identity" FOREIGN KEY (identity_uuid) REFERENCES "auth_fence"."identity" (identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT "identity_role>>role" FOREIGN KEY (role_uuid) REFERENCES "auth_fence"."role" (role_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

CREATE TABLE "auth_fence"."identity_permission" (
  "identity_uuid" uuid NOT NULL,
  "permission_uuid" uuid NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  CONSTRAINT "identity_permission.pkey" PRIMARY KEY (identity_uuid, permission_uuid),
  CONSTRAINT "identity_permission>>identity" FOREIGN KEY (identity_uuid) REFERENCES "auth_fence"."identity" (identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT "identity_permission>>permission" FOREIGN KEY (permission_uuid) REFERENCES "auth_fence"."permission" (permission_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);


CREATE TABLE "auth_fence"."ssopro" (
  "ssopro_xsid" varchar(32) NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "name" varchar(255) NOT NULL,
  "description" text,
  "active" boolean DEFAULT true NOT NULL,
  CONSTRAINT ssopro_pkey PRIMARY KEY (ssopro_xsid)
);


CREATE TABLE "auth_fence"."identity_ssopro" (
  "identity_uuid" uuid NOT NULL,
  "ssopro_xsid" varchar(32) NOT NULL,
  "remote_identity_uuid" varchar(255) not null,
  "remote_identity_email" varchar(255) not null,
  "first_authentication_ts" timestamptz(6) null,
  "first_authentication_ip" inet null,
  "last_authentication_ts" timestamptz(6) null,
  "last_authentication_ip" inet null,
  "active" boolean DEFAULT true NOT NULL,
  "active_ts" timestamptz(6) null,
  CONSTRAINT "identity_sso_provider.pkey" PRIMARY KEY (identity_uuid, ssopro_xsid),
  CONSTRAINT "identity_sso_provider>>identity" FOREIGN KEY (identity_uuid) REFERENCES "auth_fence"."identity" (identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT "identity_sso_provider>>ssopro" FOREIGN KEY (ssopro_xsid) REFERENCES "auth_fence"."ssopro" (ssopro_xsid) ON DELETE RESTRICT ON UPDATE RESTRICT,
  UNIQUE (remote_identity_email, ssopro_xsid)
);


-- add identity_uuid column to auth_log and drop not null on auth_provider
ALTER TABLE auth_fence.auth_log
    ADD COLUMN identity_uuid UUID,
    ALTER COLUMN auth_provider DROP NOT NULL;


-- drop not nulls on first* columns
ALTER TABLE auth_fence.identity_ssopro
    ALTER COLUMN first_authentication_ts DROP NOT NULL,
    ALTER COLUMN first_authentication_ip DROP NOT NULL;



CREATE TABLE auth_fence.identity_key (
    identity_key_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    identity_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    create_note text,
    secret_sha256 varchar(64) NOT NULL,
    secret_salt varchar(32) NOT NULL,
    CONSTRAINT identity_key_pkey PRIMARY KEY (identity_key_uuid),
    CONSTRAINT "identity_key>>identity" FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity (identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);
