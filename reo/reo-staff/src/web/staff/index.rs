#[approck::http(GET /staff/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Staff Dashboard");
        doc.page_nav_enable_go_back("/admin/");

        let mut quick_info_box = bux::ui::quick_info_box::new();
        quick_info_box.icon_name_count("⚠️", "Needs Attention", 175);
        quick_info_box.icon_name_count("🟢", "Active", 105);
        quick_info_box.icon_name_count("🟡", "Pending", 20);

        let mut entity_picker = bux::component::entity_picker::new();
        entity_picker.set_heading("");
        entity_picker.append(
            "",
            "ABC Corp",
            "/packet/00000000-0000-0000-0000-000000000000/wages",
            "Warning: 7 days from deadline",
            "00000000-0000-0000-0000-000000000001",
        );

        entity_picker.append(
            "",
            "XYZ Innovations",
            "/packet/00000000-0000-0000-0000-000000000000/supplies",
            "Message from Jane: Is Surplus City a supplier?",
            "00000000-0000-0000-0000-000000000002",
        );

        entity_picker.append(
            "",
            "DEF Technologies",
            "/packet/00000000-0000-0000-0000-000000000000/subcontractors",
            "Information submitted, ready for review.",
            "00000000-0000-0000-0000-000000000003",
        );

        entity_picker.append(
            "",
            "LMN Corp",
            "/packet/00000000-0000-0000-0000-000000000000/supplies",
            "Rachelle: We really need to get the reciepts for the X8 misc expenses uploaded here.",
            "00000000-0000-0000-0000-000000000004",
        );

        entity_picker.append(
            "",
            "OPQ Innovations",
            "/packet/00000000-0000-0000-0000-000000000000/wages",
            "Warning: 7 days from deadline",
            "00000000-0000-0000-0000-000000000005",
        );

        doc.add_body(html!(
            section {
                grid-12 {
                    cell-4 {
                        panel {
                            header {
                                h5 { "R&D Tax Credit"}
                            }
                            (quick_info_box)
                        }
                    }
                    cell-8 {
                        panel {
                            header {
                                h5 { "Needs Attention" }
                            }
                            content {
                                (entity_picker)
                            }
                        }
                    }
                }
            }
        ));
        Response::HTML(doc.into())
    }
}
