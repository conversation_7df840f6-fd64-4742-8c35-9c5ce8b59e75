#[approck::http(GET /client/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.page_nav_enable_go_back("/dashboard/");
        doc.set_title("Select Client");

        let db_result = vec![
            (
                "💼",
                "AppCove, Inc.",
                "/client/********-0000-0000-0000-************/",
                "Business Account",
            ),
            (
                "👤",
                "Jason Garber",
                "/client/********-0000-0000-0000-************/",
                "Personal Account",
            ),
            (
                "👤",
                "<PERSON> Ann Garber",
                "/client/********-0000-0000-0000-************/",
                "Personal Account",
            ),
            (
                "💼",
                "Sinking Valley WoodWorks",
                "/client/********-0000-0000-0000-************/",
                "Business Account",
            ),
        ];

        let mut entity_picker = bux::component::entity_picker::new();
        let uuids: Vec<String> = (0..db_result.len())
            .map(|i| format!("client-{:03}", i + 1))
            .collect();
        for (i, (icon, name, href, role)) in db_result.iter().enumerate() {
            entity_picker.append(icon, name, href, role, &uuids[i]);
        }

        doc.add_body(html!(
            panel {
                content {
                    (entity_picker)
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
