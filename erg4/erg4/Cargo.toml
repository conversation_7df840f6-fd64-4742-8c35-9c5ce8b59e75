[package]
name = "erg4"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.acp]
app.port = 3001

extends = ["bux", "approck", "granite", "approck-ace"]

[dependencies]
approck = { workspace = true }
approck-ace = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
