#[path = "libλ.rs"]
pub mod libλ;

mod module;
mod web;

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub ace: approck_ace::ModuleConfig,
    pub webserver: approck::server::ModuleConfig,
}

pub struct AppStruct {
    pub ace: approck_ace::ModuleStruct,
    pub webserver: approck::server::Module,
}

#[derive(Debug)]
pub struct IdentityStruct {}

// It makes sense to define documents in the web dir, but they should be referred to from here.
pub use crate::web::Document::Document as DocumentStruct;

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;
    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            ace: approck_ace::ModuleStruct::new(config.ace)?,
            webserver: approck::server::Module::new(config.webserver)?,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        use approck::Module;
        self.ace.init().await?;
        self.webserver.init().await?;
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct {})
    }
}

impl approck::Identity for crate::IdentityStruct {}

pub trait App {}
pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}
pub trait Document: bux::document::Base {}
